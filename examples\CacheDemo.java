import com.dataforge.core.cache.CacheConfig;
import com.dataforge.core.cache.CacheManager;
import com.dataforge.core.cache.DataSourceCacheService;
import com.github.benmanes.caffeine.cache.stats.CacheStats;

import java.time.Duration;
import java.util.List;
import java.util.Map;

/**
 * 缓存功能演示
 * 
 * 展示DataForge缓存系统的各种功能，包括数据加载、缓存命中、
 * 性能统计和缓存管理等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class CacheDemo {

    public static void main(String[] args) {
        System.out.println("=== DataForge 缓存系统演示 ===\n");

        // 演示1: 基本缓存功能
        demonstrateBasicCaching();

        // 演示2: 数据源缓存服务
        demonstrateDataSourceCaching();

        // 演示3: 缓存性能测试
        demonstrateCachePerformance();

        // 演示4: 缓存配置和管理
        demonstrateCacheManagement();

        System.out.println("=== 演示完成 ===");
    }

    /**
     * 演示基本缓存功能
     */
    private static void demonstrateBasicCaching() {
        System.out.println("1. 基本缓存功能演示");
        System.out.println("--------------------");

        // 创建缓存管理器
        CacheConfig config = CacheConfig.defaultConfig()
                .setMaximumSize(100)
                .setExpireAfterWrite(Duration.ofMinutes(5))
                .setRecordStats(true);

        CacheManager cacheManager = new CacheManager(config);

        // 基本缓存操作
        cacheManager.put("demo", "key1", "value1");
        cacheManager.put("demo", "key2", "value2");
        cacheManager.put("demo", "key3", "value3");

        System.out.println("缓存大小: " + cacheManager.size("demo"));
        System.out.println("获取key1: " + cacheManager.get("demo", "key1", String.class));
        System.out.println("获取key2: " + cacheManager.get("demo", "key2", String.class));

        // 使用加载函数
        String loadedValue = cacheManager.get("demo", "key4", String.class, key -> "loaded_" + key);
        System.out.println("加载key4: " + loadedValue);

        // 显示统计信息
        CacheStats stats = cacheManager.getStats("demo");
        if (stats != null) {
            System.out.println("缓存统计:");
            System.out.println("  命中率: " + String.format("%.2f%%", stats.hitRate() * 100));
            System.out.println("  请求次数: " + stats.requestCount());
            System.out.println("  命中次数: " + stats.hitCount());
            System.out.println("  未命中次数: " + stats.missCount());
        }

        cacheManager.shutdown();
        System.out.println();
    }

    /**
     * 演示数据源缓存服务
     */
    private static void demonstrateDataSourceCaching() {
        System.out.println("2. 数据源缓存服务演示");
        System.out.println("----------------------");

        // 创建数据源缓存服务
        CacheManager cacheManager = new CacheManager(CacheConfig.largeConfig());
        DataSourceCacheService cacheService = new DataSourceCacheService(cacheManager);

        // 预热缓存
        System.out.println("开始预热缓存...");
        long startTime = System.currentTimeMillis();
        cacheService.warmUpAll();
        long endTime = System.currentTimeMillis();
        System.out.println("缓存预热完成，耗时: " + (endTime - startTime) + " ms");

        // 获取各种数据源
        List<String> surnames = cacheService.getChineseSurnames();
        List<String> maleNames = cacheService.getChineseMaleNames();
        List<String> femaleNames = cacheService.getChineseFemaleNames();
        List<String> areaCodes = cacheService.getAreaCodes();
        List<String> phonePrefixes = cacheService.getPhonePrefixes();
        List<String> emailDomains = cacheService.getEmailDomains();

        System.out.println("数据源统计:");
        System.out.println("  中文姓氏: " + surnames.size() + " 个");
        System.out.println("  男性名字: " + maleNames.size() + " 个");
        System.out.println("  女性名字: " + femaleNames.size() + " 个");
        System.out.println("  地区代码: " + areaCodes.size() + " 个");
        System.out.println("  手机前缀: " + phonePrefixes.size() + " 个");
        System.out.println("  邮箱域名: " + emailDomains.size() + " 个");

        // 显示示例数据
        System.out.println("\n示例数据:");
        System.out.println("  姓氏: " + surnames.subList(0, Math.min(10, surnames.size())));
        System.out.println("  地区代码: " + areaCodes.subList(0, Math.min(5, areaCodes.size())));
        System.out.println("  邮箱域名: " + emailDomains.subList(0, Math.min(5, emailDomains.size())));

        cacheManager.shutdown();
        System.out.println();
    }

    /**
     * 演示缓存性能测试
     */
    private static void demonstrateCachePerformance() {
        System.out.println("3. 缓存性能测试演示");
        System.out.println("--------------------");

        CacheManager cacheManager = new CacheManager(CacheConfig.largeConfig());
        DataSourceCacheService cacheService = new DataSourceCacheService(cacheManager);

        // 预热缓存
        cacheService.warmUpAll();

        // 性能测试：多次访问相同数据
        int iterations = 10000;
        
        System.out.println("开始性能测试，访问次数: " + iterations);

        // 测试缓存命中性能
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < iterations; i++) {
            cacheService.getChineseSurnames();
            cacheService.getAreaCodes();
            cacheService.getEmailDomains();
        }
        long endTime = System.currentTimeMillis();

        System.out.println("缓存访问完成，耗时: " + (endTime - startTime) + " ms");
        System.out.println("平均每次访问: " + String.format("%.3f", (endTime - startTime) / (double) iterations) + " ms");

        // 显示缓存统计
        Map<String, Object> stats = cacheService.getCacheStatistics();
        System.out.println("\n缓存统计信息:");
        stats.forEach((key, value) -> {
            if (key.endsWith("_hit_rate")) {
                System.out.println("  " + key + ": " + String.format("%.2f%%", ((Double) value) * 100));
            } else if (key.endsWith("_size")) {
                System.out.println("  " + key + ": " + value);
            }
        });

        cacheManager.shutdown();
        System.out.println();
    }

    /**
     * 演示缓存配置和管理
     */
    private static void demonstrateCacheManagement() {
        System.out.println("4. 缓存配置和管理演示");
        System.out.println("----------------------");

        // 创建不同配置的缓存
        CacheConfig smallConfig = CacheConfig.smallConfig();
        CacheConfig largeConfig = CacheConfig.largeConfig();
        CacheConfig longTermConfig = CacheConfig.longTermConfig();

        System.out.println("缓存配置:");
        System.out.println("  小型缓存: " + smallConfig);
        System.out.println("  大型缓存: " + largeConfig);
        System.out.println("  长期缓存: " + longTermConfig);

        CacheManager cacheManager = new CacheManager();

        // 创建不同类型的缓存
        cacheManager.getCache("small", smallConfig);
        cacheManager.getCache("large", largeConfig);
        cacheManager.getCache("longterm", longTermConfig);

        // 添加测试数据
        for (int i = 0; i < 50; i++) {
            cacheManager.put("small", "key" + i, "value" + i);
            cacheManager.put("large", "key" + i, "value" + i);
            cacheManager.put("longterm", "key" + i, "value" + i);
        }

        System.out.println("\n缓存大小:");
        System.out.println("  small: " + cacheManager.size("small"));
        System.out.println("  large: " + cacheManager.size("large"));
        System.out.println("  longterm: " + cacheManager.size("longterm"));

        // 缓存管理操作
        System.out.println("\n缓存管理操作:");
        System.out.println("  所有缓存名称: " + cacheManager.getCacheNames());
        
        // 清理和统计
        cacheManager.cleanup();
        Map<String, CacheStats> allStats = cacheManager.getAllStats();
        System.out.println("  统计信息数量: " + allStats.size());

        cacheManager.shutdown();
        System.out.println("  缓存管理器已关闭");
        System.out.println();
    }
}