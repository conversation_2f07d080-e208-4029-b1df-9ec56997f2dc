package com.dataforge.core.service;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.generator.GenerationException;
import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.relation.ConsistencyManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * DataForgeService单元测试
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@ExtendWith(MockitoExtension.class)
class DataForgeServiceTest {

    @Mock
    private GeneratorFactory generatorFactory;

    @Mock
    private DataGenerator<Object> mockGenerator;

    private DataForgeService dataForgeService;

    @BeforeEach
    void setUp() {
        DataRelationManager relationManager = new DataRelationManager();
        ConsistencyManager consistencyManager = new ConsistencyManager();
        dataForgeService = new DataForgeService(generatorFactory, relationManager, consistencyManager);
    }

    @Test
    void testGenerateDataSuccess() throws GenerationException {
        // 准备测试数据
        GenerationConfig config = new GenerationConfig();
        config.setDataType("test");
        config.setCount(5);
        config.setOutputConfig(new OutputConfig());
        config.setValidationConfig(new ValidationConfig());

        // 模拟生成器行为
        when(generatorFactory.getGenerator("test")).thenReturn(mockGenerator);
        when(mockGenerator.generate(any(GenerationContext.class))).thenReturn("test-data");
        when(mockGenerator.validate(any())).thenReturn(true);

        // 执行测试
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);

        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.getSuccessCount());
        assertEquals(0, result.getFailureCount());
        assertEquals(0, result.getValidationErrors());

        // 验证交互
        verify(generatorFactory).getGenerator("test");
        verify(mockGenerator, times(5)).generate(any(GenerationContext.class));
    }

    @Test
    void testGenerateDataWithNullDataType() {
        // 准备测试数据
        GenerationConfig config = new GenerationConfig();
        config.setDataType(null);
        config.setCount(1);
        config.setOutputConfig(new OutputConfig());
        config.setValidationConfig(new ValidationConfig());

        // 执行测试
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getSuccessCount());
        assertEquals(1, result.getFailureCount());
        assertTrue(result.getWarnings().size() > 0);
    }

    @Test
    void testGenerateDataWithGeneratorNotFound() throws GenerationException {
        // 准备测试数据
        GenerationConfig config = new GenerationConfig();
        config.setDataType("nonexistent");
        config.setCount(1);
        config.setOutputConfig(new OutputConfig());
        config.setValidationConfig(new ValidationConfig());

        // 模拟生成器不存在
        when(generatorFactory.getGenerator("nonexistent")).thenReturn(null);

        // 执行测试
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);

        // 验证结果
        assertNotNull(result);
        assertEquals(0, result.getSuccessCount());
        assertEquals(1, result.getFailureCount());
        assertTrue(result.getWarnings().size() > 0);
    }
}