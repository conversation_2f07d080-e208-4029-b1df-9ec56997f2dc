package com.dataforge.core.relation;

import org.junit.jupiter.api.Test;

import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * FieldRelation 单元测试
 */
class FieldRelationTest {

    @Test
    void testFieldRelationCreation() {
        FieldRelation relation = FieldRelation.builder()
                .source("test_source")
                .targets(Set.of("target1", "target2"))
                .type(RelationType.ONE_TO_MANY)
                .function(value -> RelationResult.success("target1", "result"))
                .description("测试关联")
                .build();

        assertEquals("test_source", relation.getSourceField());
        assertEquals(Set.of("target1", "target2"), relation.getTargetFields());
        assertEquals(RelationType.ONE_TO_MANY, relation.getRelationType());
        assertEquals("测试关联", relation.getDescription());
    }

    @Test
    void testFieldRelationExecution() {
        FieldRelation relation = FieldRelation.builder()
                .source("input")
                .targets(Set.of("output"))
                .type(RelationType.ONE_TO_ONE)
                .function(value -> RelationResult.success("output", value.toString().toUpperCase()))
                .description("转换为大写")
                .build();

        RelationResult result = relation.execute("hello");

        assertTrue(result.isSuccess());
        assertEquals("HELLO", result.getValue("output"));
    }

    @Test
    void testFieldRelationExecutionFailure() {
        FieldRelation relation = FieldRelation.builder()
                .source("input")
                .targets(Set.of("output"))
                .type(RelationType.ONE_TO_ONE)
                .function(value -> {
                    throw new RuntimeException("测试异常");
                })
                .description("会失败的关联")
                .build();

        RelationResult result = relation.execute("test");

        assertTrue(result.isFailure());
        assertNotNull(result.getErrorMessage());
        assertTrue(result.getErrorMessage().contains("执行关联规则失败"));
    }

    @Test
    void testAffectsMethod() {
        FieldRelation relation = FieldRelation.builder()
                .source("source")
                .targets(Set.of("target1", "target2"))
                .type(RelationType.ONE_TO_MANY)
                .function(value -> RelationResult.success())
                .build();

        assertTrue(relation.affects("target1"));
        assertTrue(relation.affects("target2"));
        assertFalse(relation.affects("target3"));
    }

    @Test
    void testEqualsAndHashCode() {
        FieldRelation relation1 = FieldRelation.builder()
                .source("source")
                .targets(Set.of("target"))
                .type(RelationType.ONE_TO_ONE)
                .function(value -> RelationResult.success())
                .build();

        FieldRelation relation2 = FieldRelation.builder()
                .source("source")
                .targets(Set.of("target"))
                .type(RelationType.ONE_TO_ONE)
                .function(value -> RelationResult.success())
                .build();

        assertEquals(relation1, relation2);
        assertEquals(relation1.hashCode(), relation2.hashCode());
    }

    @Test
    void testBuilderValidation() {
        assertThrows(NullPointerException.class, () -> {
            FieldRelation.builder()
                    .source(null)
                    .targets(Set.of("target"))
                    .type(RelationType.ONE_TO_ONE)
                    .function(value -> RelationResult.success())
                    .build();
        });

        assertThrows(NullPointerException.class, () -> {
            FieldRelation.builder()
                    .source("source")
                    .targets(null)
                    .type(RelationType.ONE_TO_ONE)
                    .function(value -> RelationResult.success())
                    .build();
        });

        assertThrows(NullPointerException.class, () -> {
            FieldRelation.builder()
                    .source("source")
                    .targets(Set.of("target"))
                    .type(null)
                    .function(value -> RelationResult.success())
                    .build();
        });

        assertThrows(NullPointerException.class, () -> {
            FieldRelation.builder()
                    .source("source")
                    .targets(Set.of("target"))
                    .type(RelationType.ONE_TO_ONE)
                    .function(null)
                    .build();
        });
    }
}