package com.dataforge.generators.text;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static org.assertj.core.api.Assertions.*;

/**
 * 手机号生成器单元测试
 */
@DisplayName("手机号生成器测试")
class PhoneGeneratorTest {

    private PhoneGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new PhoneGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext(parameters, 12345L);
    }

    @Test
    @DisplayName("基本手机号生成测试")
    void testBasicGeneration() {
        String phone = generator.generate(context);

        assertThat(phone).isNotNull();
        assertThat(phone).hasSize(11);
        assertThat(phone).matches("1[3-9]\\d{9}");
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证格式一致性")
    void testRepeatedGeneration() {
        String phone = generator.generate(context);

        assertThat(phone).isNotNull();
        assertThat(phone).hasSize(11);
        assertThat(phone).startsWith("1");
        assertThat(generator.validate(phone)).isTrue();
    }

    @Test
    @DisplayName("指定运营商生成测试")
    void testGenerationWithCarrier() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("carrier", "CHINA_MOBILE");
        GenerationContext contextWithCarrier = new GenerationContext(parameters, 12345L);

        String phone = generator.generate(contextWithCarrier);

        assertThat(phone).isNotNull();
        assertThat(phone).hasSize(11);
        // 中国移动号段：134-139, 147, 150-152, 157-159, 178, 182-184, 187-188, 198
        assertThat(phone).matches("1(3[4-9]|47|5[0-2]|5[7-9]|78|8[2-4]|8[7-8]|98)\\d{8}");
        assertThat(generator.validate(phone)).isTrue();
    }

    @Test
    @DisplayName("指定地区生成测试")
    void testGenerationWithRegion() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("region", "BEIJING");
        GenerationContext contextWithRegion = new GenerationContext(parameters, 12345L);

        String phone = generator.generate(contextWithRegion);

        assertThat(phone).isNotNull();
        assertThat(phone).hasSize(11);
        assertThat(generator.validate(phone)).isTrue();
    }

    @Test
    @DisplayName("手机号校验测试 - 有效号码")
    void testValidationWithValidPhone() {
        String[] validPhones = {
                "13800138000", // 中国移动
                "15900159000", // 中国移动
                "18600186000", // 中国联通
                "17700177000", // 中国电信
                "19900199000" // 虚拟运营商
        };

        for (String phone : validPhones) {
            assertThat(generator.validate(phone)).isTrue();
        }
    }

    @Test
    @DisplayName("手机号校验测试 - 无效号码")
    void testValidationWithInvalidPhone() {
        // 测试长度不正确的手机号
        assertThat(generator.validate("1380013800")).isFalse();
        assertThat(generator.validate("138001380000")).isFalse();

        // 测试不以1开头的号码
        assertThat(generator.validate("23800138000")).isFalse();

        // 测试第二位数字无效的号码
        assertThat(generator.validate("12800138000")).isFalse();
        assertThat(generator.validate("10800138000")).isFalse();

        // 测试包含非数字字符的号码
        assertThat(generator.validate("1380013800A")).isFalse();

        // 测试空值
        assertThat(generator.validate(null)).isFalse();
        assertThat(generator.validate("")).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("phone");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        List<GeneratorParameter> parameters = generator.getSupportedParameters();

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("carrier", "region");
    }

    @Test
    @DisplayName("生成数据格式一致性测试")
    void testGeneratedDataConsistency() {
        Pattern phonePattern = Pattern.compile("1[3-9]\\d{9}");

        for (int i = 0; i < 100; i++) {
            String phone = generator.generate(context);
            assertThat(phone).matches(phonePattern);
            assertThat(generator.validate(phone)).isTrue();
        }
    }

    @Test
    @DisplayName("不同运营商号段测试")
    void testDifferentCarriers() {
        String[] carriers = { "CHINA_MOBILE", "CHINA_UNICOM", "CHINA_TELECOM" };

        for (String carrier : carriers) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("carrier", carrier);
            GenerationContext carrierContext = new GenerationContext(parameters, 12345L);

            String phone = generator.generate(carrierContext);
            assertThat(phone).isNotNull();
            assertThat(phone).hasSize(11);
            assertThat(generator.validate(phone)).isTrue();
        }
    }

    @Test
    @DisplayName("号段分布测试")
    void testNumberSegmentDistribution() {
        Map<String, Integer> segmentCount = new HashMap<>();

        for (int i = 0; i < 1000; i++) {
            String phone = generator.generate(context);
            String segment = phone.substring(0, 3);
            segmentCount.put(segment, segmentCount.getOrDefault(segment, 0) + 1);
        }

        // 验证生成了多种不同的号段
        assertThat(segmentCount.size()).isGreaterThan(5);

        // 验证所有号段都是有效的
        for (String segment : segmentCount.keySet()) {
            assertThat(segment).matches("1[3-9]\\d");
        }
    }

    @Test
    @DisplayName("特殊号段测试")
    void testSpecialSegments() {
        // 测试虚拟运营商号段
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("includeVirtual", true);
        GenerationContext virtualContext = new GenerationContext(parameters, 12345L);

        String phone = generator.generate(virtualContext);
        assertThat(phone).isNotNull();
        assertThat(generator.validate(phone)).isTrue();
    }

    @Test
    @DisplayName("格式化输出测试")
    void testFormattedOutput() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("format", "xxx-xxxx-xxxx");
        GenerationContext formatContext = new GenerationContext(parameters, 12345L);

        String phone = generator.generate(formatContext);

        if (phone.contains("-")) {
            assertThat(phone).matches("\\d{3}-\\d{4}-\\d{4}");
        } else {
            // 如果不支持格式化，应该返回标准格式
            assertThat(phone).matches("1[3-9]\\d{9}");
        }
    }

    @Test
    @DisplayName("边界值测试")
    void testBoundaryValues() {
        // 测试最小有效号段
        String[] minSegments = { "130", "131", "132" };

        for (String segment : minSegments) {
            // 这里主要验证生成的号码中包含这些号段
            boolean found = false;
            for (int i = 0; i < 100; i++) {
                String phone = generator.generate(context);
                if (phone.startsWith(segment)) {
                    found = true;
                    break;
                }
            }
            // 注意：由于是随机生成，不能保证一定生成特定号段，这里只是示例
        }
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("carrier", "INVALID_CARRIER");
        parameters.put("region", "INVALID_REGION");
        GenerationContext invalidContext = new GenerationContext(parameters, 12345L);

        // 应该能够处理无效参数并生成有效的手机号
        String phone = generator.generate(invalidContext);
        assertThat(phone).isNotNull();
        assertThat(phone).hasSize(11);
        assertThat(generator.validate(phone)).isTrue();
    }

    @Test
    @DisplayName("性能测试")
    void testPerformance() {
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 10000; i++) {
            String phone = generator.generate(context);
            assertThat(phone).isNotNull();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertThat(duration).isLessThan(2000); // 应该在2秒内完成
    }

    @Test
    @DisplayName("唯一性测试")
    void testUniqueness() {
        // 虽然手机号可能重复，但在小样本中应该有一定的唯一性
        java.util.Set<String> phones = new java.util.HashSet<>();

        for (int i = 0; i < 1000; i++) {
            String phone = generator.generate(context);
            phones.add(phone);
        }

        // 期望至少有90%的唯一性
        assertThat(phones.size()).isGreaterThan(900);
    }
}