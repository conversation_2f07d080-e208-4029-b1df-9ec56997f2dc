package com.dataforge.core.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 输出配置
 * 
 * 定义数据输出的相关配置，包括输出格式、目标位置、格式选项等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class OutputConfig {

    /**
     * 输出格式枚举
     */
    public enum Format {
        CSV("csv"),
        JSON("json"),
        XML("xml"),
        SQL("sql"),
        CONSOLE("console");

        private final String value;

        Format(String value) {
            this.value = value;
        }

        public String getValue() {
            return value;
        }

        public static Format fromString(String value) {
            for (Format format : Format.values()) {
                if (format.value.equalsIgnoreCase(value)) {
                    return format;
                }
            }
            throw new IllegalArgumentException("不支持的输出格式: " + value);
        }
    }

    @JsonProperty("format")
    private String format = Format.CSV.getValue();

    @JsonProperty("target")
    private String target = "stdout";

    @JsonProperty("file")
    private String file;

    @JsonProperty("pretty")
    private boolean pretty = false;

    @JsonProperty("encoding")
    private String encoding = "UTF-8";

    @JsonProperty("options")
    private Map<String, Object> options = new HashMap<>();

    /**
     * 默认构造函数
     */
    public OutputConfig() {
    }

    /**
     * 构造函数
     * 
     * @param format 输出格式
     * @param target 输出目标
     */
    public OutputConfig(String format, String target) {
        this.format = format;
        this.target = target;
    }

    // Getters and Setters

    public String getFormat() {
        return format;
    }

    public void setFormat(String format) {
        this.format = format;
    }

    public Format getFormatEnum() {
        return Format.fromString(format);
    }

    public void setFormatEnum(Format format) {
        this.format = format.getValue();
    }

    public String getTarget() {
        return target;
    }

    public void setTarget(String target) {
        this.target = target;
    }

    public String getFile() {
        return file;
    }

    public void setFile(String file) {
        this.file = file;
        if (file != null && !file.trim().isEmpty()) {
            this.target = "file";
        }
    }

    public boolean isPretty() {
        return pretty;
    }

    public void setPretty(boolean pretty) {
        this.pretty = pretty;
    }

    public String getEncoding() {
        return encoding;
    }

    public void setEncoding(String encoding) {
        this.encoding = encoding != null ? encoding : "UTF-8";
    }

    /**
     * 检查是否包含头部
     * 
     * @return 是否包含头部
     */
    public boolean isIncludeHeader() {
        Boolean value = getOption("includeHeader", Boolean.class);
        return value != null ? value : true;
    }

    /**
     * 设置是否包含头部
     * 
     * @param includeHeader 是否包含头部
     */
    public void setIncludeHeader(boolean includeHeader) {
        addOption("includeHeader", includeHeader);
    }

    /**
     * 检查是否美化输出
     * 
     * @return 是否美化输出
     */
    public boolean isPrettyPrint() {
        Boolean value = getOption("prettyPrint", Boolean.class);
        return value != null ? value : pretty;
    }

    /**
     * 设置是否美化输出
     * 
     * @param prettyPrint 是否美化输出
     */
    public void setPrettyPrint(boolean prettyPrint) {
        addOption("prettyPrint", prettyPrint);
    }

    /**
     * 获取文件路径
     * 
     * @return 文件路径
     */
    public String getFilePath() {
        return file;
    }

    /**
     * 设置文件路径
     * 
     * @param filePath 文件路径
     */
    public void setFilePath(String filePath) {
        this.file = filePath;
    }

    /**
     * 设置根元素（XML格式）
     * 
     * @param rootElement 根元素名称
     */
    public void setRootElement(String rootElement) {
        addOption("rootElement", rootElement);
    }

    /**
     * 设置记录元素（XML格式）
     * 
     * @param recordElement 记录元素名称
     */
    public void setRecordElement(String recordElement) {
        addOption("recordElement", recordElement);
    }

    /**
     * 设置工作表名称（Excel格式）
     * 
     * @param sheetName 工作表名称
     */
    public void setSheetName(String sheetName) {
        addOption("sheetName", sheetName);
    }

    /**
     * 设置表名（SQL格式）
     * 
     * @param tableName 表名
     */
    public void setTableName(String tableName) {
        addOption("tableName", tableName);
    }

    /**
     * 设置是否包含创建表语句
     * 
     * @param includeCreateTable 是否包含创建表语句
     */
    public void setIncludeCreateTable(boolean includeCreateTable) {
        addOption("includeCreateTable", includeCreateTable);
    }

    /**
     * 设置表格格式
     * 
     * @param tableFormat 是否使用表格格式
     */
    public void setTableFormat(boolean tableFormat) {
        addOption("tableFormat", tableFormat);
    }

    /**
     * 设置分隔符
     * 
     * @param delimiter 分隔符
     */
    public void setDelimiter(String delimiter) {
        addOption("delimiter", delimiter);
    }

    /**
     * 设置是否启用压缩
     * 
     * @param compressionEnabled 是否启用压缩
     */
    public void setCompressionEnabled(boolean compressionEnabled) {
        addOption("compressionEnabled", compressionEnabled);
    }

    /**
     * 设置压缩类型
     * 
     * @param compressionType 压缩类型
     */
    public void setCompressionType(String compressionType) {
        addOption("compressionType", compressionType);
    }

    /**
     * 设置是否启用流式处理
     * 
     * @param streamingEnabled 是否启用流式处理
     */
    public void setStreamingEnabled(boolean streamingEnabled) {
        addOption("streamingEnabled", streamingEnabled);
    }

    /**
     * 设置批处理大小
     * 
     * @param batchSize 批处理大小
     */
    public void setBatchSize(int batchSize) {
        addOption("batchSize", batchSize);
    }

    public Map<String, Object> getOptions() {
        return options;
    }

    public void setOptions(Map<String, Object> options) {
        this.options = options != null ? options : new HashMap<>();
    }

    /**
     * 添加格式选项
     * 
     * @param key   选项键
     * @param value 选项值
     */
    public void addOption(String key, Object value) {
        if (key != null && value != null) {
            this.options.put(key, value);
        }
    }

    /**
     * 获取格式选项
     * 
     * @param key 选项键
     * @return 选项值，如果不存在返回null
     */
    public Object getOption(String key) {
        return options.get(key);
    }

    /**
     * 获取格式选项并转换为指定类型
     * 
     * @param key  选项键
     * @param type 目标类型
     * @param <T>  类型参数
     * @return 转换后的选项值，如果不存在或转换失败返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getOption(String key, Class<T> type) {
        Object value = options.get(key);
        if (value == null) {
            return null;
        }

        if (type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }

        return null;
    }

    /**
     * 检查是否输出到文件
     * 
     * @return 如果输出到文件返回true，否则返回false
     */
    public boolean isFileOutput() {
        return "file".equals(target) || (file != null && !file.trim().isEmpty());
    }

    /**
     * 检查是否输出到控制台
     * 
     * @return 如果输出到控制台返回true，否则返回false
     */
    public boolean isConsoleOutput() {
        return "stdout".equals(target) || "console".equals(target);
    }

    /**
     * 获取实际的输出文件路径
     * 
     * @return 输出文件路径，如果不是文件输出返回null
     */
    public String getActualFilePath() {
        if (!isFileOutput()) {
            return null;
        }

        if (file != null && !file.trim().isEmpty()) {
            return file;
        }

        // 如果没有指定文件名，根据格式生成默认文件名
        String extension = getDefaultExtension();
        return "dataforge_output." + extension;
    }

    /**
     * 获取默认文件扩展名
     * 
     * @return 文件扩展名
     */
    public String getDefaultExtension() {
        try {
            Format formatEnum = getFormatEnum();
            switch (formatEnum) {
                case CSV:
                    return "csv";
                case JSON:
                    return "json";
                case XML:
                    return "xml";
                case SQL:
                    return "sql";
                default:
                    return "txt";
            }
        } catch (IllegalArgumentException e) {
            return "txt";
        }
    }

    /**
     * 验证输出配置的有效性
     * 
     * @return 验证结果
     */
    public ValidationResult validate() {
        ValidationResult result = new ValidationResult();

        if (format == null || format.trim().isEmpty()) {
            result.addError("输出格式不能为空");
        } else {
            try {
                Format.fromString(format);
            } catch (IllegalArgumentException e) {
                result.addError("不支持的输出格式: " + format);
            }
        }

        if (target == null || target.trim().isEmpty()) {
            result.addError("输出目标不能为空");
        }

        if (isFileOutput() && (file == null || file.trim().isEmpty())) {
            // 文件输出但未指定文件名，使用默认文件名（这是允许的）
        }

        if (encoding == null || encoding.trim().isEmpty()) {
            result.addError("编码格式不能为空");
        }

        return result;
    }

    /**
     * 创建输出配置的副本
     * 
     * @return 输出配置副本
     */
    public OutputConfig copy() {
        OutputConfig copy = new OutputConfig();
        copy.format = this.format;
        copy.target = this.target;
        copy.file = this.file;
        copy.pretty = this.pretty;
        copy.encoding = this.encoding;
        copy.options = new HashMap<>(this.options);
        return copy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        OutputConfig that = (OutputConfig) o;
        return pretty == that.pretty &&
                Objects.equals(format, that.format) &&
                Objects.equals(target, that.target) &&
                Objects.equals(file, that.file) &&
                Objects.equals(encoding, that.encoding) &&
                Objects.equals(options, that.options);
    }

    @Override
    public int hashCode() {
        return Objects.hash(format, target, file, pretty, encoding, options);
    }

    @Override
    public String toString() {
        return String.format("OutputConfig{format='%s', target='%s', file='%s', pretty=%s}",
                format, target, file, pretty);
    }
}