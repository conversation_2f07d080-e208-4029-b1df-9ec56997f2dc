package com.dataforge.core.cache;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;

/**
 * 数据源缓存服务
 * 
 * 专门用于缓存生成器使用的各种数据源，如姓氏库、名字库、地区代码等。
 * 提供预热、懒加载和自动刷新功能。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class DataSourceCacheService {

    private static final Logger logger = LoggerFactory.getLogger(DataSourceCacheService.class);

    // 缓存名称常量
    public static final String CHINESE_SURNAMES_CACHE = "chinese_surnames";
    public static final String CHINESE_MALE_NAMES_CACHE = "chinese_male_names";
    public static final String CHINESE_FEMALE_NAMES_CACHE = "chinese_female_names";
    public static final String AREA_CODES_CACHE = "area_codes";
    public static final String PHONE_PREFIXES_CACHE = "phone_prefixes";
    public static final String EMAIL_DOMAINS_CACHE = "email_domains";
    public static final String BANK_BIN_CODES_CACHE = "bank_bin_codes";

    private final CacheManager cacheManager;
    private final Map<String, String> resourcePaths = new ConcurrentHashMap<>();

    /**
     * 构造函数
     * 
     * @param cacheManager 缓存管理器
     */
    public DataSourceCacheService(CacheManager cacheManager) {
        this.cacheManager = cacheManager;
        initializeResourcePaths();
    }

    /**
     * 初始化资源路径映射
     */
    private void initializeResourcePaths() {
        resourcePaths.put(CHINESE_SURNAMES_CACHE, "/names/chinese_surnames.txt");
        resourcePaths.put(CHINESE_MALE_NAMES_CACHE, "/names/chinese_male_names.txt");
        resourcePaths.put(CHINESE_FEMALE_NAMES_CACHE, "/names/chinese_female_names.txt");
        resourcePaths.put(AREA_CODES_CACHE, "/codes/area_codes.txt");
        resourcePaths.put(PHONE_PREFIXES_CACHE, "/codes/phone_prefixes.txt");
        resourcePaths.put(EMAIL_DOMAINS_CACHE, "/domains/email_domains.txt");
        resourcePaths.put(BANK_BIN_CODES_CACHE, "/codes/bank_bin_codes.txt");
    }

    /**
     * 获取中文姓氏列表
     * 
     * @return 中文姓氏列表
     */
    public List<String> getChineseSurnames() {
        return getStringList(CHINESE_SURNAMES_CACHE);
    }

    /**
     * 获取中文男性名字列表
     * 
     * @return 中文男性名字列表
     */
    public List<String> getChineseMaleNames() {
        return getStringList(CHINESE_MALE_NAMES_CACHE);
    }

    /**
     * 获取中文女性名字列表
     * 
     * @return 中文女性名字列表
     */
    public List<String> getChineseFemaleNames() {
        return getStringList(CHINESE_FEMALE_NAMES_CACHE);
    }

    /**
     * 获取地区代码列表
     * 
     * @return 地区代码列表
     */
    public List<String> getAreaCodes() {
        return getStringList(AREA_CODES_CACHE);
    }

    /**
     * 获取手机号前缀列表
     * 
     * @return 手机号前缀列表
     */
    public List<String> getPhonePrefixes() {
        return getStringList(PHONE_PREFIXES_CACHE);
    }

    /**
     * 获取邮箱域名列表
     * 
     * @return 邮箱域名列表
     */
    public List<String> getEmailDomains() {
        return getStringList(EMAIL_DOMAINS_CACHE);
    }

    /**
     * 获取银行BIN码列表
     * 
     * @return 银行BIN码列表
     */
    public List<String> getBankBinCodes() {
        return getStringList(BANK_BIN_CODES_CACHE);
    }

    /**
     * 获取字符串列表（通用方法）
     * 
     * @param cacheName 缓存名称
     * @return 字符串列表
     */
    @SuppressWarnings("unchecked")
    public List<String> getStringList(String cacheName) {
        return cacheManager.get(cacheName, "data", List.class, key -> loadStringListFromResource(cacheName));
    }

    /**
     * 从资源文件加载字符串列表
     * 
     * @param cacheName 缓存名称
     * @return 字符串列表
     */
    private List<String> loadStringListFromResource(String cacheName) {
        String resourcePath = resourcePaths.get(cacheName);
        if (resourcePath == null) {
            logger.warn("No resource path configured for cache: {}", cacheName);
            return List.of();
        }

        try (InputStream inputStream = getClass().getResourceAsStream(resourcePath)) {
            if (inputStream == null) {
                logger.warn("Resource not found: {}", resourcePath);
                return createDefaultData(cacheName);
            }

            List<String> data = new BufferedReader(new InputStreamReader(inputStream, StandardCharsets.UTF_8))
                    .lines()
                    .map(String::trim)
                    .filter(line -> !line.isEmpty() && !line.startsWith("#"))
                    .collect(Collectors.toList());

            logger.info("Loaded {} items from resource: {}", data.size(), resourcePath);
            return data;

        } catch (IOException e) {
            logger.error("Failed to load resource: {}", resourcePath, e);
            return createDefaultData(cacheName);
        }
    }

    /**
     * 创建默认数据（当资源文件不存在时）
     * 
     * @param cacheName 缓存名称
     * @return 默认数据列表
     */
    private List<String> createDefaultData(String cacheName) {
        return switch (cacheName) {
            case CHINESE_SURNAMES_CACHE -> List.of("张", "王", "李", "赵", "刘", "陈", "杨", "黄", "周", "吴");
            case CHINESE_MALE_NAMES_CACHE -> List.of("伟", "强", "磊", "军", "勇", "涛", "明", "超", "辉", "华");
            case CHINESE_FEMALE_NAMES_CACHE -> List.of("丽", "娜", "敏", "静", "秀", "美", "艳", "莉", "红", "霞");
            case AREA_CODES_CACHE -> List.of("110101", "110102", "310101", "310104", "440101", "440103");
            case PHONE_PREFIXES_CACHE -> List.of("130", "131", "132", "133", "134", "135", "136", "137", "138", "139");
            case EMAIL_DOMAINS_CACHE -> List.of("gmail.com", "163.com", "qq.com", "sina.com", "hotmail.com");
            case BANK_BIN_CODES_CACHE -> List.of("622202", "622700", "622848", "621661", "621663");
            default -> List.of();
        };
    }

    /**
     * 预热所有缓存
     */
    public void warmUpAll() {
        logger.info("Starting cache warm-up for all data sources");

        long startTime = System.currentTimeMillis();

        // 并行预热所有缓存
        resourcePaths.keySet().parallelStream().forEach(cacheName -> {
            try {
                getStringList(cacheName);
                logger.debug("Warmed up cache: {}", cacheName);
            } catch (Exception e) {
                logger.error("Failed to warm up cache: {}", cacheName, e);
            }
        });

        long endTime = System.currentTimeMillis();
        logger.info("Cache warm-up completed in {} ms", endTime - startTime);
    }

    /**
     * 预热指定缓存
     * 
     * @param cacheNames 缓存名称列表
     */
    public void warmUp(String... cacheNames) {
        for (String cacheName : cacheNames) {
            try {
                getStringList(cacheName);
                logger.info("Warmed up cache: {}", cacheName);
            } catch (Exception e) {
                logger.error("Failed to warm up cache: {}", cacheName, e);
            }
        }
    }

    /**
     * 刷新指定缓存
     * 
     * @param cacheName 缓存名称
     */
    public void refresh(String cacheName) {
        cacheManager.remove(cacheName, "data");
        getStringList(cacheName);
        logger.info("Refreshed cache: {}", cacheName);
    }

    /**
     * 刷新所有缓存
     */
    public void refreshAll() {
        resourcePaths.keySet().forEach(this::refresh);
        logger.info("Refreshed all data source caches");
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存统计信息
     */
    public Map<String, Object> getCacheStatistics() {
        Map<String, Object> stats = new ConcurrentHashMap<>();

        resourcePaths.keySet().forEach(cacheName -> {
            long size = cacheManager.size(cacheName);
            stats.put(cacheName + "_size", size);

            var cacheStats = cacheManager.getStats(cacheName);
            if (cacheStats != null) {
                stats.put(cacheName + "_hit_rate", cacheStats.hitRate());
                stats.put(cacheName + "_miss_count", cacheStats.missCount());
                stats.put(cacheName + "_load_count", cacheStats.loadCount());
            }
        });

        return stats;
    }

    /**
     * 清理所有缓存
     */
    public void clearAll() {
        resourcePaths.keySet().forEach(cacheManager::clear);
        logger.info("Cleared all data source caches");
    }
}