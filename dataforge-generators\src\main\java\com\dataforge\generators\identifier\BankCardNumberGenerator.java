package com.dataforge.generators.identifier;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 银行卡号生成器
 * 
 * 生成符合Luhn算法的有效银行卡号。
 * 支持多种银行和卡组织的BIN码，确保生成的卡号格式正确且校验位有效。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class BankCardNumberGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "bankcard";
    private static final String DESCRIPTION = "生成银行卡号";

    // 银行卡号验证正则表达式
    private static final Pattern BANK_CARD_PATTERN = Pattern.compile("^\\d{13,19}$");

    // 银行BIN码配置
    private static final Map<String, BankInfo> BANK_BIN_MAP = new HashMap<>();

    static {
        // 中国工商银行
        BANK_BIN_MAP.put("ICBC", new BankInfo("中国工商银行",
                Arrays.asList("622202", "622208", "622210", "622211", "622212"), 19));

        // 中国建设银行
        BANK_BIN_MAP.put("CCB", new BankInfo("中国建设银行",
                Arrays.asList("436742", "622280", "622700", "622708", "622709"), 19));

        // 中国农业银行
        BANK_BIN_MAP.put("ABC", new BankInfo("中国农业银行",
                Arrays.asList("622848", "622849", "622850", "622851", "622852"), 19));

        // 中国银行
        BANK_BIN_MAP.put("BOC", new BankInfo("中国银行",
                Arrays.asList("456351", "601382", "621661", "621662", "621663"), 19));

        // 交通银行
        BANK_BIN_MAP.put("BOCOM", new BankInfo("交通银行",
                Arrays.asList("622258", "622259", "622260", "622261", "622262"), 19));

        // 招商银行
        BANK_BIN_MAP.put("CMB", new BankInfo("招商银行",
                Arrays.asList("621483", "621485", "621486", "621487", "621488"), 16));

        // 中信银行
        BANK_BIN_MAP.put("CITIC", new BankInfo("中信银行",
                Arrays.asList("622690", "622691", "622692", "622696", "622698"), 19));

        // 光大银行
        BANK_BIN_MAP.put("CEB", new BankInfo("光大银行",
                Arrays.asList("622655", "622650", "622658", "622659", "622660"), 19));

        // 华夏银行
        BANK_BIN_MAP.put("HXB", new BankInfo("华夏银行",
                Arrays.asList("622630", "622631", "622632", "622633", "622637"), 19));

        // 民生银行
        BANK_BIN_MAP.put("CMBC", new BankInfo("民生银行",
                Arrays.asList("622600", "622601", "622602", "622603", "415599"), 19));
    }

    private final Random random;

    /**
     * 构造函数
     */
    public BankCardNumberGenerator() {
        this.random = new Random();
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("bank", String.class, "random",
                "银行代码：ICBC、CCB、ABC、BOC、BOCOM、CMB、CITIC、CEB、HXB、CMBC，或使用random随机选择", false));
        addParameter(new GeneratorParameter("binCode", String.class, null,
                "指定BIN码（前6位），设置后忽略bank参数", false));
        addParameter(new GeneratorParameter("cardLength", Integer.class, null,
                "卡号长度，设置后忽略银行默认长度", false));
        addParameter(new GeneratorParameter("format", String.class, "plain",
                "输出格式：plain（纯数字）、space（每4位空格分隔）", false));
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        String bank = context.getParameter("bank", "random");
        String binCode = context.getParameter("binCode", null);
        Integer cardLength = context.getParameter("cardLength", null);
        String format = context.getParameter("format", "plain");

        // 选择银行和BIN码
        BankInfo bankInfo;
        String selectedBinCode;

        if (binCode != null) {
            // 使用指定的BIN码
            if (!binCode.matches("\\d{6}")) {
                throw new IllegalArgumentException("BIN码必须是6位数字");
            }
            selectedBinCode = binCode;
            bankInfo = new BankInfo("自定义银行", Arrays.asList(binCode), cardLength != null ? cardLength : 16);
        } else {
            // 选择银行
            bankInfo = selectBank(bank);
            selectedBinCode = selectBinCode(bankInfo);
        }

        // 确定卡号长度
        int finalLength = cardLength != null ? cardLength : bankInfo.getDefaultLength();
        if (finalLength < 13 || finalLength > 19) {
            throw new IllegalArgumentException("卡号长度必须在13-19位之间");
        }

        // 生成卡号
        String cardNumber = generateCardNumber(selectedBinCode, finalLength);

        // 格式化输出
        return formatCardNumber(cardNumber, format);
    }

    /**
     * 选择银行
     * 
     * @param bank 银行代码或"random"
     * @return 银行信息
     */
    private BankInfo selectBank(String bank) {
        if ("random".equals(bank) || bank == null) {
            List<String> bankCodes = Arrays.asList("ICBC", "CCB", "ABC", "BOC", "BOCOM", "CMB", "CITIC", "CEB", "HXB",
                    "CMBC");
            String randomBank = bankCodes.get(random.nextInt(bankCodes.size()));
            return BANK_BIN_MAP.get(randomBank);
        }

        BankInfo bankInfo = BANK_BIN_MAP.get(bank.toUpperCase());
        if (bankInfo == null) {
            throw new IllegalArgumentException("不支持的银行代码: " + bank);
        }

        return bankInfo;
    }

    /**
     * 选择BIN码
     * 
     * @param bankInfo 银行信息
     * @return BIN码
     */
    private String selectBinCode(BankInfo bankInfo) {
        List<String> binCodes = bankInfo.getBinCodes();
        return binCodes.get(random.nextInt(binCodes.size()));
    }

    /**
     * 生成银行卡号
     * 
     * @param binCode BIN码（前6位）
     * @param length  卡号总长度
     * @return 完整的银行卡号
     */
    private String generateCardNumber(String binCode, int length) {
        StringBuilder cardNumber = new StringBuilder(binCode);

        // 生成中间位数（除了最后一位校验位）
        int middleDigits = length - binCode.length() - 1;
        for (int i = 0; i < middleDigits; i++) {
            cardNumber.append(random.nextInt(10));
        }

        // 计算并添加Luhn校验位
        int checkDigit = calculateLuhnCheckDigit(cardNumber.toString());
        cardNumber.append(checkDigit);

        return cardNumber.toString();
    }

    /**
     * 计算Luhn算法校验位
     * 
     * @param cardNumberWithoutCheck 不包含校验位的卡号
     * @return 校验位
     */
    private int calculateLuhnCheckDigit(String cardNumberWithoutCheck) {
        int sum = 0;
        boolean alternate = true;

        // 从右到左处理每一位
        for (int i = cardNumberWithoutCheck.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(cardNumberWithoutCheck.charAt(i));

            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = digit / 10 + digit % 10;
                }
            }

            sum += digit;
            alternate = !alternate;
        }

        return (10 - (sum % 10)) % 10;
    }

    /**
     * 验证Luhn算法
     * 
     * @param cardNumber 完整卡号
     * @return 是否通过Luhn验证
     */
    private boolean validateLuhn(String cardNumber) {
        int sum = 0;
        boolean alternate = false;

        for (int i = cardNumber.length() - 1; i >= 0; i--) {
            int digit = Character.getNumericValue(cardNumber.charAt(i));

            if (alternate) {
                digit *= 2;
                if (digit > 9) {
                    digit = digit / 10 + digit % 10;
                }
            }

            sum += digit;
            alternate = !alternate;
        }

        return sum % 10 == 0;
    }

    /**
     * 格式化卡号输出
     * 
     * @param cardNumber 卡号
     * @param format     格式
     * @return 格式化后的卡号
     */
    private String formatCardNumber(String cardNumber, String format) {
        if ("space".equals(format)) {
            StringBuilder formatted = new StringBuilder();
            for (int i = 0; i < cardNumber.length(); i++) {
                if (i > 0 && i % 4 == 0) {
                    formatted.append(" ");
                }
                formatted.append(cardNumber.charAt(i));
            }
            return formatted.toString();
        }

        return cardNumber;
    }

    @Override
    public ValidationResult validateWithDetails(String data) {
        if (data == null) {
            return ValidationResult.error("银行卡号不能为空");
        }

        // 清理格式字符
        String cleanCardNumber = data.replaceAll("\\s+", "");

        if (cleanCardNumber.isEmpty()) {
            return ValidationResult.error("银行卡号不能为空");
        }

        // 格式检查
        if (!BANK_CARD_PATTERN.matcher(cleanCardNumber).matches()) {
            return ValidationResult.error("银行卡号格式不正确，只能包含数字");
        }

        // 长度检查
        if (cleanCardNumber.length() < 13 || cleanCardNumber.length() > 19) {
            return ValidationResult.error("银行卡号长度必须在13-19位之间");
        }

        // Luhn算法验证
        if (!validateLuhn(cleanCardNumber)) {
            return ValidationResult.error("银行卡号校验位不正确");
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }

    /**
     * 银行信息类
     */
    private static class BankInfo {
        private final String name;
        private final List<String> binCodes;
        private final int defaultLength;

        public BankInfo(String name, List<String> binCodes, int defaultLength) {
            this.name = name;
            this.binCodes = binCodes;
            this.defaultLength = defaultLength;
        }

        public List<String> getBinCodes() {
            return binCodes;
        }

        public int getDefaultLength() {
            return defaultLength;
        }
    }
}