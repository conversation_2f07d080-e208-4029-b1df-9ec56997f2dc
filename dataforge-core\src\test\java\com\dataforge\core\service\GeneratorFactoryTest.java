package com.dataforge.core.service;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GeneratorFactory测试类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
class GeneratorFactoryTest {

    private GeneratorFactory generatorFactory;

    @BeforeEach
    void setUp() {
        generatorFactory = new GeneratorFactory();
    }

    @Test
    void testGetGenerator() {
        // 测试获取存在的生成器
        DataGenerator<?> nameGenerator = generatorFactory.getGenerator("name");
        assertNotNull(nameGenerator, "应该能够获取到name生成器");

        // 测试获取不存在的生成器
        DataGenerator<?> nonExistentGenerator = generatorFactory.getGenerator("non-existent");
        assertNull(nonExistentGenerator, "不存在的生成器应该返回null");

        // 测试空参数
        DataGenerator<?> nullGenerator = generatorFactory.getGenerator(null);
        assertNull(nullGenerator, "null参数应该返回null");

        DataGenerator<?> emptyGenerator = generatorFactory.getGenerator("");
        assertNull(emptyGenerator, "空字符串参数应该返回null");
    }

    @Test
    void testRegisterGenerator() {
        TestGenerator testGenerator = new TestGenerator();

        // 注册生成器
        generatorFactory.registerGenerator("test", testGenerator);

        // 验证注册成功
        assertTrue(generatorFactory.hasGenerator("test"), "生成器应该注册成功");

        DataGenerator<?> retrievedGenerator = generatorFactory.getGenerator("test");
        assertNotNull(retrievedGenerator, "应该能够获取到注册的生成器");
        assertEquals("test", retrievedGenerator.getType(), "生成器类型应该匹配");
    }

    @Test
    void testUnregisterGenerator() {
        TestGenerator testGenerator = new TestGenerator();

        // 注册生成器
        generatorFactory.registerGenerator("test", testGenerator);
        assertTrue(generatorFactory.hasGenerator("test"), "生成器应该注册成功");

        // 注销生成器
        boolean unregistered = generatorFactory.unregisterGenerator("test");
        assertTrue(unregistered, "注销操作应该成功");
        assertFalse(generatorFactory.hasGenerator("test"), "生成器应该被注销");

        // 再次注销不存在的生成器
        boolean unregisteredAgain = generatorFactory.unregisterGenerator("test");
        assertFalse(unregisteredAgain, "注销不存在的生成器应该返回false");
    }

    @Test
    void testCacheManagement() {
        TestGenerator testGenerator = new TestGenerator();
        generatorFactory.registerGenerator("test", testGenerator);

        // 第一次获取，应该创建实例
        DataGenerator<?> generator1 = generatorFactory.getGenerator("test");
        assertNotNull(generator1);

        // 第二次获取，应该从缓存获取
        DataGenerator<?> generator2 = generatorFactory.getGenerator("test");
        assertNotNull(generator2);

        // 清除缓存
        generatorFactory.clearCache("test");

        // 再次获取，应该重新创建实例
        DataGenerator<?> generator3 = generatorFactory.getGenerator("test");
        assertNotNull(generator3);
    }

    @Test
    void testGetGeneratorCount() {
        int initialCount = generatorFactory.getGeneratorCount();
        assertTrue(initialCount > 0, "应该有内置生成器");

        TestGenerator testGenerator = new TestGenerator();
        generatorFactory.registerGenerator("test", testGenerator);

        assertEquals(initialCount + 1, generatorFactory.getGeneratorCount(),
                "注册生成器后数量应该增加");

        generatorFactory.unregisterGenerator("test");
        assertEquals(initialCount, generatorFactory.getGeneratorCount(),
                "注销生成器后数量应该恢复");
    }

    @Test
    void testGetRegisteredTypes() {
        Iterable<String> types = generatorFactory.getRegisteredTypes();
        assertNotNull(types, "注册类型列表不应该为null");

        boolean hasName = false;
        for (String type : types) {
            if ("name".equals(type)) {
                hasName = true;
                break;
            }
        }
        assertTrue(hasName, "应该包含name生成器类型");
    }

    /**
     * 测试用的生成器实现
     */
    private static class TestGenerator implements DataGenerator<String> {

        @Override
        public String generate(GenerationContext context) {
            return "test-value";
        }

        @Override
        public ValidationResult validateWithDetails(String data) {
            return ValidationResult.success();
        }

        @Override
        public String getType() {
            return "test";
        }

        @Override
        public String getDescription() {
            return "测试生成器";
        }

        @Override
        public GeneratorParameter[] getSupportedParameters() {
            return new GeneratorParameter[0];
        }

        @Override
        public boolean isThreadSafe() {
            return true;
        }
    }
}