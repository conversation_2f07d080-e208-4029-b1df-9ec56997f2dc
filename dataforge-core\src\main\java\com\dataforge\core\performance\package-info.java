/**
 * 性能优化包
 * 
 * 提供多线程数据生成、性能监控和优化相关的功能。
 * 
 * <h2>主要组件：</h2>
 * <ul>
 * <li>{@link com.dataforge.core.performance.ThreadPoolConfig} - 线程池配置</li>
 * <li>{@link com.dataforge.core.performance.ParallelGenerationService} -
 * 并行生成服务</li>
 * <li>{@link com.dataforge.core.performance.PerformanceMonitor} - 性能监控器</li>
 * <li>{@link com.dataforge.core.performance.ParallelDataGenerationManager} -
 * 并行数据生成管理器</li>
 * </ul>
 * 
 * <h2>使用示例：</h2>
 * 
 * <pre>{@code
 * // 创建线程池配置
 * ThreadPoolConfig config = new ThreadPoolConfig();
 * config.setCorePoolSize(4);
 * config.setMaximumPoolSize(8);
 * 
 * // 创建并行生成管理器
 * ParallelDataGenerationManager manager = new ParallelDataGenerationManager(config, generatorFactory);
 * 
 * // 并行生成数据
 * GenerationContext context = new GenerationContext.Builder()
 *         .withParameter("count", 1000)
 *         .build();
 * 
 * List<String> names = manager.generateData("name", context, 10000);
 * 
 * // 获取性能统计
 * PerformanceMonitor.OverallStats stats = manager.getOverallStats();
 * System.out.println("Throughput: " + stats.getOverallThroughput() + " items/sec");
 * }</pre>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
package com.dataforge.core.performance;