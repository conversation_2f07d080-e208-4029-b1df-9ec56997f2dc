package com.dataforge.core.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

/**
 * 性能配置
 * 
 * 定义数据生成的性能相关配置，包括线程池大小、批处理大小、缓存设置等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class PerformanceConfig {

    @JsonProperty("threadPoolSize")
    private int threadPoolSize = Runtime.getRuntime().availableProcessors();

    @JsonProperty("batchSize")
    private int batchSize = 1000;

    @JsonProperty("enableParallel")
    private boolean enableParallel = true;

    @JsonProperty("cacheEnabled")
    private boolean cacheEnabled = true;

    @JsonProperty("cacheMaxSize")
    private int cacheMaxSize = 10000;

    @JsonProperty("bufferSize")
    private int bufferSize = 8192;

    @JsonProperty("maxMemoryUsage")
    private long maxMemoryUsage = 512 * 1024 * 1024; // 512MB

    /**
     * 默认构造函数
     */
    public PerformanceConfig() {
    }

    /**
     * 构造函数
     * 
     * @param threadPoolSize 线程池大小
     * @param batchSize      批处理大小
     */
    public PerformanceConfig(int threadPoolSize, int batchSize) {
        this.threadPoolSize = threadPoolSize;
        this.batchSize = batchSize;
    }

    // Getters and Setters

    public int getThreadPoolSize() {
        return threadPoolSize;
    }

    public void setThreadPoolSize(int threadPoolSize) {
        this.threadPoolSize = threadPoolSize; // 允许设置无效值，由验证器检查
    }

    public int getBatchSize() {
        return batchSize;
    }

    public void setBatchSize(int batchSize) {
        this.batchSize = batchSize; // 允许设置无效值，由验证器检查
    }

    public boolean isEnableParallel() {
        return enableParallel;
    }

    public void setEnableParallel(boolean enableParallel) {
        this.enableParallel = enableParallel;
    }

    public boolean isCacheEnabled() {
        return cacheEnabled;
    }

    public void setCacheEnabled(boolean cacheEnabled) {
        this.cacheEnabled = cacheEnabled;
    }

    public int getCacheMaxSize() {
        return cacheMaxSize;
    }

    public void setCacheMaxSize(int cacheMaxSize) {
        this.cacheMaxSize = cacheMaxSize; // 允许设置无效值，由验证器检查
    }

    public int getBufferSize() {
        return bufferSize;
    }

    public void setBufferSize(int bufferSize) {
        this.bufferSize = bufferSize; // 允许设置无效值，由验证器检查
    }

    public long getMaxMemoryUsage() {
        return maxMemoryUsage;
    }

    public void setMaxMemoryUsage(long maxMemoryUsage) {
        this.maxMemoryUsage = maxMemoryUsage; // 允许设置无效值，由验证器检查
    }

    /**
     * 检查是否启用并行处理
     * 
     * @return 是否启用并行处理
     */
    public boolean isParallelEnabled() {
        return enableParallel;
    }

    /**
     * 获取线程数量
     * 
     * @return 线程数量
     */
    public int getThreadCount() {
        return threadPoolSize;
    }

    /**
     * 设置并行启用
     * 
     * @param parallelEnabled 是否启用并行
     */
    public void setParallelEnabled(boolean parallelEnabled) {
        this.enableParallel = parallelEnabled;
    }

    /**
     * 设置线程数量
     * 
     * @param threadCount 线程数量
     */
    public void setThreadCount(int threadCount) {
        this.threadPoolSize = threadCount;
    }

    /**
     * 检查是否应该使用并行处理
     * 
     * @param dataCount 数据数量
     * @return 如果应该使用并行处理返回true，否则返回false
     */
    public boolean shouldUseParallel(int dataCount) {
        return enableParallel && dataCount >= Math.max(1, batchSize) && Math.max(1, threadPoolSize) > 1;
    }

    /**
     * 计算实际的线程数
     * 
     * @param dataCount 数据数量
     * @return 实际线程数
     */
    public int getActualThreadCount(int dataCount) {
        if (!shouldUseParallel(dataCount)) {
            return 1;
        }

        int safeThreadPoolSize = Math.max(1, Math.min(threadPoolSize, 32));
        int safeBatchSize = Math.max(1, batchSize);
        int maxThreads = Math.min(safeThreadPoolSize, dataCount / safeBatchSize + 1);
        return Math.max(1, maxThreads);
    }

    /**
     * 计算每个线程的批处理大小
     * 
     * @param dataCount   数据数量
     * @param threadCount 线程数量
     * @return 每个线程的批处理大小
     */
    public int getBatchSizePerThread(int dataCount, int threadCount) {
        int safeBatchSize = Math.max(1, batchSize);
        if (threadCount <= 1) {
            return Math.min(dataCount, safeBatchSize);
        }

        return Math.max(1, dataCount / threadCount);
    }

    /**
     * 验证性能配置的有效性
     * 
     * @return 验证结果
     */
    public ValidationResult validate() {
        ValidationResult result = new ValidationResult();

        if (threadPoolSize <= 0) {
            result.addError("线程池大小必须大于0");
        } else if (threadPoolSize > 32) {
            result.addWarning("线程池大小过大，可能影响性能");
        }

        if (batchSize <= 0) {
            result.addError("批处理大小必须大于0");
        } else if (batchSize > 100000) {
            result.addWarning("批处理大小过大，可能导致内存不足");
        }

        if (cacheMaxSize <= 0) {
            result.addError("缓存最大大小必须大于0");
        }

        if (bufferSize <= 0) {
            result.addError("缓冲区大小必须大于0");
        }

        if (maxMemoryUsage <= 0) {
            result.addError("最大内存使用量必须大于0");
        }

        return result;
    }

    /**
     * 创建性能配置的副本
     * 
     * @return 性能配置副本
     */
    public PerformanceConfig copy() {
        PerformanceConfig copy = new PerformanceConfig();
        copy.threadPoolSize = this.threadPoolSize;
        copy.batchSize = this.batchSize;
        copy.enableParallel = this.enableParallel;
        copy.cacheEnabled = this.cacheEnabled;
        copy.cacheMaxSize = this.cacheMaxSize;
        copy.bufferSize = this.bufferSize;
        copy.maxMemoryUsage = this.maxMemoryUsage;
        return copy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        PerformanceConfig that = (PerformanceConfig) o;
        return threadPoolSize == that.threadPoolSize &&
                batchSize == that.batchSize &&
                enableParallel == that.enableParallel &&
                cacheEnabled == that.cacheEnabled &&
                cacheMaxSize == that.cacheMaxSize &&
                bufferSize == that.bufferSize &&
                maxMemoryUsage == that.maxMemoryUsage;
    }

    @Override
    public int hashCode() {
        return Objects.hash(threadPoolSize, batchSize, enableParallel, cacheEnabled, cacheMaxSize, bufferSize,
                maxMemoryUsage);
    }

    @Override
    public String toString() {
        return String.format("PerformanceConfig{threads=%d, batchSize=%d, parallel=%s, cache=%s}",
                threadPoolSize, batchSize, enableParallel, cacheEnabled);
    }
}