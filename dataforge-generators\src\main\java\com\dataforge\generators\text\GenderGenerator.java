package com.dataforge.generators.text;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;
import java.util.Random;

/**
 * 性别生成器
 * 
 * 生成性别数据，支持自定义性别比例和输出格式。
 * 可以与其他字段建立关联，如姓名、年龄等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class GenderGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "gender";
    private static final String DESCRIPTION = "生成性别数据";

    // 性别常量
    public static final String MALE = "male";
    public static final String FEMALE = "female";

    // 输出格式映射
    private static final String MALE_CN = "男";
    private static final String FEMALE_CN = "女";
    private static final String MALE_EN = "Male";
    private static final String FEMALE_EN = "Female";
    private static final String MALE_SHORT = "M";
    private static final String FEMALE_SHORT = "F";
    private static final String MALE_NUM = "1";
    private static final String FEMALE_NUM = "0";

    private final Random random;

    /**
     * 构造函数
     */
    public GenderGenerator() {
        this.random = new Random();
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("maleRatio", Double.class, 0.5,
                "男性比例，范围0.0-1.0，默认0.5（50%）", false));
        addParameter(new GeneratorParameter("format", String.class, "chinese",
                "输出格式：chinese（中文）、english（英文）、short（缩写）、number（数字）", false));
        addParameter(new GeneratorParameter("fixedGender", String.class, null,
                "固定性别：male（男性）、female（女性），设置后忽略maleRatio", false));
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        Double maleRatio = context.getParameter("maleRatio", 0.5);
        String format = context.getParameter("format", "chinese");
        String fixedGender = context.getParameter("fixedGender", null);

        // 参数验证
        if (maleRatio < 0.0 || maleRatio > 1.0) {
            throw new IllegalArgumentException("男性比例必须在0.0-1.0之间");
        }

        // 确定性别
        String gender;
        if (fixedGender != null) {
            gender = fixedGender.toLowerCase();
            if (!MALE.equals(gender) && !FEMALE.equals(gender)) {
                throw new IllegalArgumentException("固定性别只能是male或female");
            }
        } else {
            gender = random.nextDouble() < maleRatio ? MALE : FEMALE;
        }

        // 格式化输出
        return formatGender(gender, format);
    }

    /**
     * 格式化性别输出
     * 
     * @param gender 性别（male/female）
     * @param format 输出格式
     * @return 格式化后的性别字符串
     */
    private String formatGender(String gender, String format) {
        boolean isMale = MALE.equals(gender);

        switch (format.toLowerCase()) {
            case "chinese":
                return isMale ? MALE_CN : FEMALE_CN;
            case "english":
                return isMale ? MALE_EN : FEMALE_EN;
            case "short":
                return isMale ? MALE_SHORT : FEMALE_SHORT;
            case "number":
                return isMale ? MALE_NUM : FEMALE_NUM;
            default:
                return isMale ? MALE_CN : FEMALE_CN;
        }
    }

    /**
     * 解析性别字符串为标准格式
     * 
     * @param genderStr 性别字符串
     * @return 标准化的性别（male/female）
     */
    public static String parseGender(String genderStr) {
        if (genderStr == null) {
            return null;
        }

        String normalized = genderStr.trim().toLowerCase();

        // 中文格式
        if (MALE_CN.equals(normalized)) {
            return MALE;
        }
        if (FEMALE_CN.equals(normalized)) {
            return FEMALE;
        }

        // 英文格式
        if ("male".equals(normalized) || "m".equals(normalized) || "1".equals(normalized)) {
            return MALE;
        }
        if ("female".equals(normalized) || "f".equals(normalized) || "0".equals(normalized)) {
            return FEMALE;
        }

        return null;
    }

    @Override
    public ValidationResult validateWithDetails(String data) {
        if (data == null) {
            return ValidationResult.error("性别不能为空");
        }

        String trimmed = data.trim();
        if (trimmed.isEmpty()) {
            return ValidationResult.error("性别不能为空");
        }

        // 检查是否为有效的性别值
        String parsedGender = parseGender(trimmed);
        if (parsedGender == null) {
            return ValidationResult.error("无效的性别值：" + trimmed +
                    "，支持的格式：男/女、Male/Female、M/F、1/0");
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }
}