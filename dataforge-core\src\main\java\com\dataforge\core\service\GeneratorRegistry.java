package com.dataforge.core.service;

import com.dataforge.core.generator.DataGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 生成器注册表
 * 
 * 负责管理生成器的注册、发现和自动加载。
 * 支持内置生成器的自动发现和运行时动态注册。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class GeneratorRegistry {

    private static final Logger logger = LoggerFactory.getLogger(GeneratorRegistry.class);

    // 注册的生成器映射表
    private final Map<String, DataGenerator<?>> registeredGenerators = new ConcurrentHashMap<>();

    // 生成器元数据映射表
    private final Map<String, GeneratorMetadata> generatorMetadata = new ConcurrentHashMap<>();

    // 读写锁
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    // 内置生成器类名映射
    private static final Map<String, String> BUILTIN_GENERATORS = new HashMap<>();

    static {
        BUILTIN_GENERATORS.put("name", "com.dataforge.generators.text.NameGenerator");
        BUILTIN_GENERATORS.put("phone", "com.dataforge.generators.text.PhoneGenerator");
        BUILTIN_GENERATORS.put("email", "com.dataforge.generators.text.EmailGenerator");
        BUILTIN_GENERATORS.put("gender", "com.dataforge.generators.text.GenderGenerator");
        BUILTIN_GENERATORS.put("age", "com.dataforge.generators.numeric.AgeGenerator");
        BUILTIN_GENERATORS.put("idcard", "com.dataforge.generators.identifier.IdCardNumberGenerator");
        BUILTIN_GENERATORS.put("bankcard", "com.dataforge.generators.identifier.BankCardNumberGenerator");
        BUILTIN_GENERATORS.put("uuid", "com.dataforge.generators.identifier.UUIDGenerator");
        BUILTIN_GENERATORS.put("uscc", "com.dataforge.generators.identifier.USCCGenerator");
    }

    /**
     * 构造函数
     * 初始化时自动发现和注册内置生成器
     */
    public GeneratorRegistry() {
        autoDiscoverBuiltinGenerators();
        autoDiscoverSpiGenerators();
        logger.info("生成器注册表初始化完成，已注册{}个生成器", registeredGenerators.size());
    }

    /**
     * 注册生成器
     * 
     * @param generator 生成器实例
     * @return 如果注册成功返回true，否则返回false
     */
    public boolean registerGenerator(DataGenerator<?> generator) {
        if (generator == null) {
            logger.warn("注册生成器失败：生成器为空");
            return false;
        }

        String type = generator.getType();
        if (type == null || type.trim().isEmpty()) {
            logger.warn("注册生成器失败：生成器类型为空");
            return false;
        }

        String normalizedType = normalizeType(type);

        lock.writeLock().lock();
        try {
            // 检查是否已存在
            if (registeredGenerators.containsKey(normalizedType)) {
                logger.warn("生成器类型{}已存在，将被覆盖", normalizedType);
            }

            registeredGenerators.put(normalizedType, generator);

            // 创建元数据
            GeneratorMetadata metadata = new GeneratorMetadata(
                    normalizedType,
                    generator.getClass().getName(),
                    generator.getDescription(),
                    generator.getVersion(),
                    System.currentTimeMillis(),
                    GeneratorSource.RUNTIME);
            generatorMetadata.put(normalizedType, metadata);

            logger.info("注册生成器成功: {} -> {}", normalizedType, generator.getClass().getSimpleName());
            return true;

        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 批量注册生成器
     * 
     * @param generators 生成器列表
     * @return 成功注册的数量
     */
    public int registerGenerators(Collection<DataGenerator<?>> generators) {
        if (generators == null || generators.isEmpty()) {
            return 0;
        }

        int successCount = 0;
        for (DataGenerator<?> generator : generators) {
            if (registerGenerator(generator)) {
                successCount++;
            }
        }

        logger.info("批量注册完成，成功注册{}个生成器", successCount);
        return successCount;
    }

    /**
     * 获取注册的生成器
     * 
     * @param type 生成器类型
     * @return 生成器实例，如果不存在返回null
     */
    public DataGenerator<?> getGenerator(String type) {
        if (type == null || type.trim().isEmpty()) {
            return null;
        }

        String normalizedType = normalizeType(type);

        lock.readLock().lock();
        try {
            return registeredGenerators.get(normalizedType);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 检查是否存在指定类型的生成器
     * 
     * @param type 生成器类型
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasGenerator(String type) {
        if (type == null || type.trim().isEmpty()) {
            return false;
        }

        String normalizedType = normalizeType(type);

        lock.readLock().lock();
        try {
            return registeredGenerators.containsKey(normalizedType);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 注销生成器
     * 
     * @param type 生成器类型
     * @return 如果注销成功返回true，否则返回false
     */
    public boolean unregisterGenerator(String type) {
        if (type == null || type.trim().isEmpty()) {
            return false;
        }

        String normalizedType = normalizeType(type);

        lock.writeLock().lock();
        try {
            DataGenerator<?> removed = registeredGenerators.remove(normalizedType);
            generatorMetadata.remove(normalizedType);

            if (removed != null) {
                logger.info("注销生成器: {}", normalizedType);
                return true;
            }
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取所有已注册的生成器类型
     * 
     * @return 生成器类型集合
     */
    public Set<String> getRegisteredTypes() {
        lock.readLock().lock();
        try {
            return new HashSet<>(registeredGenerators.keySet());
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取所有已注册的生成器
     * 
     * @return 生成器映射表的副本
     */
    public Map<String, DataGenerator<?>> getAllGenerators() {
        lock.readLock().lock();
        try {
            return new HashMap<>(registeredGenerators);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取生成器元数据
     * 
     * @param type 生成器类型
     * @return 生成器元数据，如果不存在返回null
     */
    public GeneratorMetadata getGeneratorMetadata(String type) {
        if (type == null || type.trim().isEmpty()) {
            return null;
        }

        String normalizedType = normalizeType(type);

        lock.readLock().lock();
        try {
            return generatorMetadata.get(normalizedType);
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取所有生成器元数据
     * 
     * @return 生成器元数据列表
     */
    public List<GeneratorMetadata> getAllGeneratorMetadata() {
        lock.readLock().lock();
        try {
            return new ArrayList<>(generatorMetadata.values());
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 获取已注册的生成器数量
     * 
     * @return 生成器数量
     */
    public int getGeneratorCount() {
        lock.readLock().lock();
        try {
            return registeredGenerators.size();
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 清除所有注册的生成器
     */
    public void clear() {
        lock.writeLock().lock();
        try {
            registeredGenerators.clear();
            generatorMetadata.clear();
            logger.info("清除所有注册的生成器");
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 自动发现和注册内置生成器
     */
    private void autoDiscoverBuiltinGenerators() {
        logger.debug("开始自动发现内置生成器");

        for (Map.Entry<String, String> entry : BUILTIN_GENERATORS.entrySet()) {
            String type = entry.getKey();
            String className = entry.getValue();

            try {
                DataGenerator<?> generator = loadGeneratorByReflection(className);
                if (generator != null) {
                    String normalizedType = normalizeType(type);
                    registeredGenerators.put(normalizedType, generator);

                    // 创建元数据
                    GeneratorMetadata metadata = new GeneratorMetadata(
                            normalizedType,
                            className,
                            generator.getDescription(),
                            generator.getVersion(),
                            System.currentTimeMillis(),
                            GeneratorSource.BUILTIN);
                    generatorMetadata.put(normalizedType, metadata);

                    logger.debug("自动注册内置生成器: {} -> {}", normalizedType, className);
                }
            } catch (Exception e) {
                logger.debug("加载内置生成器{}失败，跳过: {}", className, e.getMessage());
            }
        }

        logger.info("内置生成器自动发现完成，共注册{}个生成器", registeredGenerators.size());
    }

    /**
     * 自动发现和注册SPI生成器
     */
    private void autoDiscoverSpiGenerators() {
        logger.debug("开始自动发现SPI生成器");

        try {
            // 加载GeneratorProvider实现
            ServiceLoader<GeneratorProvider> providerLoader = ServiceLoader.load(GeneratorProvider.class);

            for (GeneratorProvider provider : providerLoader) {
                loadGeneratorsFromProvider(provider);
            }

            // 兼容旧的直接DataGenerator SPI方式
            @SuppressWarnings("rawtypes")
            ServiceLoader<DataGenerator> generatorLoader = ServiceLoader.load(DataGenerator.class);

            for (DataGenerator<?> generator : generatorLoader) {
                if (registerGenerator(generator)) {
                    logger.debug("自动注册SPI生成器: {} -> {}",
                            generator.getType(), generator.getClass().getSimpleName());
                }
            }

        } catch (Exception e) {
            logger.error("自动发现SPI生成器时发生异常", e);
        }

        logger.info("SPI生成器自动发现完成");
    }

    /**
     * 从生成器提供者加载生成器
     * 
     * @param provider 生成器提供者
     */
    private void loadGeneratorsFromProvider(GeneratorProvider provider) {
        try {
            logger.debug("加载生成器提供者: {} v{}", provider.getProviderName(), provider.getVersion());

            // 检查提供者是否可用
            if (!provider.isAvailable()) {
                logger.warn("生成器提供者{}不可用，跳过加载", provider.getProviderName());
                return;
            }

            // 初始化提供者
            provider.initialize();

            // 获取并注册生成器
            Collection<DataGenerator<?>> generators = provider.getGenerators();
            if (generators != null && !generators.isEmpty()) {
                int registeredCount = registerGenerators(generators);
                logger.info("从提供者{}加载了{}个生成器", provider.getProviderName(), registeredCount);
            } else {
                logger.warn("生成器提供者{}没有提供任何生成器", provider.getProviderName());
            }

        } catch (Exception e) {
            logger.error("加载生成器提供者{}时发生异常: {}", provider.getProviderName(), e.getMessage(), e);
        }
    }

    /**
     * 通过反射加载生成器
     * 
     * @param className 生成器类名
     * @return 生成器实例，如果加载失败返回null
     */
    private DataGenerator<?> loadGeneratorByReflection(String className) {
        try {
            Class<?> clazz = Class.forName(className);
            Object instance = clazz.getDeclaredConstructor().newInstance();

            if (instance instanceof DataGenerator) {
                return (DataGenerator<?>) instance;
            } else {
                logger.warn("类{}不是DataGenerator的实例", className);
                return null;
            }
        } catch (ClassNotFoundException e) {
            logger.debug("生成器类{}未找到", className);
            return null;
        } catch (Exception e) {
            logger.warn("加载生成器{}时发生异常: {}", className, e.getMessage());
            return null;
        }
    }

    /**
     * 标准化类型名称
     * 
     * @param type 原始类型名称
     * @return 标准化后的类型名称
     */
    private String normalizeType(String type) {
        return type.trim().toLowerCase().replaceAll("[-_\\s]+", "-");
    }

    /**
     * 生成器元数据
     */
    public static class GeneratorMetadata {
        private final String type;
        private final String className;
        private final String description;
        private final String version;
        private final long registrationTime;
        private final GeneratorSource source;

        public GeneratorMetadata(String type, String className, String description,
                String version, long registrationTime, GeneratorSource source) {
            this.type = type;
            this.className = className;
            this.description = description;
            this.version = version;
            this.registrationTime = registrationTime;
            this.source = source;
        }

        public String getType() {
            return type;
        }

        public String getClassName() {
            return className;
        }

        public String getDescription() {
            return description;
        }

        public String getVersion() {
            return version;
        }

        public long getRegistrationTime() {
            return registrationTime;
        }

        public GeneratorSource getSource() {
            return source;
        }

        @Override
        public String toString() {
            return String.format("GeneratorMetadata{type='%s', className='%s', version='%s', source=%s}",
                    type, className, version, source);
        }
    }

    /**
     * 生成器来源枚举
     */
    public enum GeneratorSource {
        BUILTIN, // 内置生成器
        SPI, // SPI加载的生成器
        RUNTIME // 运行时注册的生成器
    }
}