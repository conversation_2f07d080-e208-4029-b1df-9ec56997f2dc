package com.dataforge.core.relation;

import org.junit.jupiter.api.Test;

import java.time.LocalDate;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RelationRuleFactory 单元测试
 */
class RelationRuleFactoryTest {

    @Test
    void testCreateIdCardRelation() {
        FieldRelation relation = RelationRuleFactory.createIdCardRelation();

        assertEquals("idcard", relation.getSourceField());
        assertEquals(RelationType.EXTRACTION, relation.getRelationType());
        assertTrue(relation.getTargetFields().contains("age"));
        assertTrue(relation.getTargetFields().contains("gender"));
        assertTrue(relation.getTargetFields().contains("birth_date"));
        assertTrue(relation.getTargetFields().contains("region_code"));
    }

    @Test
    void testIdCardExtractionValid() {
        FieldRelation relation = RelationRuleFactory.createIdCardRelation();

        // 使用一个有效的身份证号（1990年1月1日出生的男性）
        String idCard = "110101199001011234";
        RelationResult result = relation.execute(idCard);

        assertTrue(result.isSuccess());
        assertEquals("1990-01-01", result.getValue("birth_date"));
        assertEquals("MALE", result.getValue("gender"));
        assertEquals("110101", result.getValue("region_code"));

        // 验证年龄计算（应该是当前年份减去1990）
        Integer age = result.getValue("age", Integer.class);
        assertNotNull(age);
        assertTrue(age >= 30); // 假设当前年份至少是2020年
    }

    @Test
    void testIdCardExtractionFemale() {
        FieldRelation relation = RelationRuleFactory.createIdCardRelation();

        // 使用一个有效的身份证号（1990年1月1日出生的女性）
        String idCard = "110101199001011223";
        RelationResult result = relation.execute(idCard);

        assertTrue(result.isSuccess());
        assertEquals("FEMALE", result.getValue("gender"));
    }

    @Test
    void testIdCardExtractionInvalid() {
        FieldRelation relation = RelationRuleFactory.createIdCardRelation();

        // 测试无效长度
        RelationResult result1 = relation.execute("12345");
        assertTrue(result1.isFailure());
        assertTrue(result1.getErrorMessage().contains("长度不正确"));

        // 测试空值
        RelationResult result2 = relation.execute(null);
        assertTrue(result2.isFailure());
        assertTrue(result2.getErrorMessage().contains("为空"));

        // 测试无效日期
        RelationResult result3 = relation.execute("110101199913011234");
        assertTrue(result3.isFailure());
        assertTrue(result3.getErrorMessage().contains("日期格式不正确"));
    }

    @Test
    void testCreateNameRelation() {
        FieldRelation relation = RelationRuleFactory.createNameRelation();

        assertEquals("name", relation.getSourceField());
        assertEquals(RelationType.EXTRACTION, relation.getRelationType());
        assertTrue(relation.getTargetFields().contains("surname"));
        assertTrue(relation.getTargetFields().contains("given_name"));
        assertTrue(relation.getTargetFields().contains("name_pinyin"));
    }

    @Test
    void testNameExtractionValid() {
        FieldRelation relation = RelationRuleFactory.createNameRelation();

        RelationResult result = relation.execute("张三");

        assertTrue(result.isSuccess());
        assertEquals("张", result.getValue("surname"));
        assertEquals("三", result.getValue("given_name"));
        assertNotNull(result.getValue("name_pinyin"));
    }

    @Test
    void testNameExtractionSingleChar() {
        FieldRelation relation = RelationRuleFactory.createNameRelation();

        RelationResult result = relation.execute("李");

        assertTrue(result.isSuccess());
        assertEquals("李", result.getValue("surname"));
        assertEquals("", result.getValue("given_name"));
    }

    @Test
    void testNameExtractionInvalid() {
        FieldRelation relation = RelationRuleFactory.createNameRelation();

        RelationResult result = relation.execute(null);
        assertTrue(result.isFailure());
        assertTrue(result.getErrorMessage().contains("为空"));
    }

    @Test
    void testCreateEmailRelation() {
        FieldRelation relation = RelationRuleFactory.createEmailRelation();

        assertEquals("name", relation.getSourceField());
        assertEquals(RelationType.FORMATTING, relation.getRelationType());
        assertTrue(relation.getTargetFields().contains("email_username"));
    }

    @Test
    void testEmailUsernameGeneration() {
        FieldRelation relation = RelationRuleFactory.createEmailRelation();

        RelationResult result = relation.execute("张三");

        assertTrue(result.isSuccess());
        String username = result.getValue("email_username", String.class);
        assertNotNull(username);
        assertFalse(username.isEmpty());
        assertTrue(username.matches("[a-z0-9]+"));
    }

    @Test
    void testCreateAddressRelation() {
        FieldRelation relation = RelationRuleFactory.createAddressRelation();

        assertEquals("address", relation.getSourceField());
        assertEquals(RelationType.EXTRACTION, relation.getRelationType());
        assertTrue(relation.getTargetFields().contains("province"));
        assertTrue(relation.getTargetFields().contains("city"));
        assertTrue(relation.getTargetFields().contains("district"));
    }

    @Test
    void testAddressExtraction() {
        FieldRelation relation = RelationRuleFactory.createAddressRelation();

        RelationResult result = relation.execute("北京市朝阳区建国门外大街1号");

        assertTrue(result.isSuccess());
        // 由于是简化实现，可能无法正确解析所有地址格式
        // 这里主要测试不会抛出异常
    }

    @Test
    void testCreateAgeRelation() {
        FieldRelation relation = RelationRuleFactory.createAgeRelation();

        assertEquals("age", relation.getSourceField());
        assertEquals(RelationType.CALCULATION, relation.getRelationType());
        assertTrue(relation.getTargetFields().contains("birth_year"));
    }

    @Test
    void testAgeCalculation() {
        FieldRelation relation = RelationRuleFactory.createAgeRelation();

        RelationResult result = relation.execute(25);

        assertTrue(result.isSuccess());
        Integer birthYear = result.getValue("birth_year", Integer.class);
        assertNotNull(birthYear);
        assertEquals(LocalDate.now().getYear() - 25, birthYear.intValue());
    }

    @Test
    void testAgeCalculationInvalid() {
        FieldRelation relation = RelationRuleFactory.createAgeRelation();

        // 测试负数年龄
        RelationResult result1 = relation.execute(-5);
        assertTrue(result1.isFailure());
        assertTrue(result1.getErrorMessage().contains("不合理"));

        // 测试过大年龄
        RelationResult result2 = relation.execute(200);
        assertTrue(result2.isFailure());
        assertTrue(result2.getErrorMessage().contains("不合理"));

        // 测试无效格式
        RelationResult result3 = relation.execute("invalid");
        assertTrue(result3.isFailure());
        assertTrue(result3.getErrorMessage().contains("格式不正确"));
    }
}