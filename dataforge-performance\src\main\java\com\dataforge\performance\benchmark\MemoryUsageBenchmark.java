package com.dataforge.performance.benchmark;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.PerformanceConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.service.DataForgeService;
import com.dataforge.core.service.GeneratorFactory;
import com.dataforge.core.service.DataRelationManager;
import com.dataforge.core.relation.ConsistencyManager;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;
import org.openjdk.jol.info.GraphLayout;

import java.lang.management.ManagementFactory;
import java.lang.management.MemoryMXBean;
import java.lang.management.MemoryUsage;
import java.util.concurrent.TimeUnit;

/**
 * 内存使用性能基准测试
 * 
 * 测试数据生成过程中的内存使用情况和GC压力
 */
@BenchmarkMode({Mode.Throughput, Mode.AverageTime})
@OutputTimeUnit(TimeUnit.MILLISECONDS)
@State(Scope.Benchmark)
@Fork(1)
@Warmup(iterations = 2, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 3, time = 2, timeUnit = TimeUnit.SECONDS)
public class MemoryUsageBenchmark {

    private DataForgeService dataForgeService;
    private MemoryMXBean memoryBean;

    @Setup(Level.Trial)
    public void setupTrial() {
        GeneratorFactory generatorFactory = new GeneratorFactory();
        DataRelationManager relationManager = new DataRelationManager();
        ConsistencyManager consistencyManager = new ConsistencyManager();
        dataForgeService = new DataForgeService(generatorFactory, relationManager, consistencyManager);
        
        memoryBean = ManagementFactory.getMemoryMXBean();
    }

    /**
     * 小数据量内存使用测试
     */
    @Benchmark
    public void benchmarkSmallDataMemoryUsage(Blackhole bh) {
        // 强制GC以获得准确的内存基线
        System.gc();
        long beforeMemory = getUsedMemory();
        
        GenerationConfig config = createMemoryTestConfig("uuid", 1000);
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        long afterMemory = getUsedMemory();
        long memoryUsed = afterMemory - beforeMemory;
        
        bh.consume(result);
        bh.consume(memoryUsed);
    }

    /**
     * 中等数据量内存使用测试
     */
    @Benchmark
    public void benchmarkMediumDataMemoryUsage(Blackhole bh) {
        System.gc();
        long beforeMemory = getUsedMemory();
        
        GenerationConfig config = createMemoryTestConfig("email", 10000);
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        long afterMemory = getUsedMemory();
        long memoryUsed = afterMemory - beforeMemory;
        
        bh.consume(result);
        bh.consume(memoryUsed);
    }

    /**
     * 大数据量内存使用测试
     */
    @Benchmark
    public void benchmarkLargeDataMemoryUsage(Blackhole bh) {
        System.gc();
        long beforeMemory = getUsedMemory();
        
        GenerationConfig config = createMemoryTestConfig("idcard", 50000);
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        long afterMemory = getUsedMemory();
        long memoryUsed = afterMemory - beforeMemory;
        
        bh.consume(result);
        bh.consume(memoryUsed);
    }

    /**
     * 并行处理内存使用测试
     */
    @Benchmark
    public void benchmarkParallelMemoryUsage(Blackhole bh) {
        System.gc();
        long beforeMemory = getUsedMemory();
        
        GenerationConfig config = createMemoryTestConfig("name", 20000);
        
        // 启用并行处理
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(4);
        perfConfig.setBatchSize(1000);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        long afterMemory = getUsedMemory();
        long memoryUsed = afterMemory - beforeMemory;
        
        bh.consume(result);
        bh.consume(memoryUsed);
    }

    /**
     * 批处理大小对内存使用的影响测试
     */
    @Param({"100", "500", "1000", "2000", "5000"})
    public int batchSize;

    @Benchmark
    public void benchmarkBatchSizeMemoryImpact(Blackhole bh) {
        System.gc();
        long beforeMemory = getUsedMemory();
        
        GenerationConfig config = createMemoryTestConfig("phone", 10000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(2);
        perfConfig.setBatchSize(batchSize);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        long afterMemory = getUsedMemory();
        long memoryUsed = afterMemory - beforeMemory;
        
        bh.consume(result);
        bh.consume(memoryUsed);
    }

    /**
     * 内存泄漏检测测试
     */
    @Benchmark
    public void benchmarkMemoryLeakDetection(Blackhole bh) {
        // 连续执行多次生成，检查内存是否持续增长
        long initialMemory = getUsedMemory();
        
        for (int i = 0; i < 10; i++) {
            GenerationConfig config = createMemoryTestConfig("uuid", 1000);
            DataForgeService.GenerationResult result = dataForgeService.generateData(config);
            bh.consume(result);
            
            // 每次生成后检查内存使用
            if (i % 3 == 0) {
                System.gc();
                Thread.yield(); // 给GC一些时间
            }
        }
        
        System.gc();
        long finalMemory = getUsedMemory();
        long memoryGrowth = finalMemory - initialMemory;
        
        bh.consume(memoryGrowth);
    }

    /**
     * 对象大小分析测试
     */
    @Benchmark
    public void benchmarkObjectSizeAnalysis(Blackhole bh) {
        GenerationConfig config = createMemoryTestConfig("idcard", 100);
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        // 使用JOL分析对象大小
        if (result.hasData() && !result.getGeneratedData().isEmpty()) {
            Object firstRecord = result.getGeneratedData().get(0);
            GraphLayout layout = GraphLayout.parseInstance(firstRecord);
            long objectSize = layout.totalSize();
            bh.consume(objectSize);
        }
        
        bh.consume(result);
    }

    /**
     * GC压力测试
     */
    @Benchmark
    public void benchmarkGCPressure(Blackhole bh) {
        long gcCountBefore = getGCCount();
        long gcTimeBefore = getGCTime();
        
        // 执行大量数据生成以产生GC压力
        for (int i = 0; i < 5; i++) {
            GenerationConfig config = createMemoryTestConfig("email", 5000);
            DataForgeService.GenerationResult result = dataForgeService.generateData(config);
            bh.consume(result);
        }
        
        long gcCountAfter = getGCCount();
        long gcTimeAfter = getGCTime();
        
        long gcCountDiff = gcCountAfter - gcCountBefore;
        long gcTimeDiff = gcTimeAfter - gcTimeBefore;
        
        bh.consume(gcCountDiff);
        bh.consume(gcTimeDiff);
    }

    /**
     * 获取当前使用的内存量
     */
    private long getUsedMemory() {
        MemoryUsage heapUsage = memoryBean.getHeapMemoryUsage();
        return heapUsage.getUsed();
    }

    /**
     * 获取GC次数
     */
    private long getGCCount() {
        return ManagementFactory.getGarbageCollectorMXBeans().stream()
                .mapToLong(gcBean -> gcBean.getCollectionCount())
                .sum();
    }

    /**
     * 获取GC时间
     */
    private long getGCTime() {
        return ManagementFactory.getGarbageCollectorMXBeans().stream()
                .mapToLong(gcBean -> gcBean.getCollectionTime())
                .sum();
    }

    /**
     * 创建内存测试配置
     */
    private GenerationConfig createMemoryTestConfig(String dataType, int count) {
        GenerationConfig config = new GenerationConfig();
        config.setDataType(dataType);
        config.setCount(count);
        config.setSeed(12345L);
        
        // 禁用验证以专注于内存使用
        ValidationConfig validationConfig = new ValidationConfig();
        validationConfig.setEnabled(false);
        config.setValidationConfig(validationConfig);
        
        // 设置基本性能配置
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(false);
        perfConfig.setBatchSize(1000);
        perfConfig.setCacheEnabled(true); // 启用缓存以测试缓存对内存的影响
        config.setPerformanceConfig(perfConfig);
        
        return config;
    }
}