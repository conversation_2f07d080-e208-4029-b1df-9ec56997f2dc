---
alwaysApply: true
---
Java通用项目规则
1. 项目结构 (Project Structure)
清晰、一致的项目结构是高效开发的基础。
 * Maven/Gradle 标准结构: 遵循Maven或Gradle的标准项目布局。
   * src/main/java: 存放Java源代码。
   * src/main/resources: 存放配置文件、XML、国际化文件等资源。
   * src/main/webapp: (Web项目) 存放Web资源，如JSP、HTML、CSS、JS。
   * src/test/java: 存放单元测试和集成测试代码。
   * src/test/resources: 存放测试相关的资源文件。
   * pom.xml (Maven) / build.gradle (Gradle): 项目构建文件。
 * 模块化/分层:
   * 多模块项目: 对于大型项目，推荐使用多模块结构（如Maven Multi-module Project），按业务领域或技术分层拆分模块。
   * 经典三层架构:
     * controller/web: 处理用户请求，调用Service层。
     * service/biz: 业务逻辑层，封装核心业务流程。
     * dao/repository: 数据访问层，负责与数据库交互。
     * model/domain/entity: 实体类，数据模型。
     * util/common: 通用工具类、常量等。
     * config: 配置类。
     * exception: 自定义异常类。
 * 包命名: 遵循反域名命名规则，例如 com.yourcompany.projectname.module.layer。
   * com.yourcompany.projectname.controller
   * com.yourcompany.projectname.service
   * com.yourcompany.projectname.dao
   * com.yourcompany.projectname.model
   * com.yourcompany.projectname.util
2. 命名规范 (Naming Conventions)
一致且有意义的命名是代码自解释性的关键。
 * 包 (Packages): 全小写，使用.分隔，反域名命名。例如：com.example.project.module。
 * 类/接口 (Classes/Interfaces): 大驼峰命名法 (PascalCase)，名词。例如：UserService, OrderProcessor, PaymentGateway。
 * 方法 (Methods): 小驼峰命名法 (camelCase)，动词或动宾短语。例如：getUserById(), processOrder(), calculateTotalPrice()。
 * 变量 (Variables): 小驼峰命名法 (camelCase)。
   * 局部变量：userName, orderCount。
   * 成员变量：private String userName;。
   * 常量：全大写，单词间用下划线分隔。例如：public static final int MAX_RETRIES = 3;。
 * 枚举 (Enums): 大驼峰命名法，枚举值全大写。例如：OrderStatus.PENDING, PaymentStatus.SUCCESS。
 * 泛型 (Generics): 单个大写字母，通常为 T (Type), E (Element), K (Key), V (Value), N (Number), S, U。
 * 缩写: 避免过度缩写，除非是广为人知的缩写（如 URL, DAO, DTO）。
3. 编码规范 (Coding Conventions)
统一的编码风格和最佳实践。
 * 代码格式化:
   * 缩进: 统一使用4个空格进行缩进，而不是Tab。
   * 行长度: 建议每行代码不超过120个字符，超出时进行换行。
   * 大括号: 采用K&R风格（左大括号与语句在同一行）。
     public class MyClass {
    public void myMethod() {
        // ...
    }
}

   * 空格:
     * 运算符两侧加空格：int a = b + c;
     * 逗号、分号后加空格：for (int i = 0; i < 10; i++)
     * 方法参数之间加空格：myMethod(arg1, arg2)
     * 控制语句（if, for, while, switch）后加空格：if (condition)
 * 面向对象原则:
   * 单一职责原则 (SRP): 一个类或方法只负责一项职责。
   * 开放封闭原则 (OCP): 对扩展开放，对修改封闭。
   * 依赖倒置原则 (DIP): 依赖抽象而不是具体实现。
   * 接口隔离原则 (ISP): 客户端不应该依赖它不需要的接口。
   * 里氏替换原则 (LSP): 子类型必须能够替换掉它们的基类型。
 * 避免魔法数字/字符串: 使用常量或枚举代替硬编码的数字和字符串。
 * 卫语句 (Guard Clauses): 优先处理异常情况并尽早返回，减少嵌套层次。
   public void process(User user) {
    if (user == null) {
        return; // 或抛出异常
    }
    if (!user.isActive()) {
        return;
    }
    // 正常逻辑
}

 * 资源关闭: 确保数据库连接、I/O流等资源在使用后及时关闭，推荐使用 try-with-resources。
   try (FileInputStream fis = new FileInputStream("file.txt")) {
    // 使用fis
} catch (IOException e) {
    // 处理异常
}

 * 集合使用:
   * 优先使用接口类型声明集合：List<String> list = new ArrayList<>();。
   * 选择合适的集合类型（ArrayList vs LinkedList, HashMap vs TreeMap vs ConcurrentHashMap）。
 * 字符串操作:
   * 字符串拼接使用 StringBuilder 或 StringBuffer (多线程安全)。
   * 避免在循环中进行大量的字符串拼接。
 * 方法参数: 避免过多的方法参数，考虑使用DTO (Data Transfer Object) 或Builder模式。
4. 注释规范 (Commenting Conventions)
清晰、简洁、有用的注释是理解代码的重要辅助。
 * Javadoc注释: 对类、接口、方法、公共成员变量使用Javado注释。
   * 类/接口：说明其目的、作者、版本。
   * 方法：说明其功能、参数 (@param)、返回值 (@return)、可能抛出的异常 (@throws)。
   * 示例：
     /**
 * 用户服务接口，提供用户相关的业务操作。
 *
 * <AUTHOR>
 * @version 1.0
 */
public interface UserService {
    /**
     * 根据用户ID获取用户信息。
     *
     * @param userId 用户唯一标识符
     * @return 对应的用户信息，如果不存在则返回null
     * @throws IllegalArgumentException 如果userId为负数
     */
    User getUserById(Long userId);
}

 * 行内注释: 解释复杂逻辑、特殊处理或非显而易见的实现细节。
 * TODO/FIXME注释: 标记待办事项或需要修复的问题，并附上日期、责任人。
   * // TODO: [日期] [责任人] 描述待办事项
   * // FIXME: [日期] [责任人] 描述需要修复的问题
 * 避免注释废代码: 废弃的代码应直接删除，而不是注释掉。
 * 注释与代码同步: 确保注释与代码逻辑保持一致，代码修改时及时更新注释。
5. 异常处理 (Exception Handling)
健壮的异常处理是系统稳定性的保障。
 * 区分受检异常和非受检异常:
   * 受检异常 (Checked Exceptions): 表示调用者可以合理预期并处理的异常（如 IOException, SQLException）。通常在方法签名中声明，由调用者捕获处理。
   * 非受检异常 (Unchecked Exceptions): 表示程序错误（如 NullPointerException, ArrayIndexOutOfBoundsException）。通常不捕获，而是让程序崩溃以暴露问题，或在最顶层进行统一处理。
 * 不要吞噬异常: 捕获异常后，必须进行适当的处理（记录日志、向上抛出、给用户提示等），不要简单地捕获后不处理。
 * 细化异常捕获: 捕获具体的异常类型，而不是笼统地捕获 Exception。
 * 自定义异常: 对于业务异常，定义清晰的自定义异常类，包含错误码和错误信息。
   public class BusinessException extends RuntimeException {
    private int errorCode;
    // 构造函数、getter等
}

 * 异常链: 捕获低级异常后，封装成高级异常并保留原始异常信息（e.getCause()）。
 * 统一异常处理: 在Web层或AOP中实现全局异常处理器，统一处理未捕获的异常，返回友好的错误信息。
6. 日志规范 (Logging Conventions)
有效的日志记录对于问题排查和系统监控至关重要。
 * 日志框架: 统一使用SLF4J + Logback/Log4j2。
 * 日志级别:
   * TRACE: 最详细的日志，用于跟踪代码执行路径。
   * DEBUG: 调试信息，开发阶段使用。
   * INFO: 重要的业务流程信息，系统正常运行状态。
   * WARN: 潜在问题，不影响系统正常运行但需要关注。
   * ERROR: 运行时错误，影响系统正常功能。
   * FATAL: 严重错误，导致系统不可用。
 * 日志内容:
   * 包含时间戳、日志级别、线程名、类名、方法名。
   * 记录关键业务数据（但不包含敏感信息）。
   * 记录异常堆栈信息。
 * 避免循环日志: 避免在日志记录器中记录日志，可能导致无限循环。
 * 异步日志: 生产环境推荐使用异步日志，减少对应用性能的影响。
 * 日志文件管理: 配置日志按日期、大小滚动，并定期清理旧日志。
7. 数据库操作规范 (Database Operations Conventions)
高效、安全的数据库交互。
 * 使用ORM框架: 推荐使用MyBatis、Hibernate (JPA) 等ORM框架。
 * SQL语句:
   * 避免硬编码SQL: 将SQL语句放在XML文件（MyBatis）或通过ORM注解管理。
   * 参数化查询: 避免SQL注入，使用预编译语句或ORM框架的参数绑定。
   * 索引: 合理设计和使用索引，优化查询性能。
   * 事务: 明确事务边界，使用@Transactional注解或编程式事务管理，确保数据一致性。
   * 批量操作: 对于大量数据的插入、更新，使用批量操作提高效率。
 * N+1问题: 警惕并解决N+1查询问题，例如使用JOIN FETCH或批量查询。
 * 懒加载: 合理配置ORM的懒加载策略，避免不必要的查询。
 * 连接池: 使用数据库连接池（如HikariCP, Druid）管理数据库连接。
8. API 设计规范 (API Design Conventions)**
清晰、一致、易用的API设计。
 * RESTful API: 遵循RESTful原则设计Web API。
   * 资源: 使用名词表示资源，例如 /users, /orders。
   * HTTP方法: 使用标准的HTTP方法表示操作：
     * GET: 获取资源。
     * POST: 创建资源。
     * PUT: 更新资源（完整替换）。
     * PATCH: 更新资源（部分更新）。
     * DELETE: 删除资源。
   * URI: 使用复数名词，避免动词。例如：/users/{id} 而不是 /getUserById。
   * 状态码: 使用标准的HTTP状态码表示结果（200 OK, 201 Created, 204 No Content, 400 Bad Request, 401 Unauthorized, 403 Forbidden, 404 Not Found, 500 Internal Server Error）。
 * 版本控制: API版本化，例如通过URI (/v1/users) 或Header。
 * 请求/响应格式: 统一使用JSON格式。
 * 输入校验: 对所有API请求参数进行严格的输入校验。
 * 分页: 对于列表查询，提供分页参数（page, size）和返回总数。
 * 安全性: API接口应考虑认证、授权、防止CSRF/XSS等安全措施。
9. 安全规范 (Security Conventions)**
保障应用程序的安全性。
 * 输入验证: 对所有用户输入进行严格的验证和净化，防止SQL注入、XSS、CSRF等攻击。
 * 认证与授权:
   * 认证: 确认用户身份（如JWT, OAuth2, Spring Security）。
   * 授权: 确认用户是否有权限执行某个操作。
   * 密码存储: 密码必须加盐哈希存储，禁止明文存储。
 * 敏感数据保护:
   * 加密: 敏感数据（如银行卡号、身份证号）应加密存储和传输。
   * 日志: 避免在日志中记录敏感信息。
 * 会话管理: 使用安全的会话管理机制，如HTTPS、HttpOnly/Secure Cookie。
 * 依赖安全: 定期检查项目依赖的漏洞（如使用OWASP Dependency-Check）。
 * 错误信息: 避免在错误信息中暴露敏感的系统内部信息。
10. 性能优化规范 (Performance Optimization Conventions)**
提升应用程序的响应速度和吞吐量。
 * 代码层面:
   * 高效算法: 选择合适的数据结构和算法。
   * 减少对象创建: 避免在循环中频繁创建大对象。
   * 字符串操作: 使用 StringBuilder。
   * 并发编程: 合理使用线程池、并发集合。
 * 数据库层面:
   * 索引优化: 确保常用查询字段有索引。
   * 慢查询优化: 定期分析和优化慢查询SQL。
   * 批量操作: 批量插入、更新、删除。
   * 缓存: 使用Redis、Ehcache等缓存技术减少数据库访问。
 * 网络层面:
   * 减少网络请求: 合并请求，减少HTTP/RPC调用次数。
   * 数据压缩: 传输数据时进行压缩。
   * CDN: 静态资源使用CDN。
 * JVM优化:
   * GC调优: 根据应用特点选择合适的GC算法和参数。
   * 内存分析: 使用JProfiler、VisualVM等工具进行内存分析，查找内存泄漏。
 * 异步/非阻塞: 对于耗时操作，考虑使用异步编程或非阻塞I/O。
11. 测试规范 (Testing Conventions)**
确保代码质量和功能正确性。
 * 单元测试 (Unit Tests):
   * 使用JUnit、Mockito等框架。
   * 测试粒度小，只测试单个类或方法的功能。
   * 覆盖率要求：核心业务逻辑单元测试覆盖率应达到较高水平（例如80%以上）。
   * 测试用例独立、可重复、快速执行。
 * 集成测试 (Integration Tests):
   * 测试多个模块或组件之间的交互。
   * 使用Spring Boot Test等框架。
   * 可能涉及真实数据库或外部服务（使用测试替身或嵌入式服务）。
 * 功能测试/端到端测试 (Functional/E2E Tests):
   * 模拟用户行为，测试整个系统的功能。
   * 可以使用Selenium、Cypress等工具。
 * 性能测试 (Performance Tests):
   * 使用JMeter、Gatling等工具进行负载测试、压力测试。
 * 测试数据: 准备充分的测试数据，包括边界值、异常数据。
 * 持续集成 (CI): 将自动化测试集成到CI/CD流程中，每次代码提交后自动运行测试。
12. 版本控制规范 (Version Control Conventions)**
高效协作和代码管理。
 * Git: 统一使用Git作为版本控制工具。
 * 分支策略: 推荐使用Git Flow或GitHub Flow。
   * master/main: 生产环境稳定代码。
   * develop: 开发主分支。
   * feature/xxx: 功能开发分支。
   * bugfix/xxx: Bug修复分支。
   * release/xxx: 发布分支。
   * hotfix/xxx: 紧急热修复分支。
 * 提交信息 (Commit Messages):
   * 清晰、简洁、有意义。
   * 第一行是简短的总结（不超过50字符）。
   * 空一行。
   * 后续行详细描述提交内容、原因、解决了什么问题。
   * 遵循Conventional Commits规范（可选但推荐）。
     * feat: add new user registration feature
     * fix: resolve NullPointerException in login
     * docs: update README with new setup instructions
 * 代码合并: 优先使用rebase保持提交历史整洁，或者使用merge --no-ff保留合并记录。
13. 依赖管理 (Dependency Management)**
管理项目依赖，避免冲突和冗余。
 * Maven/Gradle: 统一使用Maven或Gradle管理项目依赖。
 * 版本统一:
   * 在Maven中使用<properties>统一管理依赖版本。
   * 在Gradle中使用ext块或version catalog。
   * 避免不同模块使用相同库的不同版本，可能导致冲突。
 * 依赖范围: 合理使用Maven的依赖范围（compile, provided, runtime, test, system, import）。
 * 排除传递性依赖: 当出现依赖冲突时，使用<exclusions>排除不需要的传递性依赖。
 * 最小化依赖: 只引入项目真正需要的依赖，减少包大小和潜在冲突。
 * 定期更新依赖: 定期检查并更新依赖到最新稳定版本，获取新特性和安全修复。
14. 文档规范 (Documentation Conventions)**
良好的文档是项目知识传承的关键。
 * 项目文档:
   * README.md: 项目简介、如何构建、如何运行、主要功能、技术栈。
   * 设计文档: 架构设计、模块设计、数据库设计、接口设计。
   * 部署文档: 部署流程、环境配置。
   * 用户手册: 如果有面向用户的部分。
 * API文档:
   * 使用Swagger/OpenAPI生成API文档。
   * 提供API接口的详细说明、参数、返回值、示例。
 * 代码注释: 遵循Javado注释规范（如前所述）。
 * Wiki/Confluence: 维护项目知识库、常见问题、技术选型决策。
 * 文档更新: 确保文档与代码同步更新。
15. 代码审查 (Code Review)**
提升代码质量和团队协作。
 * 强制执行: 每次代码合并到主分支前，必须经过至少一名同事的审查。
 * 审查内容:
   * 功能正确性: 是否满足需求。
   * 代码风格: 是否符合编码规范。
   * 可读性/可维护性: 代码是否易于理解和修改。
   * 性能: 是否存在潜在的性能问题。
   * 安全性: 是否存在安全漏洞。
   * 错误处理: 异常处理是否完善。
   * 测试覆盖: 是否有足够的测试。
 * 工具辅助: 使用GitLab/GitHub Pull Request、Gerrit、SonarQube等工具辅助代码审查和静态代码分析。
 * 积极反馈: 审查者提供建设性、友好的反馈；被审查者虚心接受并改进。
遵循上述规则，不仅能提高单个项目的质量，更能培养团队成员良好的编程习惯和协作精神，为构建高质量、高可维护性的软件系统奠定坚实基础。这些规则并非一成不变，应根据项目特点、团队规模和技术栈进行适当调整和完善。