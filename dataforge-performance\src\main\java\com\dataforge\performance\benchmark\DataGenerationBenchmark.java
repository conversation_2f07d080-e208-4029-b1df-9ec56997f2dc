package com.dataforge.performance.benchmark;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.PerformanceConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.service.DataForgeService;
import com.dataforge.core.service.GeneratorFactory;
import com.dataforge.core.service.DataRelationManager;
import com.dataforge.core.relation.ConsistencyManager;
import com.dataforge.generators.identifier.IdCardNumberGenerator;
import com.dataforge.generators.identifier.UUIDGenerator;
import com.dataforge.generators.text.EmailGenerator;
import com.dataforge.generators.text.NameGenerator;
import com.dataforge.generators.text.PhoneGenerator;
import com.dataforge.generators.numeric.AgeGenerator;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;

import java.util.concurrent.TimeUnit;

/**
 * 数据生成性能基准测试
 * 
 * 使用JMH测试各种数据生成器的性能表现
 */
@BenchmarkMode(Mode.Throughput)
@OutputTimeUnit(TimeUnit.SECONDS)
@State(Scope.Benchmark)
@Fork(1)
@Warmup(iterations = 3, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 2, timeUnit = TimeUnit.SECONDS)
public class DataGenerationBenchmark {

    private DataForgeService dataForgeService;
    private GeneratorFactory generatorFactory;
    private GenerationContext context;
    
    // 各种生成器实例
    private IdCardNumberGenerator idCardGenerator;
    private UUIDGenerator uuidGenerator;
    private EmailGenerator emailGenerator;
    private NameGenerator nameGenerator;
    private PhoneGenerator phoneGenerator;
    private AgeGenerator ageGenerator;

    @Setup(Level.Trial)
    public void setupTrial() {
        // 初始化服务
        generatorFactory = new GeneratorFactory();
        DataRelationManager relationManager = new DataRelationManager();
        ConsistencyManager consistencyManager = new ConsistencyManager();
        dataForgeService = new DataForgeService(generatorFactory, relationManager, consistencyManager);
        
        // 初始化生成上下文
        context = new GenerationContext.Builder()
                .withSeed(12345L)
                .build();
        
        // 初始化各种生成器
        idCardGenerator = new IdCardNumberGenerator();
        uuidGenerator = new UUIDGenerator();
        emailGenerator = new EmailGenerator();
        nameGenerator = new NameGenerator();
        phoneGenerator = new PhoneGenerator();
        ageGenerator = new AgeGenerator();
    }

    /**
     * 身份证号生成性能测试
     */
    @Benchmark
    public void benchmarkIdCardGeneration(Blackhole bh) {
        String idCard = idCardGenerator.generate(context);
        bh.consume(idCard);
    }

    /**
     * UUID生成性能测试
     */
    @Benchmark
    public void benchmarkUUIDGeneration(Blackhole bh) {
        String uuid = uuidGenerator.generate(context);
        bh.consume(uuid);
    }

    /**
     * 邮箱生成性能测试
     */
    @Benchmark
    public void benchmarkEmailGeneration(Blackhole bh) {
        String email = emailGenerator.generate(context);
        bh.consume(email);
    }

    /**
     * 姓名生成性能测试
     */
    @Benchmark
    public void benchmarkNameGeneration(Blackhole bh) {
        String name = nameGenerator.generate(context);
        bh.consume(name);
    }

    /**
     * 电话号码生成性能测试
     */
    @Benchmark
    public void benchmarkPhoneGeneration(Blackhole bh) {
        String phone = phoneGenerator.generate(context);
        bh.consume(phone);
    }

    /**
     * 年龄生成性能测试
     */
    @Benchmark
    public void benchmarkAgeGeneration(Blackhole bh) {
        Integer age = ageGenerator.generate(context);
        bh.consume(age);
    }

    /**
     * 批量数据生成性能测试 - 小批量
     */
    @Benchmark
    public void benchmarkSmallBatchGeneration(Blackhole bh) {
        GenerationConfig config = createBenchmarkConfig("idcard", 100);
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 批量数据生成性能测试 - 中等批量
     */
    @Benchmark
    public void benchmarkMediumBatchGeneration(Blackhole bh) {
        GenerationConfig config = createBenchmarkConfig("email", 1000);
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 批量数据生成性能测试 - 大批量
     */
    @Benchmark
    public void benchmarkLargeBatchGeneration(Blackhole bh) {
        GenerationConfig config = createBenchmarkConfig("uuid", 5000);
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 并行数据生成性能测试
     */
    @Benchmark
    public void benchmarkParallelGeneration(Blackhole bh) {
        GenerationConfig config = createBenchmarkConfig("name", 2000);
        
        // 启用并行处理
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(4);
        perfConfig.setBatchSize(500);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 复杂数据类型生成性能测试
     */
    @Benchmark
    public void benchmarkComplexDataGeneration(Blackhole bh) {
        // 测试身份证号这种复杂的生成逻辑
        GenerationConfig config = createBenchmarkConfig("idcard", 500);
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 创建基准测试配置
     */
    private GenerationConfig createBenchmarkConfig(String dataType, int count) {
        GenerationConfig config = new GenerationConfig();
        config.setDataType(dataType);
        config.setCount(count);
        config.setSeed(12345L);
        
        // 禁用验证以专注于生成性能
        ValidationConfig validationConfig = new ValidationConfig();
        validationConfig.setEnabled(false);
        config.setValidationConfig(validationConfig);
        
        // 设置基本性能配置
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(false); // 默认串行，除非特别指定
        perfConfig.setBatchSize(1000);
        config.setPerformanceConfig(perfConfig);
        
        return config;
    }
}