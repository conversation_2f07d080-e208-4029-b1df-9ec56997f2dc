package com.dataforge.cli;

import com.dataforge.core.model.GenerationConfig;
import org.apache.commons.cli.ParseException;

/**
 * 简单的主入口类，用于测试CLI功能
 * 不依赖Spring Boot，直接测试CLI组件
 */
public class SimpleMain {

    public static void main(String[] args) {
        try {
            // 创建CLI接口
            CommandLineInterface cli = new CommandLineInterface();
            HelpFormatter helpFormatter = new HelpFormatter();

            // 检查帮助请求
            if (args.length == 0 || cli.isHelpRequested(args)) {
                helpFormatter.printHelp();
                System.exit(0);
            }

            // 检查版本请求
            if (cli.isVersionRequested(args)) {
                helpFormatter.printVersion();
                System.exit(0);
            }

            // 解析参数
            GenerationConfig config = cli.parseArguments(args);

            // 显示解析结果
            System.out.println("参数解析成功:");
            System.out.println("数据类型: " + config.getDataType());
            System.out.println("生成数量: " + config.getCount());
            System.out.println("输出格式: " + config.getOutputConfig().getFormat());

            if (config.getOutputConfig().getFile() != null) {
                System.out.println("输出文件: " + config.getOutputConfig().getFile());
            }

            System.out.println("CLI参数解析测试完成！");

        } catch (ParseException e) {
            System.err.println("参数解析错误: " + e.getMessage());
            System.err.println("使用 --help 查看帮助信息");
            System.exit(1);
        } catch (Exception e) {
            System.err.println("执行失败: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }
}