package com.dataforge.core.relation;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ConsistencyManager 单元测试
 */
class ConsistencyManagerTest {

    private ConsistencyManager consistencyManager;

    @BeforeEach
    void setUp() {
        consistencyManager = new ConsistencyManager();
    }

    @Test
    void testIdCardAgeConsistency() {
        // 测试一致的数据
        Map<String, Object> consistentData = new HashMap<>();
        consistentData.put("idcard", "110101199001011234");
        consistentData.put("age", 34); // 2024年计算，1990年出生应该是34岁左右

        ConsistencyResult result = consistencyManager.checkConsistency(consistentData);
        assertTrue(result.isConsistent());
    }

    @Test
    void testIdCardAgeInconsistency() {
        // 测试不一致的数据
        Map<String, Object> inconsistentData = new HashMap<>();
        inconsistentData.put("idcard", "110101199001011234");
        inconsistentData.put("age", 20); // 明显错误的年龄

        ConsistencyResult result = consistencyManager.checkConsistency(inconsistentData);
        assertFalse(result.isConsistent());
        assertTrue(result.hasViolations());
    }

    @Test
    void testIdCardGenderConsistency() {
        // 测试性别一致性
        Map<String, Object> testData = new HashMap<>();
        testData.put("idcard", "110101199001011234"); // 末位是4，偶数，应该是女性
        testData.put("gender", "FEMALE");

        ConsistencyResult result = consistencyManager.checkConsistency(testData);
        assertTrue(result.isConsistent());
    }

    @Test
    void testIdCardGenderInconsistency() {
        // 测试性别不一致
        Map<String, Object> testData = new HashMap<>();
        testData.put("idcard", "110101199001011234"); // 末位是4，偶数，应该是女性
        testData.put("gender", "MALE"); // 设置为男性，不一致

        ConsistencyResult result = consistencyManager.checkConsistency(testData);
        assertFalse(result.isConsistent());
        assertTrue(result.hasViolations());
    }

    @Test
    void testConsistencyFix() {
        // 测试一致性修复
        Map<String, Object> testData = new HashMap<>();
        testData.put("idcard", "110101199001011234");
        testData.put("age", 20); // 错误的年龄
        testData.put("gender", "MALE"); // 错误的性别

        ConsistencyFixResult fixResult = consistencyManager.fixConsistency(testData);

        assertTrue(fixResult.isFixed());
        assertTrue(fixResult.getAppliedFixCount() > 0);

        // 验证修复后的值
        Map<String, Object> fixedValues = fixResult.getFixedValues();
        assertNotNull(fixedValues.get("age"));
        assertEquals("FEMALE", fixedValues.get("gender"));
    }

    @Test
    void testEmptyDataConsistency() {
        // 测试空数据
        Map<String, Object> emptyData = new HashMap<>();

        ConsistencyResult result = consistencyManager.checkConsistency(emptyData);
        assertTrue(result.isConsistent());
    }

    @Test
    void testNullDataConsistency() {
        // 测试null数据
        ConsistencyResult result = consistencyManager.checkConsistency(null);
        assertTrue(result.isConsistent());
    }

    @Test
    void testPartialDataConsistency() {
        // 测试部分数据（缺少某些字段）
        Map<String, Object> partialData = new HashMap<>();
        partialData.put("idcard", "110101199001011234");
        // 缺少age和gender字段

        ConsistencyResult result = consistencyManager.checkConsistency(partialData);
        assertTrue(result.isConsistent()); // 缺少字段时应该跳过检查
    }

    @Test
    void testInvalidIdCardFormat() {
        // 测试无效的身份证号格式
        Map<String, Object> testData = new HashMap<>();
        testData.put("idcard", "12345"); // 无效格式
        testData.put("age", 25);

        ConsistencyResult result = consistencyManager.checkConsistency(testData);
        assertTrue(result.isConsistent()); // 无效格式时应该跳过检查
    }

    @Test
    void testConsistencyManagerState() {
        // 测试一致性管理器的状态
        assertFalse(consistencyManager.isEmpty());
        assertTrue(consistencyManager.getRuleCount() > 0);

        // 测试获取所有规则
        Map<String, ConsistencyRule> allRules = consistencyManager.getAllConsistencyRules();
        assertFalse(allRules.isEmpty());

        // 验证默认规则是否存在
        assertTrue(allRules.containsKey("idcard_age_consistency"));
        assertTrue(allRules.containsKey("idcard_gender_consistency"));
    }

    @Test
    void testFieldDependencies() {
        // 测试字段依赖关系
        assertTrue(consistencyManager.hasFieldDependencies("idcard"));
        assertTrue(consistencyManager.hasFieldDependencies("age"));
        assertTrue(consistencyManager.hasFieldDependencies("gender"));

        // 获取字段依赖
        var idCardDeps = consistencyManager.getFieldDependencies("idcard");
        assertFalse(idCardDeps.isEmpty());
        assertTrue(idCardDeps.contains("age") || idCardDeps.contains("gender"));
    }

    @Test
    void testCustomConsistencyRule() {
        // 测试自定义一致性规则
        ConsistencyRule customRule = new ConsistencyRule() {
            @Override
            public String getRuleName() {
                return "test_rule";
            }

            @Override
            public String getDescription() {
                return "测试规则";
            }

            @Override
            public java.util.Set<String> getInvolvedFields() {
                return java.util.Set.of("field1", "field2");
            }

            @Override
            public int getPriority() {
                return 10;
            }

            @Override
            public ConsistencyResult check(Map<String, Object> fieldValues) {
                return ConsistencyResult.success("测试通过");
            }

            @Override
            public boolean canFix() {
                return false;
            }

            @Override
            public ConsistencyFixResult fix(Map<String, Object> fieldValues) {
                return ConsistencyFixResult.noActionNeeded("无需修复");
            }
        };

        // 注册自定义规则
        consistencyManager.registerConsistencyRule(customRule);

        // 验证规则已注册
        assertTrue(consistencyManager.getAllConsistencyRules().containsKey("test_rule"));

        // 移除规则
        ConsistencyRule removed = consistencyManager.removeConsistencyRule("test_rule");
        assertNotNull(removed);
        assertEquals("test_rule", removed.getRuleName());

        // 验证规则已移除
        assertFalse(consistencyManager.getAllConsistencyRules().containsKey("test_rule"));
    }
}