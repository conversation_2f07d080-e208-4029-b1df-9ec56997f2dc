import com.dataforge.core.service.DataRelationManager;
import com.dataforge.core.relation.FieldRelation;
import com.dataforge.core.relation.RelationResult;
import com.dataforge.core.relation.RelationType;

import java.util.Set;

/**
 * 数据关联管理系统演示程序
 * 
 * 演示如何使用DataRelationManager和FieldRelation来管理数据字段之间的关联关系。
 */
public class RelationDemo {
    
    public static void main(String[] args) {
        System.out.println("=== DataForge 数据关联管理系统演示 ===\n");
        
        // 创建数据关联管理器
        DataRelationManager relationManager = new DataRelationManager();
        
        // 演示1：身份证号关联
        demonstrateIdCardRelation(relationManager);
        
        // 演示2：姓名关联
        demonstrateNameRelation(relationManager);
        
        // 演示3：自定义关联规则
        demonstrateCustomRelation(relationManager);
        
        // 演示4：多字段关联链
        demonstrateRelationChain(relationManager);
    }
    
    /**
     * 演示身份证号关联
     */
    private static void demonstrateIdCardRelation(DataRelationManager relationManager) {
        System.out.println("1. 身份证号关联演示");
        System.out.println("-------------------");
        
        // 设置身份证号，自动触发关联字段更新
        String idCard = "110101199001011234"; // 1990年1月1日出生的男性
        relationManager.setRelatedValue("idcard", idCard);
        
        // 获取关联的字段值
        System.out.println("身份证号: " + idCard);
        System.out.println("出生日期: " + relationManager.getRelatedValue("birth_date"));
        System.out.println("性别: " + relationManager.getRelatedValue("gender"));
        System.out.println("年龄: " + relationManager.getRelatedValue("age"));
        System.out.println("地区代码: " + relationManager.getRelatedValue("region_code"));
        System.out.println();
    }
    
    /**
     * 演示姓名关联
     */
    private static void demonstrateNameRelation(DataRelationManager relationManager) {
        System.out.println("2. 姓名关联演示");
        System.out.println("---------------");
        
        // 设置姓名，自动触发关联字段更新
        String name = "张三";
        relationManager.setRelatedValue("name", name);
        
        // 获取关联的字段值
        System.out.println("姓名: " + name);
        System.out.println("姓氏: " + relationManager.getRelatedValue("surname"));
        System.out.println("名字: " + relationManager.getRelatedValue("given_name"));
        System.out.println("拼音: " + relationManager.getRelatedValue("name_pinyin"));
        System.out.println("邮箱用户名: " + relationManager.getRelatedValue("email_username"));
        System.out.println();
    }
    
    /**
     * 演示自定义关联规则
     */
    private static void demonstrateCustomRelation(DataRelationManager relationManager) {
        System.out.println("3. 自定义关联规则演示");
        System.out.println("-------------------");
        
        // 创建自定义关联规则：将输入转换为大写
        FieldRelation customRelation = FieldRelation.builder()
                .source("input_text")
                .targets(Set.of("upper_text", "lower_text", "length"))
                .type(RelationType.CUSTOM)
                .function(value -> {
                    if (value == null) {
                        return RelationResult.failure("输入为空");
                    }
                    
                    String text = value.toString();
                    return RelationResult.success(Map.of(
                        "upper_text", text.toUpperCase(),
                        "lower_text", text.toLowerCase(),
                        "length", text.length()
                    ));
                })
                .description("文本格式转换")
                .build();
        
        // 注册自定义关联规则
        relationManager.registerFieldRelation(customRelation);
        
        // 测试自定义关联
        String inputText = "Hello World";
        relationManager.setRelatedValue("input_text", inputText);
        
        System.out.println("输入文本: " + inputText);
        System.out.println("大写: " + relationManager.getRelatedValue("upper_text"));
        System.out.println("小写: " + relationManager.getRelatedValue("lower_text"));
        System.out.println("长度: " + relationManager.getRelatedValue("length"));
        System.out.println();
    }
    
    /**
     * 演示多字段关联链
     */
    private static void demonstrateRelationChain(DataRelationManager relationManager) {
        System.out.println("4. 多字段关联链演示");
        System.out.println("-----------------");
        
        // 清空之前的数据
        relationManager.clearAll();
        
        // 设置多个相关字段
        relationManager.setRelatedValue("name", "李四");
        relationManager.setRelatedValue("age", 28);
        relationManager.setRelatedValue("idcard", "110101199601011234");
        
        // 显示所有关联的字段
        System.out.println("设置的字段:");
        System.out.println("- 姓名: 李四");
        System.out.println("- 年龄: 28");
        System.out.println("- 身份证号: 110101199601011234");
        
        System.out.println("\n自动生成的关联字段:");
        relationManager.getAllRelatedValues().forEach((key, value) -> {
            if (!Set.of("name", "age", "idcard").contains(key)) {
                System.out.println("- " + key + ": " + value);
            }
        });
        
        System.out.println("\n关联管理器状态: " + relationManager);
    }
}