package com.dataforge.core.validation;

/**
 * 校验规则接口
 * 
 * 定义数据校验规则的标准接口。
 * 所有校验规则实现都必须实现此接口。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface ValidationRule {

    /**
     * 获取规则名称
     * 
     * @return 规则名称
     */
    String getRuleName();

    /**
     * 获取规则描述
     * 
     * @return 规则描述
     */
    String getDescription();

    /**
     * 获取支持的数据类型
     * 
     * @return 数据类型
     */
    String getSupportedDataType();

    /**
     * 校验数据
     * 
     * @param value 待校验的数据值
     * @return 校验结果
     */
    ValidationResult validate(Object value);

    /**
     * 检查是否支持指定的数据类型
     * 
     * @param dataType 数据类型
     * @return 如果支持返回true，否则返回false
     */
    default boolean supports(String dataType) {
        return getSupportedDataType().equals(dataType);
    }

    /**
     * 获取规则优先级
     * 数值越小优先级越高
     * 
     * @return 优先级
     */
    default int getPriority() {
        return 100;
    }

    /**
     * 检查是否启用
     * 
     * @return 如果启用返回true，否则返回false
     */
    default boolean isEnabled() {
        return true;
    }
}