package com.dataforge.core.cache;

import com.github.benmanes.caffeine.cache.Cache;
import com.github.benmanes.caffeine.cache.Caffeine;
import com.github.benmanes.caffeine.cache.stats.CacheStats;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 缓存管理器
 * 
 * 管理多个缓存实例，提供统一的缓存操作接口和配置管理。
 * 使用Caffeine作为底层缓存实现，支持过期策略、大小限制和统计信息。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class CacheManager {

    private static final Logger logger = LoggerFactory.getLogger(CacheManager.class);

    private final Map<String, Cache<String, Object>> caches = new ConcurrentHashMap<>();
    private final CacheConfig defaultConfig;

    /**
     * 构造函数
     * 
     * @param defaultConfig 默认缓存配置
     */
    public CacheManager(CacheConfig defaultConfig) {
        this.defaultConfig = defaultConfig;
        logger.info("Cache manager initialized with default config: {}", defaultConfig);
    }

    /**
     * 使用默认配置的构造函数
     */
    public CacheManager() {
        this(CacheConfig.defaultConfig());
    }

    /**
     * 获取或创建缓存
     * 
     * @param cacheName 缓存名称
     * @return 缓存实例
     */
    public Cache<String, Object> getCache(String cacheName) {
        return getCache(cacheName, defaultConfig);
    }

    /**
     * 获取或创建缓存（使用指定配置）
     * 
     * @param cacheName 缓存名称
     * @param config    缓存配置
     * @return 缓存实例
     */
    public Cache<String, Object> getCache(String cacheName, CacheConfig config) {
        return caches.computeIfAbsent(cacheName, name -> {
            logger.info("Creating new cache: {} with config: {}", name, config);
            return createCache(config);
        });
    }

    /**
     * 创建缓存实例
     */
    private Cache<String, Object> createCache(CacheConfig config) {
        Caffeine<Object, Object> builder = Caffeine.newBuilder();

        // 设置最大大小
        if (config.getMaximumSize() > 0) {
            builder.maximumSize(config.getMaximumSize());
        }

        // 设置过期时间
        if (config.getExpireAfterWrite() != null) {
            builder.expireAfterWrite(config.getExpireAfterWrite());
        }
        if (config.getExpireAfterAccess() != null) {
            builder.expireAfterAccess(config.getExpireAfterAccess());
        }

        // 设置刷新时间
        if (config.getRefreshAfterWrite() != null) {
            builder.refreshAfterWrite(config.getRefreshAfterWrite());
        }

        // 启用统计
        if (config.isRecordStats()) {
            builder.recordStats();
        }

        // 设置移除监听器
        builder.removalListener((key, value, cause) -> {
            logger.debug("Cache entry removed: key={}, cause={}", key, cause);
        });

        return builder.build();
    }

    /**
     * 从缓存获取值
     * 
     * @param cacheName 缓存名称
     * @param key       键
     * @param type      值类型
     * @param <T>       值类型
     * @return 缓存值，如果不存在返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String cacheName, String key, Class<T> type) {
        Cache<String, Object> cache = getCache(cacheName);
        Object value = cache.getIfPresent(key);

        if (value != null && type.isInstance(value)) {
            return (T) value;
        }

        return null;
    }

    /**
     * 从缓存获取值，如果不存在则使用加载函数加载
     * 
     * @param cacheName 缓存名称
     * @param key       键
     * @param type      值类型
     * @param loader    加载函数
     * @param <T>       值类型
     * @return 缓存值
     */
    @SuppressWarnings("unchecked")
    public <T> T get(String cacheName, String key, Class<T> type, Function<String, T> loader) {
        Cache<String, Object> cache = getCache(cacheName);

        Object value = cache.get(key, k -> {
            T loadedValue = loader.apply(k);
            logger.debug("Loaded value for cache: {}, key: {}", cacheName, k);
            return loadedValue;
        });

        if (value != null && type.isInstance(value)) {
            return (T) value;
        }

        return null;
    }

    /**
     * 向缓存放入值
     * 
     * @param cacheName 缓存名称
     * @param key       键
     * @param value     值
     */
    public void put(String cacheName, String key, Object value) {
        Cache<String, Object> cache = getCache(cacheName);
        cache.put(key, value);
        logger.debug("Put value to cache: {}, key: {}", cacheName, key);
    }

    /**
     * 从缓存移除值
     * 
     * @param cacheName 缓存名称
     * @param key       键
     */
    public void remove(String cacheName, String key) {
        Cache<String, Object> cache = getCache(cacheName);
        cache.invalidate(key);
        logger.debug("Removed value from cache: {}, key: {}", cacheName, key);
    }

    /**
     * 清空指定缓存
     * 
     * @param cacheName 缓存名称
     */
    public void clear(String cacheName) {
        Cache<String, Object> cache = caches.get(cacheName);
        if (cache != null) {
            cache.invalidateAll();
            logger.info("Cleared cache: {}", cacheName);
        }
    }

    /**
     * 清空所有缓存
     */
    public void clearAll() {
        caches.forEach((name, cache) -> {
            cache.invalidateAll();
            logger.info("Cleared cache: {}", name);
        });
        logger.info("All caches cleared");
    }

    /**
     * 获取缓存统计信息
     * 
     * @param cacheName 缓存名称
     * @return 缓存统计信息，如果缓存不存在或未启用统计返回null
     */
    public CacheStats getStats(String cacheName) {
        Cache<String, Object> cache = caches.get(cacheName);
        return cache != null ? cache.stats() : null;
    }

    /**
     * 获取所有缓存的统计信息
     * 
     * @return 缓存统计信息映射
     */
    public Map<String, CacheStats> getAllStats() {
        Map<String, CacheStats> statsMap = new ConcurrentHashMap<>();
        caches.forEach((name, cache) -> {
            CacheStats stats = cache.stats();
            if (stats != null) {
                statsMap.put(name, stats);
            }
        });
        return statsMap;
    }

    /**
     * 获取缓存大小
     * 
     * @param cacheName 缓存名称
     * @return 缓存大小
     */
    public long size(String cacheName) {
        Cache<String, Object> cache = caches.get(cacheName);
        return cache != null ? cache.estimatedSize() : 0;
    }

    /**
     * 检查缓存是否存在
     * 
     * @param cacheName 缓存名称
     * @return 是否存在
     */
    public boolean exists(String cacheName) {
        return caches.containsKey(cacheName);
    }

    /**
     * 获取所有缓存名称
     * 
     * @return 缓存名称集合
     */
    public java.util.Set<String> getCacheNames() {
        return caches.keySet();
    }

    /**
     * 执行缓存清理
     * 清理过期的缓存条目
     */
    public void cleanup() {
        caches.forEach((name, cache) -> {
            cache.cleanUp();
            logger.debug("Cleaned up cache: {}", name);
        });
    }

    /**
     * 预热缓存
     * 
     * @param cacheName 缓存名称
     * @param data      预热数据
     */
    public void warmUp(String cacheName, Map<String, Object> data) {
        if (data == null || data.isEmpty()) {
            return;
        }

        Cache<String, Object> cache = getCache(cacheName);
        cache.putAll(data);

        logger.info("Warmed up cache: {} with {} entries", cacheName, data.size());
    }

    /**
     * 关闭缓存管理器
     */
    public void shutdown() {
        logger.info("Shutting down cache manager");

        // 清理所有缓存
        cleanup();

        // 清空缓存映射
        caches.clear();

        logger.info("Cache manager shutdown completed");
    }
}