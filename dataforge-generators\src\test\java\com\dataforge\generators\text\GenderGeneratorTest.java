package com.dataforge.generators.text;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * 性别生成器单元测试
 */
@DisplayName("性别生成器测试")
class GenderGeneratorTest {

    private GenderGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new GenderGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext(parameters, 12345L);
    }

    @Test
    @DisplayName("基本性别生成测试")
    void testBasicGeneration() {
        String gender = generator.generate(context);

        assertThat(gender).isNotNull();
        assertThat(gender).isIn("男", "女", "male", "female", "M", "F");
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证有效性")
    void testRepeatedGeneration() {
        String gender = generator.generate(context);

        assertThat(gender).isNotNull();
        assertThat(generator.validate(gender)).isTrue();
    }

    @Test
    @DisplayName("指定输出格式生成测试")
    void testGenerationWithFormat() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("format", "CHINESE");
        GenerationContext contextChinese = new GenerationContext(parameters, 12345L);

        String gender = generator.generate(contextChinese);

        assertThat(gender).isNotNull();
        assertThat(gender).isIn("男", "女");
        assertThat(generator.validate(gender)).isTrue();

        // 测试英文格式
        parameters.put("format", "ENGLISH");
        GenerationContext contextEnglish = new GenerationContext(parameters, 12345L);

        gender = generator.generate(contextEnglish);
        assertThat(gender).isIn("male", "female");
        assertThat(generator.validate(gender)).isTrue();

        // 测试简写格式
        parameters.put("format", "SHORT");
        GenerationContext contextShort = new GenerationContext(parameters, 12345L);

        gender = generator.generate(contextShort);
        assertThat(gender).isIn("M", "F");
        assertThat(generator.validate(gender)).isTrue();
    }

    @Test
    @DisplayName("指定性别比例生成测试")
    void testGenerationWithRatio() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("maleRatio", 0.7); // 70%男性
        parameters.put("format", "ENGLISH");
        GenerationContext contextWithRatio = new GenerationContext(parameters, 12345L);

        int maleCount = 0;
        int totalCount = 1000;

        for (int i = 0; i < totalCount; i++) {
            String gender = generator.generate(contextWithRatio);
            if ("male".equals(gender)) {
                maleCount++;
            }
        }

        double actualRatio = (double) maleCount / totalCount;
        // 允许5%的误差范围
        assertThat(actualRatio).isBetween(0.65, 0.75);
    }

    @Test
    @DisplayName("性别校验测试 - 有效值")
    void testValidationWithValidGender() {
        String[] validGenders = {
                "男", "女", // 中文
                "male", "female", // 英文
                "M", "F" // 简写
        };

        for (String gender : validGenders) {
            assertThat(generator.validate(gender)).isTrue();
        }
    }

    @Test
    @DisplayName("性别校验测试 - 无效值")
    void testValidationWithInvalidGender() {
        String[] invalidGenders = {
                "unknown",
                "其他",
                "X",
                "1",
                "0",
                "",
                "   "
        };

        for (String gender : invalidGenders) {
            assertThat(generator.validate(gender)).isFalse();
        }

        // 测试null值
        assertThat(generator.validate(null)).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("gender");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        List<GeneratorParameter> parameters = generator.getSupportedParameters();

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("format", "maleRatio");
    }

    @Test
    @DisplayName("生成数据格式一致性测试")
    void testGeneratedDataConsistency() {
        for (int i = 0; i < 100; i++) {
            String gender = generator.generate(context);
            assertThat(generator.validate(gender)).isTrue();
        }
    }

    @Test
    @DisplayName("不同格式输出测试")
    void testDifferentFormats() {
        String[] formats = { "CHINESE", "ENGLISH", "SHORT" };
        String[][] expectedValues = {
                { "男", "女" },
                { "male", "female" },
                { "M", "F" }
        };

        for (int i = 0; i < formats.length; i++) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("format", formats[i]);
            GenerationContext formatContext = new GenerationContext(parameters, 12345L);

            String gender = generator.generate(formatContext);
            assertThat(gender).isIn((Object[]) expectedValues[i]);
            assertThat(generator.validate(gender)).isTrue();
        }
    }

    @Test
    @DisplayName("性别分布测试")
    void testGenderDistribution() {
        Map<String, Integer> genderCount = new HashMap<>();
        int totalCount = 1000;

        for (int i = 0; i < totalCount; i++) {
            String gender = generator.generate(context);
            genderCount.put(gender, genderCount.getOrDefault(gender, 0) + 1);
        }

        // 验证生成了两种性别
        assertThat(genderCount.size()).isEqualTo(2);

        // 验证分布相对均匀（允许一定的随机性）
        for (Integer count : genderCount.values()) {
            assertThat(count).isBetween(400, 600); // 40%-60%的范围
        }
    }

    @Test
    @DisplayName("极端比例测试")
    void testExtremeRatios() {
        // 测试100%男性
        Map<String, Object> allMaleParams = new HashMap<>();
        allMaleParams.put("maleRatio", 1.0);
        allMaleParams.put("format", "ENGLISH");
        GenerationContext allMaleContext = new GenerationContext(allMaleParams, 12345L);

        for (int i = 0; i < 100; i++) {
            String gender = generator.generate(allMaleContext);
            assertThat(gender).isEqualTo("male");
        }

        // 测试0%男性（100%女性）
        Map<String, Object> allFemaleParams = new HashMap<>();
        allFemaleParams.put("maleRatio", 0.0);
        allFemaleParams.put("format", "ENGLISH");
        GenerationContext allFemaleContext = new GenerationContext(allFemaleParams, 12345L);

        for (int i = 0; i < 100; i++) {
            String gender = generator.generate(allFemaleContext);
            assertThat(gender).isEqualTo("female");
        }
    }

    @Test
    @DisplayName("大小写敏感性测试")
    void testCaseSensitivity() {
        // 测试校验是否区分大小写
        assertThat(generator.validate("MALE")).isFalse();
        assertThat(generator.validate("FEMALE")).isFalse();
        assertThat(generator.validate("m")).isFalse();
        assertThat(generator.validate("f")).isFalse();
    }

    @Test
    @DisplayName("边界值测试")
    void testBoundaryValues() {
        // 测试边界比例值
        double[] ratios = { 0.0, 0.1, 0.5, 0.9, 1.0 };

        for (double ratio : ratios) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("maleRatio", ratio);
            parameters.put("format", "SHORT");
            GenerationContext ratioContext = new GenerationContext(parameters, 12345L);

            String gender = generator.generate(ratioContext);
            assertThat(gender).isIn("M", "F");
            assertThat(generator.validate(gender)).isTrue();
        }
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("format", "INVALID_FORMAT");
        parameters.put("maleRatio", -0.5); // 无效比例
        GenerationContext invalidContext = new GenerationContext(parameters, 12345L);

        // 应该能够处理无效参数并生成有效的性别
        String gender = generator.generate(invalidContext);
        assertThat(gender).isNotNull();
        assertThat(generator.validate(gender)).isTrue();
    }

    @Test
    @DisplayName("性能测试")
    void testPerformance() {
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 100000; i++) {
            String gender = generator.generate(context);
            assertThat(gender).isNotNull();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertThat(duration).isLessThan(1000); // 应该在1秒内完成
    }

    @Test
    @DisplayName("线程安全测试")
    void testThreadSafety() throws InterruptedException {
        int threadCount = 10;
        int iterationsPerThread = 1000;
        Thread[] threads = new Thread[threadCount];

        for (int i = 0; i < threadCount; i++) {
            threads[i] = new Thread(() -> {
                for (int j = 0; j < iterationsPerThread; j++) {
                    String gender = generator.generate(context);
                    assertThat(generator.validate(gender)).isTrue();
                }
            });
            threads[i].start();
        }

        for (Thread thread : threads) {
            thread.join();
        }
    }

    @Test
    @DisplayName("数值比例精度测试")
    void testRatioPrecision() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("maleRatio", 0.333); // 33.3%
        parameters.put("format", "SHORT");
        GenerationContext precisionContext = new GenerationContext(parameters, 12345L);

        int maleCount = 0;
        int totalCount = 10000;

        for (int i = 0; i < totalCount; i++) {
            String gender = generator.generate(precisionContext);
            if ("M".equals(gender)) {
                maleCount++;
            }
        }

        double actualRatio = (double) maleCount / totalCount;
        // 允许2%的误差范围
        assertThat(actualRatio).isBetween(0.313, 0.353);
    }
}