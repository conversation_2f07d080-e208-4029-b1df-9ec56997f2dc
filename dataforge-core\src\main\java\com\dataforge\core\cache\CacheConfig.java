package com.dataforge.core.cache;

import java.time.Duration;

/**
 * 缓存配置类
 * 
 * 定义缓存的各种配置参数，包括大小限制、过期策略、统计开关等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class CacheConfig {

    private long maximumSize = 1000;
    private Duration expireAfterWrite;
    private Duration expireAfterAccess;
    private Duration refreshAfterWrite;
    private boolean recordStats = true;

    /**
     * 默认构造函数
     */
    public CacheConfig() {
    }

    /**
     * 创建默认配置
     * 
     * @return 默认缓存配置
     */
    public static CacheConfig defaultConfig() {
        CacheConfig config = new CacheConfig();
        config.setMaximumSize(1000);
        config.setExpireAfterWrite(Duration.ofHours(1));
        config.setExpireAfterAccess(Duration.ofMinutes(30));
        config.setRecordStats(true);
        return config;
    }

    /**
     * 创建小型缓存配置
     * 
     * @return 小型缓存配置
     */
    public static CacheConfig smallConfig() {
        CacheConfig config = new CacheConfig();
        config.setMaximumSize(100);
        config.setExpireAfterWrite(Duration.ofMinutes(30));
        config.setExpireAfterAccess(Duration.ofMinutes(15));
        config.setRecordStats(true);
        return config;
    }

    /**
     * 创建大型缓存配置
     * 
     * @return 大型缓存配置
     */
    public static CacheConfig largeConfig() {
        CacheConfig config = new CacheConfig();
        config.setMaximumSize(10000);
        config.setExpireAfterWrite(Duration.ofHours(4));
        config.setExpireAfterAccess(Duration.ofHours(2));
        config.setRecordStats(true);
        return config;
    }

    /**
     * 创建长期缓存配置
     * 
     * @return 长期缓存配置
     */
    public static CacheConfig longTermConfig() {
        CacheConfig config = new CacheConfig();
        config.setMaximumSize(5000);
        config.setExpireAfterWrite(Duration.ofDays(1));
        config.setExpireAfterAccess(Duration.ofHours(6));
        config.setRecordStats(true);
        return config;
    }

    /**
     * 创建无过期缓存配置
     * 
     * @return 无过期缓存配置
     */
    public static CacheConfig neverExpireConfig() {
        CacheConfig config = new CacheConfig();
        config.setMaximumSize(2000);
        // 不设置过期时间
        config.setRecordStats(true);
        return config;
    }

    // Getters and Setters

    public long getMaximumSize() {
        return maximumSize;
    }

    public CacheConfig setMaximumSize(long maximumSize) {
        this.maximumSize = maximumSize;
        return this;
    }

    public Duration getExpireAfterWrite() {
        return expireAfterWrite;
    }

    public CacheConfig setExpireAfterWrite(Duration expireAfterWrite) {
        this.expireAfterWrite = expireAfterWrite;
        return this;
    }

    public Duration getExpireAfterAccess() {
        return expireAfterAccess;
    }

    public CacheConfig setExpireAfterAccess(Duration expireAfterAccess) {
        this.expireAfterAccess = expireAfterAccess;
        return this;
    }

    public Duration getRefreshAfterWrite() {
        return refreshAfterWrite;
    }

    public CacheConfig setRefreshAfterWrite(Duration refreshAfterWrite) {
        this.refreshAfterWrite = refreshAfterWrite;
        return this;
    }

    public boolean isRecordStats() {
        return recordStats;
    }

    public CacheConfig setRecordStats(boolean recordStats) {
        this.recordStats = recordStats;
        return this;
    }

    @Override
    public String toString() {
        return String.format(
                "CacheConfig{maxSize=%d, expireAfterWrite=%s, expireAfterAccess=%s, refreshAfterWrite=%s, recordStats=%s}",
                maximumSize, expireAfterWrite, expireAfterAccess, refreshAfterWrite, recordStats);
    }
}