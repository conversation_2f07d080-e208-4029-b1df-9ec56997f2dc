package com.dataforge.generators.numeric;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.util.Random;

/**
 * 年龄生成器
 * 
 * 在指定范围内生成年龄数据，支持不同的分布模式。
 * 可以与其他字段建立关联，确保数据的逻辑一致性。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class AgeGenerator extends AbstractDataGenerator<Integer> {

    private static final String TYPE = "age";
    private static final String DESCRIPTION = "生成年龄数据";

    private final Random random;

    /**
     * 构造函数
     */
    public AgeGenerator() {
        this.random = new Random();
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("minAge", Integer.class, 18,
                "最小年龄，默认18岁", false));
        addParameter(new GeneratorParameter("maxAge", Integer.class, 65,
                "最大年龄，默认65岁", false));
        addParameter(new GeneratorParameter("distribution", String.class, "uniform",
                "分布模式：uniform（均匀分布）、normal（正态分布）、young（偏年轻）、old（偏年长）", false));
        addParameter(new GeneratorParameter("meanAge", Integer.class, 35,
                "正态分布的平均年龄，仅在distribution为normal时有效", false));
        addParameter(new GeneratorParameter("stdDev", Double.class, 10.0,
                "正态分布的标准差，仅在distribution为normal时有效", false));
    }

    @Override
    protected Integer doGenerate(GenerationContext context) {
        // 获取参数
        Integer minAge = context.getParameter("minAge", 18);
        Integer maxAge = context.getParameter("maxAge", 65);
        String distribution = context.getParameter("distribution", "uniform");
        Integer meanAge = context.getParameter("meanAge", 35);
        Double stdDev = context.getParameter("stdDev", 10.0);

        // 参数验证
        if (minAge >= maxAge) {
            throw new IllegalArgumentException("最小年龄必须小于最大年龄");
        }

        // 根据分布模式生成年龄
        int age;
        switch (distribution) {
            case "normal":
                age = generateNormalDistribution(meanAge, stdDev, minAge, maxAge);
                break;
            case "young":
                age = generateYoungBiased(minAge, maxAge);
                break;
            case "old":
                age = generateOldBiased(minAge, maxAge);
                break;
            case "uniform":
            default:
                age = generateUniform(minAge, maxAge);
                break;
        }

        // 将生成的年龄设置到关联管理器中，触发关联字段的自动生成
        context.getRelationManager().setRelatedValue("age", age);

        return age;
    }

    /**
     * 生成均匀分布的年龄
     * 
     * @param minAge 最小年龄
     * @param maxAge 最大年龄
     * @return 生成的年龄
     */
    private int generateUniform(int minAge, int maxAge) {
        return minAge + random.nextInt(maxAge - minAge + 1);
    }

    /**
     * 生成正态分布的年龄
     * 
     * @param mean   平均值
     * @param stdDev 标准差
     * @param minAge 最小年龄
     * @param maxAge 最大年龄
     * @return 生成的年龄
     */
    private int generateNormalDistribution(int mean, double stdDev, int minAge, int maxAge) {
        int age;
        int attempts = 0;
        do {
            double gaussian = random.nextGaussian();
            age = (int) Math.round(mean + gaussian * stdDev);
            attempts++;

            // 防止无限循环
            if (attempts > 100) {
                age = generateUniform(minAge, maxAge);
                break;
            }
        } while (age < minAge || age > maxAge);

        return age;
    }

    /**
     * 生成偏年轻的年龄分布
     * 
     * @param minAge 最小年龄
     * @param maxAge 最大年龄
     * @return 生成的年龄
     */
    private int generateYoungBiased(int minAge, int maxAge) {
        // 使用Beta分布的近似，偏向较小值
        double beta = Math.pow(random.nextDouble(), 2.0);
        return minAge + (int) (beta * (maxAge - minAge + 1));
    }

    /**
     * 生成偏年长的年龄分布
     * 
     * @param minAge 最小年龄
     * @param maxAge 最大年龄
     * @return 生成的年龄
     */
    private int generateOldBiased(int minAge, int maxAge) {
        // 使用Beta分布的近似，偏向较大值
        double beta = 1.0 - Math.pow(random.nextDouble(), 2.0);
        return minAge + (int) (beta * (maxAge - minAge + 1));
    }

    @Override
    public ValidationResult validateWithDetails(Integer data) {
        if (data == null) {
            return ValidationResult.error("年龄不能为空");
        }

        if (data < 0) {
            return ValidationResult.error("年龄不能为负数");
        }

        if (data > 150) {
            return ValidationResult.error("年龄不能超过150岁");
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }
}