package com.dataforge.core.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 字段配置
 * 
 * 定义单个字段的生成配置，包括字段名称、数据类型、参数等。
 * 用于多字段生成模式。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class FieldConfig {

    @JsonProperty("name")
    private String name;

    @JsonProperty("type")
    private String type;

    @JsonProperty("parameters")
    private Map<String, Object> parameters = new HashMap<>();

    @JsonProperty("required")
    private boolean required = true;

    @JsonProperty("description")
    private String description;

    /**
     * 默认构造函数
     */
    public FieldConfig() {
    }

    /**
     * 构造函数
     * 
     * @param name 字段名称
     * @param type 数据类型
     */
    public FieldConfig(String name, String type) {
        this.name = name;
        this.type = type;
    }

    /**
     * 构造函数
     * 
     * @param name       字段名称
     * @param type       数据类型
     * @param parameters 参数
     */
    public FieldConfig(String name, String type, Map<String, Object> parameters) {
        this.name = name;
        this.type = type;
        this.parameters = parameters != null ? parameters : new HashMap<>();
    }

    // Getters and Setters

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters != null ? parameters : new HashMap<>();
    }

    public boolean isRequired() {
        return required;
    }

    public void setRequired(boolean required) {
        this.required = required;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * 添加参数
     * 
     * @param key   参数键
     * @param value 参数值
     */
    public void addParameter(String key, Object value) {
        if (key != null && value != null) {
            this.parameters.put(key, value);
        }
    }

    /**
     * 获取参数值
     * 
     * @param key 参数键
     * @return 参数值，如果不存在返回null
     */
    public Object getParameter(String key) {
        return parameters.get(key);
    }

    /**
     * 获取参数值并转换为指定类型
     * 
     * @param key  参数键
     * @param type 目标类型
     * @param <T>  类型参数
     * @return 转换后的参数值，如果不存在或转换失败返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, Class<T> type) {
        Object value = parameters.get(key);
        if (value == null) {
            return null;
        }

        if (type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }

        return null;
    }

    /**
     * 验证字段配置的有效性
     * 
     * @return 验证结果
     */
    public ValidationResult validate() {
        ValidationResult result = new ValidationResult();

        if (name == null || name.trim().isEmpty()) {
            result.addError("字段名称不能为空");
        }

        if (type == null || type.trim().isEmpty()) {
            result.addError("字段类型不能为空");
        }

        // 验证字段名称格式
        if (name != null && !name.matches("^[a-zA-Z][a-zA-Z0-9_]*$")) {
            result.addError("字段名称格式无效，必须以字母开头，只能包含字母、数字和下划线");
        }

        return result;
    }

    /**
     * 创建字段配置的副本
     * 
     * @return 字段配置副本
     */
    public FieldConfig copy() {
        FieldConfig copy = new FieldConfig();
        copy.name = this.name;
        copy.type = this.type;
        copy.parameters = new HashMap<>(this.parameters);
        copy.required = this.required;
        copy.description = this.description;
        return copy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        FieldConfig that = (FieldConfig) o;
        return required == that.required &&
                Objects.equals(name, that.name) &&
                Objects.equals(type, that.type) &&
                Objects.equals(parameters, that.parameters) &&
                Objects.equals(description, that.description);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, type, parameters, required, description);
    }

    @Override
    public String toString() {
        return String.format("FieldConfig{name='%s', type='%s', required=%s, parametersCount=%d}",
                name, type, required, parameters.size());
    }
}