package com.dataforge.core.validation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.regex.Pattern;

/**
 * 校验规则工厂
 * 
 * 提供常见的数据校验规则创建方法。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ValidationRuleFactory {

    // 暂时禁用logger以避免Logback兼容性问题
    // private static final Logger logger = LoggerFactory.getLogger(ValidationRuleFactory.class);

    // 常用正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    private static final Pattern PHONE_PATTERN = Pattern.compile(
            "^1[3-9]\\d{9}$");

    /**
     * 创建身份证号校验规则
     * 
     * @return 身份证号校验规则
     */
    public ValidationRule createIdCardValidationRule() {
        return new ValidationRule() {
            @Override
            public String getRuleName() {
                return "idcard";
            }

            @Override
            public String getDescription() {
                return "中国大陆18位身份证号校验";
            }

            @Override
            public String getSupportedDataType() {
                return "idcard";
            }

            @Override
            public ValidationResult validate(Object value) {
                if (value == null) {
                    return ValidationResult.failure("身份证号不能为空");
                }

                String idCard = value.toString().trim();

                // 长度检查
                if (idCard.length() != 18) {
                    return ValidationResult.failure("身份证号长度必须为18位", "LENGTH_ERROR", value);
                }

                // 格式检查
                if (!idCard.matches("^\\d{17}[\\dXx]$")) {
                    return ValidationResult.failure("身份证号格式不正确", "FORMAT_ERROR", value);
                }

                // 校验位检查
                if (!isValidIdCardChecksum(idCard)) {
                    return ValidationResult.failure("身份证号校验位不正确", "CHECKSUM_ERROR", value);
                }

                return ValidationResult.success("身份证号校验通过", value);
            }

            private boolean isValidIdCardChecksum(String idCard) {
                try {
                    int[] weights = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 };
                    char[] checksums = { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' };

                    int sum = 0;
                    for (int i = 0; i < 17; i++) {
                        sum += Character.getNumericValue(idCard.charAt(i)) * weights[i];
                    }

                    char expectedChecksum = checksums[sum % 11];
                    char actualChecksum = Character.toUpperCase(idCard.charAt(17));

                    return expectedChecksum == actualChecksum;
                } catch (Exception e) {
                    // 暂时禁用日志以避免Logback兼容性问题
                    // logger.warn("身份证号校验位计算失败: {}", idCard, e);
                    return false;
                }
            }
        };
    }

    /**
     * 创建银行卡号校验规则
     * 
     * @return 银行卡号校验规则
     */
    public ValidationRule createBankCardValidationRule() {
        return new ValidationRule() {
            @Override
            public String getRuleName() {
                return "bankcard";
            }

            @Override
            public String getDescription() {
                return "银行卡号Luhn算法校验";
            }

            @Override
            public String getSupportedDataType() {
                return "bankcard";
            }

            @Override
            public ValidationResult validate(Object value) {
                if (value == null) {
                    return ValidationResult.failure("银行卡号不能为空");
                }

                String cardNumber = value.toString().trim().replaceAll("\\s+", "");

                // 长度检查
                if (cardNumber.length() < 13 || cardNumber.length() > 19) {
                    return ValidationResult.failure("银行卡号长度必须在13-19位之间", "LENGTH_ERROR", value);
                }

                // 格式检查
                if (!cardNumber.matches("^\\d+$")) {
                    return ValidationResult.failure("银行卡号只能包含数字", "FORMAT_ERROR", value);
                }

                // Luhn算法校验
                if (!isValidLuhn(cardNumber)) {
                    return ValidationResult.failure("银行卡号校验失败", "LUHN_ERROR", value);
                }

                return ValidationResult.success("银行卡号校验通过", value);
            }

            private boolean isValidLuhn(String cardNumber) {
                try {
                    int sum = 0;
                    boolean alternate = false;

                    for (int i = cardNumber.length() - 1; i >= 0; i--) {
                        int digit = Character.getNumericValue(cardNumber.charAt(i));

                        if (alternate) {
                            digit *= 2;
                            if (digit > 9) {
                                digit = digit % 10 + digit / 10;
                            }
                        }

                        sum += digit;
                        alternate = !alternate;
                    }

                    return sum % 10 == 0;
                } catch (Exception e) {
                    // 暂时禁用日志以避免Logback兼容性问题
                    // logger.warn("银行卡号Luhn校验失败: {}", cardNumber, e);
                    return false;
                }
            }
        };
    }

    /**
     * 创建统一社会信用代码校验规则
     * 
     * @return 统一社会信用代码校验规则
     */
    public ValidationRule createUSCCValidationRule() {
        return new ValidationRule() {
            @Override
            public String getRuleName() {
                return "uscc";
            }

            @Override
            public String getDescription() {
                return "统一社会信用代码校验";
            }

            @Override
            public String getSupportedDataType() {
                return "uscc";
            }

            @Override
            public ValidationResult validate(Object value) {
                if (value == null) {
                    return ValidationResult.failure("统一社会信用代码不能为空");
                }

                String uscc = value.toString().trim().toUpperCase();

                // 长度检查
                if (uscc.length() != 18) {
                    return ValidationResult.failure("统一社会信用代码长度必须为18位", "LENGTH_ERROR", value);
                }

                // 格式检查
                if (!uscc.matches("^[0-9A-HJ-NPQRTUWXY]{18}$")) {
                    return ValidationResult.failure("统一社会信用代码格式不正确", "FORMAT_ERROR", value);
                }

                // 校验位检查
                if (!isValidUSCCChecksum(uscc)) {
                    return ValidationResult.failure("统一社会信用代码校验位不正确", "CHECKSUM_ERROR", value);
                }

                return ValidationResult.success("统一社会信用代码校验通过", value);
            }

            private boolean isValidUSCCChecksum(String uscc) {
                try {
                    String chars = "0123456789ABCDEFGHJKLMNPQRTUWXY";
                    int[] weights = { 1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28 };

                    int sum = 0;
                    for (int i = 0; i < 17; i++) {
                        char c = uscc.charAt(i);
                        int value = chars.indexOf(c);
                        if (value == -1) {
                            return false;
                        }
                        sum += value * weights[i];
                    }

                    int remainder = sum % 31;
                    char expectedChecksum = chars.charAt(31 - remainder);
                    char actualChecksum = uscc.charAt(17);

                    return expectedChecksum == actualChecksum;
                } catch (Exception e) {
                    // 暂时禁用日志以避免Logback兼容性问题
                    // logger.warn("统一社会信用代码校验位计算失败: {}", uscc, e);
                    return false;
                }
            }
        };
    }

    /**
     * 创建邮箱校验规则
     * 
     * @return 邮箱校验规则
     */
    public ValidationRule createEmailValidationRule() {
        return new ValidationRule() {
            @Override
            public String getRuleName() {
                return "email";
            }

            @Override
            public String getDescription() {
                return "邮箱地址格式校验";
            }

            @Override
            public String getSupportedDataType() {
                return "email";
            }

            @Override
            public ValidationResult validate(Object value) {
                if (value == null) {
                    return ValidationResult.failure("邮箱地址不能为空");
                }

                String email = value.toString().trim();

                // 长度检查
                if (email.length() > 254) {
                    return ValidationResult.failure("邮箱地址长度不能超过254个字符", "LENGTH_ERROR", value);
                }

                // 格式检查
                if (!EMAIL_PATTERN.matcher(email).matches()) {
                    return ValidationResult.failure("邮箱地址格式不正确", "FORMAT_ERROR", value);
                }

                return ValidationResult.success("邮箱地址校验通过", value);
            }
        };
    }

    /**
     * 创建手机号校验规则
     * 
     * @return 手机号校验规则
     */
    public ValidationRule createPhoneValidationRule() {
        return new ValidationRule() {
            @Override
            public String getRuleName() {
                return "phone";
            }

            @Override
            public String getDescription() {
                return "中国大陆手机号校验";
            }

            @Override
            public String getSupportedDataType() {
                return "phone";
            }

            @Override
            public ValidationResult validate(Object value) {
                if (value == null) {
                    return ValidationResult.failure("手机号不能为空");
                }

                String phone = value.toString().trim();

                // 长度检查
                if (phone.length() != 11) {
                    return ValidationResult.failure("手机号长度必须为11位", "LENGTH_ERROR", value);
                }

                // 格式检查
                if (!PHONE_PATTERN.matcher(phone).matches()) {
                    return ValidationResult.failure("手机号格式不正确", "FORMAT_ERROR", value);
                }

                return ValidationResult.success("手机号校验通过", value);
            }
        };
    }
}