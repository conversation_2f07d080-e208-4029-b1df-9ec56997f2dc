package com.dataforge.core.output.impl;

import com.dataforge.core.output.AbstractOutputWriter;
import com.dataforge.core.output.OutputConfig;

import java.io.IOException;
import java.io.PrintStream;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 控制台输出器
 * 
 * 将数据以表格格式输出到控制台，支持自动列宽调整和分页显示。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ConsoleOutputWriter extends AbstractOutputWriter {

    private static final String TYPE = "console";

    private PrintStream output;
    private List<String> headers;
    private List<Map<String, Object>> bufferedRows;
    private int maxRowsPerPage;
    private boolean enablePaging;

    /**
     * 构造函数
     */
    public ConsoleOutputWriter() {
        this.output = System.out;
        this.headers = new ArrayList<>();
        this.bufferedRows = new ArrayList<>();
        this.maxRowsPerPage = 50;
        this.enablePaging = false;
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    protected void doInitialize(OutputConfig config) throws Exception {
        // 获取配置选项
        this.maxRowsPerPage = config.getFormatOption("maxRowsPerPage", 50);
        this.enablePaging = config.getFormatOption("enablePaging", false);

        // 如果目标是stderr，使用标准错误输出
        if ("stderr".equalsIgnoreCase(config.getTarget())) {
            this.output = System.err;
        }
    }

    @Override
    protected void doWriteHeader(List<String> headers) throws Exception {
        this.headers = new ArrayList<>(headers);

        if (config.isIncludeHeader()) {
            printTableHeader();
        }
    }

    @Override
    protected void doWriteRecord(Map<String, Object> record) throws Exception {
        bufferedRows.add(record);

        // 如果启用分页且达到页面大小，输出当前页
        if (enablePaging && bufferedRows.size() >= maxRowsPerPage) {
            printBufferedRows();
            bufferedRows.clear();

            // 等待用户输入继续
            if (recordCount.get() < maxRowsPerPage) {
                waitForUserInput();
            }
        }
    }

    @Override
    protected void doFlush() throws Exception {
        if (!bufferedRows.isEmpty()) {
            printBufferedRows();
            bufferedRows.clear();
        }
        output.flush();
    }

    @Override
    protected void doClose() throws Exception {
        // 输出统计信息
        if (recordCount.get() > 0) {
            output.println();
            output.println("总计: " + recordCount.get() + " 条记录");
        }
    }

    /**
     * 打印表格头部
     */
    private void printTableHeader() {
        if (headers.isEmpty()) {
            return;
        }

        Map<String, Integer> columnWidths = calculateColumnWidths();

        // 打印分隔线
        printSeparatorLine(columnWidths);

        // 打印表头
        output.print("|");
        for (String header : headers) {
            int width = columnWidths.get(header);
            output.printf(" %-" + width + "s |", header);
        }
        output.println();

        // 打印分隔线
        printSeparatorLine(columnWidths);
    }

    /**
     * 打印缓冲的行数据
     */
    private void printBufferedRows() {
        if (bufferedRows.isEmpty()) {
            return;
        }

        Map<String, Integer> columnWidths = calculateColumnWidths();

        // 如果没有打印过表头，先打印表头
        if (config.isIncludeHeader() && recordCount.get() == bufferedRows.size()) {
            printTableHeader();
        }

        // 打印数据行
        for (Map<String, Object> row : bufferedRows) {
            output.print("|");
            for (String header : headers) {
                int width = columnWidths.get(header);
                Object value = row.get(header);
                String strValue = value != null ? value.toString() : "";
                output.printf(" %-" + width + "s |", strValue);
            }
            output.println();
        }

        // 打印底部分隔线
        printSeparatorLine(columnWidths);
    }

    /**
     * 计算列宽度
     * 
     * @return 列宽度映射
     */
    private Map<String, Integer> calculateColumnWidths() {
        Map<String, Integer> widths = new HashMap<>();

        // 初始化为表头宽度
        for (String header : headers) {
            widths.put(header, header.length());
        }

        // 计算数据行的最大宽度
        for (Map<String, Object> row : bufferedRows) {
            for (String header : headers) {
                Object value = row.get(header);
                if (value != null) {
                    int valueLength = value.toString().length();
                    widths.put(header, Math.max(widths.get(header), valueLength));
                }
            }
        }

        // 设置最小和最大宽度限制
        for (String header : headers) {
            int width = widths.get(header);
            width = Math.max(width, 8); // 最小宽度
            width = Math.min(width, 50); // 最大宽度
            widths.put(header, width);
        }

        return widths;
    }

    /**
     * 打印分隔线
     * 
     * @param columnWidths 列宽度映射
     */
    private void printSeparatorLine(Map<String, Integer> columnWidths) {
        output.print("+");
        for (String header : headers) {
            int width = columnWidths.get(header);
            for (int i = 0; i < width + 2; i++) {
                output.print("-");
            }
            output.print("+");
        }
        output.println();
    }

    /**
     * 等待用户输入继续
     */
    private void waitForUserInput() {
        try {
            output.print("按回车键继续...");
            System.in.read();
        } catch (IOException e) {
            // 忽略异常，继续执行
        }
    }
}