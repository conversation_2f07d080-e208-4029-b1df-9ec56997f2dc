package com.dataforge.core.relation;

import java.util.Objects;
import java.util.Set;
import java.util.function.Function;

/**
 * 字段关联定义
 * 
 * 定义数据字段之间的关联关系，包括源字段、目标字段和关联规则。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class FieldRelation {

    private final String sourceField;
    private final Set<String> targetFields;
    private final RelationType relationType;
    private final Function<Object, RelationResult> relationFunction;
    private final String description;

    /**
     * 构造函数
     * 
     * @param sourceField      源字段名
     * @param targetFields     目标字段名集合
     * @param relationType     关联类型
     * @param relationFunction 关联函数
     * @param description      关联描述
     */
    public FieldRelation(String sourceField, Set<String> targetFields,
            RelationType relationType, Function<Object, RelationResult> relationFunction,
            String description) {
        this.sourceField = Objects.requireNonNull(sourceField, "源字段不能为空");
        this.targetFields = Objects.requireNonNull(targetFields, "目标字段不能为空");
        this.relationType = Objects.requireNonNull(relationType, "关联类型不能为空");
        this.relationFunction = Objects.requireNonNull(relationFunction, "关联函数不能为空");
        this.description = description != null ? description : "";
    }

    /**
     * 执行关联规则
     * 
     * @param sourceValue 源字段值
     * @return 关联结果
     */
    public RelationResult execute(Object sourceValue) {
        try {
            return relationFunction.apply(sourceValue);
        } catch (Exception e) {
            return RelationResult.failure("执行关联规则失败: " + e.getMessage());
        }
    }

    /**
     * 获取源字段名
     * 
     * @return 源字段名
     */
    public String getSourceField() {
        return sourceField;
    }

    /**
     * 获取目标字段名集合
     * 
     * @return 目标字段名集合
     */
    public Set<String> getTargetFields() {
        return targetFields;
    }

    /**
     * 获取关联类型
     * 
     * @return 关联类型
     */
    public RelationType getRelationType() {
        return relationType;
    }

    /**
     * 获取关联描述
     * 
     * @return 关联描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 检查是否影响指定字段
     * 
     * @param fieldName 字段名
     * @return 如果影响返回true，否则返回false
     */
    public boolean affects(String fieldName) {
        return targetFields.contains(fieldName);
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        FieldRelation that = (FieldRelation) o;
        return Objects.equals(sourceField, that.sourceField) &&
                Objects.equals(targetFields, that.targetFields) &&
                relationType == that.relationType;
    }

    @Override
    public int hashCode() {
        return Objects.hash(sourceField, targetFields, relationType);
    }

    @Override
    public String toString() {
        return String.format("FieldRelation{source='%s', targets=%s, type=%s, description='%s'}",
                sourceField, targetFields, relationType, description);
    }

    /**
     * 创建构建器
     * 
     * @return 构建器实例
     */
    public static Builder builder() {
        return new Builder();
    }

    /**
     * 字段关联构建器
     */
    public static class Builder {
        private String sourceField;
        private Set<String> targetFields;
        private RelationType relationType;
        private Function<Object, RelationResult> relationFunction;
        private String description;

        /**
         * 设置源字段
         * 
         * @param sourceField 源字段名
         * @return 构建器实例
         */
        public Builder source(String sourceField) {
            this.sourceField = sourceField;
            return this;
        }

        /**
         * 设置目标字段
         * 
         * @param targetFields 目标字段名集合
         * @return 构建器实例
         */
        public Builder targets(Set<String> targetFields) {
            this.targetFields = targetFields;
            return this;
        }

        /**
         * 设置关联类型
         * 
         * @param relationType 关联类型
         * @return 构建器实例
         */
        public Builder type(RelationType relationType) {
            this.relationType = relationType;
            return this;
        }

        /**
         * 设置关联函数
         * 
         * @param relationFunction 关联函数
         * @return 构建器实例
         */
        public Builder function(Function<Object, RelationResult> relationFunction) {
            this.relationFunction = relationFunction;
            return this;
        }

        /**
         * 设置描述
         * 
         * @param description 描述
         * @return 构建器实例
         */
        public Builder description(String description) {
            this.description = description;
            return this;
        }

        /**
         * 构建字段关联
         * 
         * @return 字段关联实例
         */
        public FieldRelation build() {
            return new FieldRelation(sourceField, targetFields, relationType, relationFunction, description);
        }
    }
}