/**
 * 安全测试数据生成器包
 * 
 * 提供各种安全测试相关的数据生成器，用于安全漏洞检测、
 * 渗透测试和安全评估。
 * 
 * <h2>主要生成器：</h2>
 * <ul>
 * <li>{@link com.dataforge.generators.security.SqlInjectionPayloadGenerator} -
 * SQL注入payload生成器</li>
 * <li>XSS攻击脚本生成器（待实现）</li>
 * <li>路径穿越数据生成器（待实现）</li>
 * <li>命令注入payload生成器（待实现）</li>
 * </ul>
 * 
 * <h2>支持的攻击类型：</h2>
 * <ul>
 * <li>SQL注入（Union、Boolean、Time、Error、Stacked、Second Order）</li>
 * <li>跨站脚本攻击（XSS）</li>
 * <li>路径穿越攻击</li>
 * <li>命令注入攻击</li>
 * <li>LDAP注入攻击</li>
 * <li>XML注入攻击</li>
 * </ul>
 * 
 * <h2>支持的数据库：</h2>
 * <ul>
 * <li>MySQL</li>
 * <li>PostgreSQL</li>
 * <li>Microsoft SQL Server</li>
 * <li>Oracle</li>
 * <li>SQLite</li>
 * <li>通用SQL</li>
 * </ul>
 * 
 * <h2>编码支持：</h2>
 * <ul>
 * <li>URL编码</li>
 * <li>双重URL编码</li>
 * <li>十六进制编码</li>
 * <li>Unicode编码</li>
 * <li>Base64编码</li>
 * </ul>
 * 
 * <h2>使用示例：</h2>
 * 
 * <pre>{@code
 * // 创建SQL注入payload生成器
 * SqlInjectionPayloadGenerator generator = new SqlInjectionPayloadGenerator();
 * 
 * // 配置生成参数
 * GenerationContext context = new GenerationContext.Builder()
 *         .withParameter("injectionType", SqlInjectionPayloadGenerator.InjectionType.UNION_BASED)
 *         .withParameter("databaseType", SqlInjectionPayloadGenerator.DatabaseType.MYSQL)
 *         .withParameter("encodingType", SqlInjectionPayloadGenerator.EncodingType.URL)
 *         .withParameter("complexity", 2)
 *         .build();
 * 
 * // 生成payload
 * String payload = generator.generate(context);
 * System.out.println("SQL Injection Payload: " + payload);
 * }</pre>
 * 
 * <h2>安全注意事项：</h2>
 * <ul>
 * <li>⚠️ 本包生成的数据仅用于合法的安全测试</li>
 * <li>⚠️ 禁止用于非法攻击或恶意用途</li>
 * <li>⚠️ 使用前请确保获得适当的授权</li>
 * <li>⚠️ 建议在隔离的测试环境中使用</li>
 * </ul>
 * 
 * <h2>最佳实践：</h2>
 * <ul>
 * <li>在受控环境中进行测试</li>
 * <li>记录所有测试活动</li>
 * <li>及时修复发现的漏洞</li>
 * <li>定期更新payload库</li>
 * <li>遵循负责任的披露原则</li>
 * </ul>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
package com.dataforge.generators.security;