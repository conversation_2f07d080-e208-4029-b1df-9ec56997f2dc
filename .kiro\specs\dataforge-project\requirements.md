# DataForge 项目需求文档

## 介绍

DataForge 是一款高效、灵活且高度可配置的测试数据生成工具，专注于为软件测试团队提供高质量、真实且多样化的测试数据。该项目采用分层单体架构，使用 Java 语言开发，提供命令行接口（CLI），支持多种数据类型生成和输出格式。

## 需求

### 需求 1 - 核心数据生成引擎

**用户故事：** 作为测试工程师，我希望能够通过命令行快速生成各种类型的测试数据，以便为自动化测试和功能测试提供数据支持。

#### 验收标准

1. WHEN 用户执行命令行指令 THEN 系统 SHALL 解析参数并调用相应的数据生成器
2. WHEN 指定数据类型和数量 THEN 系统 SHALL 生成指定数量的对应类型数据
3. WHEN 生成过程中出现错误 THEN 系统 SHALL 提供清晰的错误信息和日志
4. WHEN 用户请求帮助信息 THEN 系统 SHALL 显示完整的命令使用说明

### 需求 2 - 基础信息类数据生成

**用户故事：** 作为测试工程师，我希望能够生成真实的中文姓名、手机号码、邮箱地址等基础用户信息，以便构建完整的用户测试数据集。

#### 验收标准

1. WHEN 生成中文姓名 THEN 系统 SHALL 基于真实姓氏库和名字库进行加权随机组合
2. WHEN 生成手机号码 THEN 系统 SHALL 基于中国大陆运营商号段生成11位有效号码
3. WHEN 生成邮箱地址 THEN 系统 SHALL 生成符合邮箱格式规范的地址
4. WHEN 指定性别参数 THEN 系统 SHALL 生成对应性别倾向的姓名
5. WHEN 设置年龄范围 THEN 系统 SHALL 在指定范围内生成随机年龄

### 需求 3 - 标识类数据生成

**用户故事：** 作为测试工程师，我希望能够生成符合中国标准的身份证号、银行卡号、统一社会信用代码等标识数据，以便测试涉及身份验证和金融业务的系统。

#### 验收标准

1. WHEN 生成身份证号 THEN 系统 SHALL 基于18位结构和GB 11643-1999标准生成有效号码
2. WHEN 生成银行卡号 THEN 系统 SHALL 实现Luhn算法确保校验位正确
3. WHEN 生成统一社会信用代码 THEN 系统 SHALL 实现GB32100-2015标准的校验算法
4. WHEN 指定地区代码 THEN 系统 SHALL 生成对应地区的身份证号
5. WHEN 设置无效数据标志 THEN 系统 SHALL 生成校验失败的测试数据

### 需求 4 - 数据输出管理

**用户故事：** 作为测试工程师，我希望能够将生成的数据以多种格式输出到不同目标，以便适应不同的测试场景和数据导入需求。

#### 验收标准

1. WHEN 指定CSV格式 THEN 系统 SHALL 生成符合CSV规范的文件
2. WHEN 指定JSON格式 THEN 系统 SHALL 生成格式化的JSON数据
3. WHEN 指定输出到文件 THEN 系统 SHALL 创建文件并写入数据
4. WHEN 指定输出到控制台 THEN 系统 SHALL 直接显示生成的数据
5. WHEN 生成大量数据 THEN 系统 SHALL 采用流式处理避免内存溢出

### 需求 5 - 配置管理系统

**用户故事：** 作为测试工程师，我希望能够通过配置文件定义复杂的数据生成规则，以便重复使用和批量生成复杂数据结构。

#### 验收标准

1. WHEN 提供YAML配置文件 THEN 系统 SHALL 解析并应用配置规则
2. WHEN 配置文件包含字段关联 THEN 系统 SHALL 确保生成数据的逻辑一致性
3. WHEN CLI参数与配置文件冲突 THEN 系统 SHALL 优先使用CLI参数
4. WHEN 配置文件格式错误 THEN 系统 SHALL 提供详细的错误信息
5. WHEN 保存配置模板 THEN 系统 SHALL 支持配置的保存和重用

### 需求 6 - 数据校验与质量保证

**用户故事：** 作为测试工程师，我希望生成的数据能够通过严格的校验，确保数据的真实性和有效性，同时也能生成无效数据用于异常测试。

#### 验收标准

1. WHEN 启用数据校验 THEN 系统 SHALL 对生成的数据进行格式和逻辑校验
2. WHEN 生成身份证号 THEN 系统 SHALL 验证地区代码、出生日期和校验位的正确性
3. WHEN 生成银行卡号 THEN 系统 SHALL 验证Luhn算法校验通过
4. WHEN 设置生成无效数据 THEN 系统 SHALL 故意生成校验失败的数据
5. WHEN 校验失败 THEN 系统 SHALL 在日志中标记无效数据

### 需求 7 - 性能与扩展性

**用户故事：** 作为测试工程师，我希望工具能够快速生成大量数据，并支持自定义数据类型的扩展，以便满足不同项目的特殊需求。

#### 验收标准

1. WHEN 生成10万条基础数据 THEN 系统 SHALL 在合理时间内完成（目标：<10秒）
2. WHEN 使用多线程生成 THEN 系统 SHALL 充分利用多核CPU资源
3. WHEN 生成大量数据 THEN 系统 SHALL 保持内存使用在合理范围内
4. WHEN 添加自定义生成器 THEN 系统 SHALL 通过SPI机制动态加载
5. WHEN 系统启动 THEN 系统 SHALL 在5秒内完成初始化

### 需求 8 - 安全测试数据生成

**用户故事：** 作为安全测试工程师，我希望能够生成各种安全测试payload，包括SQL注入、XSS攻击脚本等，以便进行安全漏洞测试。

#### 验收标准

1. WHEN 生成SQL注入payload THEN 系统 SHALL 支持多种数据库类型的注入语法
2. WHEN 生成XSS攻击脚本 THEN 系统 SHALL 包含常见的反射型和存储型payload
3. WHEN 生成路径穿越数据 THEN 系统 SHALL 支持Windows和Unix风格的路径
4. WHEN 生成命令注入数据 THEN 系统 SHALL 支持不同操作系统的命令分隔符
5. WHEN 启用编码选项 THEN 系统 SHALL 支持URL编码、Base64编码等绕过方式
