package com.dataforge.core.stream;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.service.GeneratorFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

/**
 * 流式数据生成服务
 * 
 * 提供统一的流式数据生成接口，支持大数据量的流式处理、
 * 背压控制和内存优化。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class StreamGenerationService {

    private static final Logger logger = LoggerFactory.getLogger(StreamGenerationService.class);

    private final GeneratorFactory generatorFactory;
    private final ConcurrentHashMap<String, StreamDataGenerator<?>> streamGenerators = new ConcurrentHashMap<>();
    private final AtomicLong totalGenerated = new AtomicLong(0);

    /**
     * 构造函数
     * 
     * @param generatorFactory 生成器工厂
     */
    public StreamGenerationService(GeneratorFactory generatorFactory) {
        this.generatorFactory = generatorFactory;
    }

    /**
     * 生成数据流
     * 
     * @param generatorType 生成器类型
     * @param context       生成上下文
     * @param count         生成数量
     * @param <T>           数据类型
     * @return 数据流
     */
    @SuppressWarnings("unchecked")
    public <T> Stream<T> generateStream(String generatorType, GenerationContext context, long count) {
        StreamDataGenerator<T> streamGenerator = (StreamDataGenerator<T>) getOrCreateStreamGenerator(generatorType);

        logger.info("Generating stream: type={}, count={}", generatorType, count);

        return streamGenerator.generateStream(context, count)
                .peek(item -> totalGenerated.incrementAndGet());
    }

    /**
     * 生成并行数据流
     * 
     * @param generatorType 生成器类型
     * @param context       生成上下文
     * @param count         生成数量
     * @param <T>           数据类型
     * @return 并行数据流
     */
    @SuppressWarnings("unchecked")
    public <T> Stream<T> generateParallelStream(String generatorType, GenerationContext context, long count) {
        StreamDataGenerator<T> streamGenerator = (StreamDataGenerator<T>) getOrCreateStreamGenerator(generatorType);

        if (!(streamGenerator instanceof AbstractStreamDataGenerator)) {
            logger.warn("Generator {} does not support parallel streams, using sequential stream", generatorType);
            return generateStream(generatorType, context, count);
        }

        logger.info("Generating parallel stream: type={}, count={}", generatorType, count);

        AbstractStreamDataGenerator<T> abstractGenerator = (AbstractStreamDataGenerator<T>) streamGenerator;
        return abstractGenerator.generateParallelStream(context, count)
                .peek(item -> totalGenerated.incrementAndGet());
    }

    /**
     * 生成无限数据流
     * 
     * @param generatorType 生成器类型
     * @param context       生成上下文
     * @param <T>           数据类型
     * @return 无限数据流
     */
    @SuppressWarnings("unchecked")
    public <T> Stream<T> generateInfiniteStream(String generatorType, GenerationContext context) {
        StreamDataGenerator<T> streamGenerator = (StreamDataGenerator<T>) getOrCreateStreamGenerator(generatorType);

        logger.info("Generating infinite stream: type={}", generatorType);

        return streamGenerator.generateInfiniteStream(context)
                .peek(item -> totalGenerated.incrementAndGet());
    }

    /**
     * 生成批次数据流
     * 
     * @param generatorType 生成器类型
     * @param context       生成上下文
     * @param batchSize     批次大小
     * @param batchCount    批次数量
     * @param <T>           数据类型
     * @return 批次数据流
     */
    @SuppressWarnings("unchecked")
    public <T> Stream<StreamDataGenerator.Batch<T>> generateBatchStream(String generatorType,
            GenerationContext context,
            int batchSize,
            long batchCount) {
        StreamDataGenerator<T> streamGenerator = (StreamDataGenerator<T>) getOrCreateStreamGenerator(generatorType);

        logger.info("Generating batch stream: type={}, batchSize={}, batchCount={}",
                generatorType, batchSize, batchCount);

        return streamGenerator.generateBatchStream(context, batchSize, batchCount)
                .peek(batch -> totalGenerated.addAndGet(batch.size()));
    }

    /**
     * 生成带缓冲的数据流
     * 
     * @param generatorType 生成器类型
     * @param context       生成上下文
     * @param count         生成数量
     * @param bufferSize    缓冲区大小
     * @param <T>           数据类型
     * @return 带缓冲的数据流
     */
    @SuppressWarnings("unchecked")
    public <T> Stream<T> generateBufferedStream(String generatorType, GenerationContext context,
            long count, int bufferSize) {
        StreamDataGenerator<T> streamGenerator = (StreamDataGenerator<T>) getOrCreateStreamGenerator(generatorType);

        if (!(streamGenerator instanceof AbstractStreamDataGenerator)) {
            logger.warn("Generator {} does not support buffered streams, using regular stream", generatorType);
            return generateStream(generatorType, context, count);
        }

        logger.info("Generating buffered stream: type={}, count={}, bufferSize={}",
                generatorType, count, bufferSize);

        AbstractStreamDataGenerator<T> abstractGenerator = (AbstractStreamDataGenerator<T>) streamGenerator;
        return abstractGenerator.generateBufferedStream(context, count, bufferSize)
                .peek(item -> totalGenerated.incrementAndGet());
    }

    /**
     * 生成带进度回调的数据流
     * 
     * @param generatorType    生成器类型
     * @param context          生成上下文
     * @param count            生成数量
     * @param progressCallback 进度回调
     * @param <T>              数据类型
     * @return 数据流
     */
    @SuppressWarnings("unchecked")
    public <T> Stream<T> generateStreamWithProgress(String generatorType, GenerationContext context,
            long count,
            AbstractStreamDataGenerator.ProgressCallback progressCallback) {
        StreamDataGenerator<T> streamGenerator = (StreamDataGenerator<T>) getOrCreateStreamGenerator(generatorType);

        if (!(streamGenerator instanceof AbstractStreamDataGenerator)) {
            logger.warn("Generator {} does not support progress tracking, using regular stream", generatorType);
            return generateStream(generatorType, context, count);
        }

        logger.info("Generating stream with progress: type={}, count={}", generatorType, count);

        AbstractStreamDataGenerator<T> abstractGenerator = (AbstractStreamDataGenerator<T>) streamGenerator;
        return abstractGenerator.generateStreamWithProgress(context, count, progressCallback)
                .peek(item -> totalGenerated.incrementAndGet());
    }

    /**
     * 获取或创建流式生成器
     * 
     * @param generatorType 生成器类型
     * @return 流式生成器
     */
    private StreamDataGenerator<?> getOrCreateStreamGenerator(String generatorType) {
        return streamGenerators.computeIfAbsent(generatorType, type -> {
            DataGenerator<?> baseGenerator = generatorFactory.getGenerator(type);
            if (baseGenerator == null) {
                throw new IllegalArgumentException("Unknown generator type: " + type);
            }

            return createStreamGenerator(baseGenerator);
        });
    }

    /**
     * 创建流式生成器
     * 
     * @param baseGenerator 基础生成器
     * @param <T>           数据类型
     * @return 流式生成器
     */
    @SuppressWarnings("unchecked")
    private <T> StreamDataGenerator<T> createStreamGenerator(DataGenerator<T> baseGenerator) {
        return new AbstractStreamDataGenerator<T>(baseGenerator) {
            @Override
            public int getRecommendedBatchSize() {
                // 根据生成器类型调整批次大小
                return switch (baseGenerator.getType()) {
                    case "name", "phone", "email" -> 2000; // 简单文本数据
                    case "idcard", "bankcard", "uscc" -> 1000; // 复杂计算数据
                    default -> 1500; // 默认批次大小
                };
            }

            @Override
            public boolean supportsParallelStream() {
                // 大部分生成器都支持并行流
                return true;
            }
        };
    }

    /**
     * 获取总生成数量
     * 
     * @return 总生成数量
     */
    public long getTotalGenerated() {
        return totalGenerated.get();
    }

    /**
     * 重置统计信息
     */
    public void resetStats() {
        totalGenerated.set(0);
        logger.info("Stream generation statistics reset");
    }

    /**
     * 获取已注册的流式生成器数量
     * 
     * @return 生成器数量
     */
    public int getRegisteredGeneratorCount() {
        return streamGenerators.size();
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        streamGenerators.clear();
        resetStats();
        logger.info("Stream generation service cleaned up");
    }
}