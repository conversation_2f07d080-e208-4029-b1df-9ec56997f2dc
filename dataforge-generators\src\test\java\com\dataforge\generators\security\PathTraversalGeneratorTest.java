package com.dataforge.generators.security;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * 路径遍历攻击生成器单元测试
 */
@DisplayName("路径遍历攻击生成器测试")
class PathTraversalGeneratorTest {

    private PathTraversalGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new PathTraversalGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext(parameters, 12345L);
    }

    @Test
    @DisplayName("基本路径遍历攻击生成测试")
    void testBasicGeneration() {
        String pathTraversal = generator.generate(context);

        assertThat(pathTraversal).isNotNull();
        assertThat(pathTraversal).isNotEmpty();
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证多样性")
    void testRepeatedGeneration() {
        String pathTraversal = generator.generate(context);

        assertThat(pathTraversal).isNotNull();
        assertThat(pathTraversal).isNotEmpty();
        assertThat(generator.validate(pathTraversal)).isTrue();
    }

    @Test
    @DisplayName("指定操作系统类型生成测试")
    void testGenerationWithOsType() {
        String[] osTypes = { "WINDOWS", "LINUX", "UNIX" };

        for (String osType : osTypes) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("osType", osType);
            GenerationContext osContext = new GenerationContext(parameters, 12345L);

            String pathTraversal = generator.generate(osContext);
            assertThat(pathTraversal).isNotNull();
            assertThat(generator.validate(pathTraversal)).isTrue();
        }
    }

    @Test
    @DisplayName("Windows路径遍历测试")
    void testWindowsPathTraversal() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("osType", "WINDOWS");
        GenerationContext windowsContext = new GenerationContext(parameters, 12345L);

        String pathTraversal = generator.generate(windowsContext);

        assertThat(pathTraversal).isNotNull();
        assertThat(pathTraversal).containsAnyOf("..\\", "..\\..\\", "C:\\");
    }

    @Test
    @DisplayName("Linux路径遍历测试")
    void testLinuxPathTraversal() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("osType", "LINUX");
        GenerationContext linuxContext = new GenerationContext(parameters, 12345L);

        String pathTraversal = generator.generate(linuxContext);

        assertThat(pathTraversal).isNotNull();
        assertThat(pathTraversal).containsAnyOf("../", "../../", "/etc/", "/var/");
    }

    @Test
    @DisplayName("指定目标文件生成测试")
    void testGenerationWithTargetFile() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("targetFile", "passwd");
        GenerationContext targetContext = new GenerationContext(parameters, 12345L);

        String pathTraversal = generator.generate(targetContext);

        assertThat(pathTraversal).isNotNull();
        assertThat(pathTraversal).contains("passwd");
    }

    @Test
    @DisplayName("指定遍历深度生成测试")
    void testGenerationWithTraversalDepth() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("depth", 5);
        GenerationContext depthContext = new GenerationContext(parameters, 12345L);

        String pathTraversal = generator.generate(depthContext);

        assertThat(pathTraversal).isNotNull();
        // 应该包含多个../或..\
        long dotDotCount = pathTraversal.chars().filter(ch -> ch == '.').count();
        assertThat(dotDotCount).isGreaterThanOrEqualTo(10); // 5层深度至少10个点
    }

    @Test
    @DisplayName("编码绕过测试")
    void testEncodingBypass() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("encoding", "URL");
        GenerationContext urlContext = new GenerationContext(parameters, 12345L);

        String pathTraversal = generator.generate(urlContext);

        assertThat(pathTraversal).isNotNull();
        // URL编码的路径应该包含%符号
        if (pathTraversal.contains("%")) {
            assertThat(pathTraversal).matches(".*%[0-9A-Fa-f]{2}.*");
        }

        // 测试双重编码
        parameters.put("encoding", "DOUBLE_URL");
        GenerationContext doubleContext = new GenerationContext(parameters, 12345L);

        pathTraversal = generator.generate(doubleContext);
        assertThat(pathTraversal).isNotNull();
    }

    @Test
    @DisplayName("路径遍历校验测试 - 有效路径")
    void testValidationWithValidPathTraversal() {
        String[] validPaths = {
                "../../../etc/passwd",
                "..\\..\\..\\windows\\system32\\config\\sam",
                "....//....//etc/passwd",
                "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd"
        };

        for (String path : validPaths) {
            assertThat(generator.validate(path)).isTrue();
        }
    }

    @Test
    @DisplayName("路径遍历校验测试 - 无效路径")
    void testValidationWithInvalidPathTraversal() {
        String[] invalidPaths = {
                "",
                "   ",
                "normal/path/file.txt",
                "relative/path"
        };

        for (String path : invalidPaths) {
            assertThat(generator.validate(path)).isFalse();
        }

        // 测试null值
        assertThat(generator.validate(null)).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("path_traversal");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        List<GeneratorParameter> parameters = generator.getSupportedParameters();

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("osType", "targetFile", "depth", "encoding");
    }

    @Test
    @DisplayName("常见目标文件测试")
    void testCommonTargetFiles() {
        String[] targetFiles = { "passwd", "shadow", "hosts", "config", "web.config" };

        for (String targetFile : targetFiles) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("targetFile", targetFile);
            GenerationContext fileContext = new GenerationContext(parameters, 12345L);

            String pathTraversal = generator.generate(fileContext);
            assertThat(pathTraversal).isNotNull();
            assertThat(pathTraversal).contains(targetFile);
            assertThat(generator.validate(pathTraversal)).isTrue();
        }
    }

    @Test
    @DisplayName("空字节注入测试")
    void testNullByteInjection() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("nullByteInjection", true);
        GenerationContext nullByteContext = new GenerationContext(parameters, 12345L);

        String pathTraversal = generator.generate(nullByteContext);

        assertThat(pathTraversal).isNotNull();
        // 可能包含%00或其他空字节表示
        assertThat(generator.validate(pathTraversal)).isTrue();
    }

    @Test
    @DisplayName("过滤器绕过测试")
    void testFilterBypass() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("bypassFilter", true);
        GenerationContext bypassContext = new GenerationContext(parameters, 12345L);

        String pathTraversal = generator.generate(bypassContext);

        assertThat(pathTraversal).isNotNull();
        assertThat(generator.validate(pathTraversal)).isTrue();
    }

    @Test
    @DisplayName("路径长度限制测试")
    void testPathLengthLimit() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("maxLength", 100);
        GenerationContext lengthContext = new GenerationContext(parameters, 12345L);

        String pathTraversal = generator.generate(lengthContext);

        assertThat(pathTraversal).isNotNull();
        assertThat(pathTraversal.length()).isLessThanOrEqualTo(100);
    }

    @Test
    @DisplayName("特殊字符处理测试")
    void testSpecialCharacterHandling() {
        String pathTraversal = generator.generate(context);

        assertThat(pathTraversal).isNotNull();
        // 路径遍历通常包含特殊字符
        assertThat(pathTraversal).containsAnyOf(".", "/", "\\", "%");
    }

    @Test
    @DisplayName("路径多样性测试")
    void testPathDiversity() {
        java.util.Set<String> paths = new java.util.HashSet<>();

        for (int i = 0; i < 50; i++) {
            String pathTraversal = generator.generate(context);
            paths.add(pathTraversal);
        }

        // 应该生成多种不同的路径
        assertThat(paths.size()).isGreaterThan(10);
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("osType", "INVALID_OS");
        parameters.put("depth", -1);
        GenerationContext invalidContext = new GenerationContext(parameters, 12345L);

        // 应该能够处理无效参数并生成有效的路径遍历
        String pathTraversal = generator.generate(invalidContext);
        assertThat(pathTraversal).isNotNull();
        assertThat(generator.validate(pathTraversal)).isTrue();
    }

    @Test
    @DisplayName("性能测试")
    void testPerformance() {
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 1000; i++) {
            String pathTraversal = generator.generate(context);
            assertThat(pathTraversal).isNotNull();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertThat(duration).isLessThan(2000); // 应该在2秒内完成
    }

    @Test
    @DisplayName("路径分隔符测试")
    void testPathSeparators() {
        Map<String, Object> windowsParams = new HashMap<>();
        windowsParams.put("osType", "WINDOWS");
        GenerationContext windowsContext = new GenerationContext(windowsParams, 12345L);

        Map<String, Object> linuxParams = new HashMap<>();
        linuxParams.put("osType", "LINUX");
        GenerationContext linuxContext = new GenerationContext(linuxParams, 12345L);

        boolean foundWindowsSeparator = false;
        boolean foundLinuxSeparator = false;

        for (int i = 0; i < 20; i++) {
            String windowsPath = generator.generate(windowsContext);
            String linuxPath = generator.generate(linuxContext);

            if (windowsPath.contains("\\")) {
                foundWindowsSeparator = true;
            }
            if (linuxPath.contains("/")) {
                foundLinuxSeparator = true;
            }
        }

        assertThat(foundWindowsSeparator).isTrue();
        assertThat(foundLinuxSeparator).isTrue();
    }

    @Test
    @DisplayName("绝对路径测试")
    void testAbsolutePaths() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("useAbsolutePath", true);
        GenerationContext absoluteContext = new GenerationContext(parameters, 12345L);

        String pathTraversal = generator.generate(absoluteContext);

        assertThat(pathTraversal).isNotNull();
        // 绝对路径应该以/或C:\等开头
        assertThat(pathTraversal).matches("^(/|[A-Za-z]:\\\\).*");
    }

    @Test
    @DisplayName("相对路径测试")
    void testRelativePaths() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("useRelativePath", true);
        GenerationContext relativeContext = new GenerationContext(parameters, 12345L);

        String pathTraversal = generator.generate(relativeContext);

        assertThat(pathTraversal).isNotNull();
        // 相对路径应该包含../或..\
        assertThat(pathTraversal).containsAnyOf("../", "..\\");
    }
}