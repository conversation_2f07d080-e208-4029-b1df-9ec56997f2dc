package examples;

import com.dataforge.core.relation.ConsistencyManager;
import com.dataforge.core.relation.ConsistencyResult;
import com.dataforge.core.relation.ConsistencyFixResult;

import java.util.HashMap;
import java.util.Map;

/**
 * 数据一致性保证演示程序
 * 
 * 演示如何使用ConsistencyManager来检查和修复数据一致性问题。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class DataConsistencyDemo {

    public static void main(String[] args) {
        System.out.println("=== DataForge 数据一致性保证演示 ===\n");

        // 创建一致性管理器
        ConsistencyManager consistencyManager = new ConsistencyManager();

        // 演示1: 身份证号与年龄一致性检查
        demonstrateIdCardAgeConsistency(consistencyManager);

        // 演示2: 身份证号与性别一致性检查
        demonstrateIdCardGenderConsistency(consistencyManager);

        // 演示3: 姓名与性别一致性检查
        demonstrateNameGenderConsistency(consistencyManager);

        // 演示4: 邮箱与姓名一致性检查
        demonstrateEmailNameConsistency(consistencyManager);

        // 演示5: 综合一致性检查和修复
        demonstrateComprehensiveConsistency(consistencyManager);

        System.out.println("\n=== 演示完成 ===");
    }

    /**
     * 演示身份证号与年龄一致性检查
     */
    private static void demonstrateIdCardAgeConsistency(ConsistencyManager consistencyManager) {
        System.out.println("1. 身份证号与年龄一致性检查演示");
        System.out.println("----------------------------------------");

        // 测试一致的数据
        Map<String, Object> consistentData = new HashMap<>();
        consistentData.put("idcard", "110101199001011234");
        consistentData.put("age", 34); // 1990年出生，2024年34岁

        System.out.println("测试一致的数据:");
        System.out.println("  身份证号: " + consistentData.get("idcard"));
        System.out.println("  年龄: " + consistentData.get("age"));

        ConsistencyResult result = consistencyManager.checkConsistency(consistentData);
        System.out.println("一致性检查结果: " + (result.hasViolations() ? "不一致" : "一致"));
        if (result.hasViolations()) {
            System.out.println("  违规项: " + result.getViolations());
        }

        // 测试不一致的数据
        Map<String, Object> inconsistentData = new HashMap<>();
        inconsistentData.put("idcard", "110101199001011234");
        inconsistentData.put("age", 20); // 年龄不匹配

        System.out.println("\n测试不一致的数据:");
        System.out.println("  身份证号: " + inconsistentData.get("idcard"));
        System.out.println("  年龄: " + inconsistentData.get("age"));

        result = consistencyManager.checkConsistency(inconsistentData);
        System.out.println("一致性检查结果: " + (result.hasViolations() ? "不一致" : "一致"));
        if (result.hasViolations()) {
            System.out.println("  违规项: " + result.getViolations());
        }

        // 尝试修复
        ConsistencyFixResult fixResult = consistencyManager.fixConsistency(inconsistentData);
        if (fixResult.isFixed()) {
            System.out.println("修复成功:");
            System.out.println("  修复后的数据: " + fixResult.getFixedValues());
            System.out.println("  应用的修复: " + fixResult.getAppliedFixes());
        }

        System.out.println();
    }

    /**
     * 演示身份证号与性别一致性检查
     */
    private static void demonstrateIdCardGenderConsistency(ConsistencyManager consistencyManager) {
        System.out.println("2. 身份证号与性别一致性检查演示");
        System.out.println("----------------------------------------");

        // 测试不一致的数据（身份证号显示男性，但性别设为女性）
        Map<String, Object> inconsistentData = new HashMap<>();
        inconsistentData.put("idcard", "110101199001011234"); // 倒数第二位是3，奇数表示男性
        inconsistentData.put("gender", "FEMALE"); // 但设置为女性

        System.out.println("测试不一致的数据:");
        System.out.println("  身份证号: " + inconsistentData.get("idcard") + " (男性)");
        System.out.println("  性别: " + inconsistentData.get("gender"));

        ConsistencyResult result = consistencyManager.checkConsistency(inconsistentData);
        System.out.println("一致性检查结果: " + (result.hasViolations() ? "不一致" : "一致"));
        if (result.hasViolations()) {
            System.out.println("  违规项: " + result.getViolations());
        }

        // 尝试修复
        ConsistencyFixResult fixResult = consistencyManager.fixConsistency(inconsistentData);
        if (fixResult.isFixed()) {
            System.out.println("修复成功:");
            System.out.println("  修复后的数据: " + fixResult.getFixedValues());
            System.out.println("  应用的修复: " + fixResult.getAppliedFixes());
        }

        System.out.println();
    }

    /**
     * 演示姓名与性别一致性检查
     */
    private static void demonstrateNameGenderConsistency(ConsistencyManager consistencyManager) {
        System.out.println("3. 姓名与性别一致性检查演示");
        System.out.println("----------------------------------------");

        // 测试可能不一致的数据
        Map<String, Object> testData = new HashMap<>();
        testData.put("name", "王小明");
        testData.put("gender", "FEMALE"); // 名字通常是男性，但性别设为女性

        System.out.println("测试数据:");
        System.out.println("  姓名: " + testData.get("name"));
        System.out.println("  性别: " + testData.get("gender"));

        ConsistencyResult result = consistencyManager.checkConsistency(testData);
        System.out.println("一致性检查结果: " + (result.hasViolations() ? "不一致" : "一致"));
        if (result.hasViolations()) {
            System.out.println("  违规项: " + result.getViolations());
        }
        if (result.hasWarnings()) {
            System.out.println("  警告: " + result.getWarnings());
        }

        System.out.println();
    }

    /**
     * 演示邮箱与姓名一致性检查
     */
    private static void demonstrateEmailNameConsistency(ConsistencyManager consistencyManager) {
        System.out.println("4. 邮箱与姓名一致性检查演示");
        System.out.println("----------------------------------------");

        // 测试不一致的数据
        Map<String, Object> inconsistentData = new HashMap<>();
        inconsistentData.put("name", "张三");
        inconsistentData.put("email_username", "lisi"); // 邮箱用户名与姓名不匹配

        System.out.println("测试不一致的数据:");
        System.out.println("  姓名: " + inconsistentData.get("name"));
        System.out.println("  邮箱用户名: " + inconsistentData.get("email_username"));

        ConsistencyResult result = consistencyManager.checkConsistency(inconsistentData);
        System.out.println("一致性检查结果: " + (result.hasViolations() ? "不一致" : "一致"));
        if (result.hasViolations()) {
            System.out.println("  违规项: " + result.getViolations());
        }

        // 尝试修复
        ConsistencyFixResult fixResult = consistencyManager.fixConsistency(inconsistentData);
        if (fixResult.isFixed()) {
            System.out.println("修复成功:");
            System.out.println("  修复后的数据: " + fixResult.getFixedValues());
            System.out.println("  应用的修复: " + fixResult.getAppliedFixes());
        }

        System.out.println();
    }

    /**
     * 演示综合一致性检查和修复
     */
    private static void demonstrateComprehensiveConsistency(ConsistencyManager consistencyManager) {
        System.out.println("5. 综合一致性检查和修复演示");
        System.out.println("----------------------------------------");

        // 创建包含多个不一致问题的数据
        Map<String, Object> complexData = new HashMap<>();
        complexData.put("idcard", "110101199001011234");
        complexData.put("age", 20); // 年龄不匹配
        complexData.put("gender", "FEMALE"); // 性别不匹配
        complexData.put("name", "李四");
        complexData.put("email_username", "wangwu"); // 邮箱用户名不匹配

        System.out.println("测试复杂数据:");
        for (Map.Entry<String, Object> entry : complexData.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }

        // 检查一致性
        ConsistencyResult result = consistencyManager.checkConsistency(complexData);
        System.out.println("\n一致性检查结果: " + (result.hasViolations() ? "发现问题" : "一致"));
        if (result.hasViolations()) {
            System.out.println("违规项:");
            for (String violation : result.getViolations()) {
                System.out.println("  - " + violation);
            }
        }
        if (result.hasWarnings()) {
            System.out.println("警告:");
            for (String warning : result.getWarnings()) {
                System.out.println("  - " + warning);
            }
        }

        // 尝试修复
        System.out.println("\n尝试修复数据一致性...");
        ConsistencyFixResult fixResult = consistencyManager.fixConsistency(complexData);

        if (fixResult.isFixed()) {
            System.out.println("修复结果: " + (fixResult.isPartialSuccess() ? "部分成功" : "完全成功"));
            
            System.out.println("修复后的数据:");
            for (Map.Entry<String, Object> entry : fixResult.getFixedValues().entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }

            if (!fixResult.getAppliedFixes().isEmpty()) {
                System.out.println("应用的修复:");
                for (String fix : fixResult.getAppliedFixes()) {
                    System.out.println("  - " + fix);
                }
            }

            if (fixResult.hasFailed()) {
                System.out.println("失败的修复:");
                for (String failed : fixResult.getFailedFixes()) {
                    System.out.println("  - " + failed);
                }
            }
        } else {
            System.out.println("修复失败");
            if (fixResult.hasFailed()) {
                System.out.println("失败原因:");
                for (String failed : fixResult.getFailedFixes()) {
                    System.out.println("  - " + failed);
                }
            }
        }

        // 显示一致性管理器统计信息
        System.out.println("\n一致性管理器统计:");
        System.out.println("  规则数量: " + consistencyManager.getRuleCount());
        System.out.println("  管理器状态: " + consistencyManager.toString());

        System.out.println();
    }
}