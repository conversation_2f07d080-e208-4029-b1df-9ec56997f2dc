package com.dataforge.performance.runner;

import com.dataforge.performance.baseline.PerformanceBaseline;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * 性能测试运行器
 * 
 * 统一管理和执行所有性能测试，包括JMH基准测试和基线回归测试
 */
public class PerformanceTestRunner {

    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");

    /**
     * 运行所有性能测试
     */
    public static void runAllTests() throws RunnerException {
        System.out.println("=== DataForge 性能测试套件 ===");
        System.out.println("开始时间: " + LocalDateTime.now().format(TIMESTAMP_FORMAT));
        
        // 1. 运行基线回归测试
        System.out.println("\n1. 执行性能回归测试...");
        runRegressionTests();
        
        // 2. 运行JMH基准测试
        System.out.println("\n2. 执行JMH基准测试...");
        runJMHBenchmarks();
        
        System.out.println("\n=== 性能测试完成 ===");
        System.out.println("结束时间: " + LocalDateTime.now().format(TIMESTAMP_FORMAT));
    }

    /**
     * 运行快速性能测试
     */
    public static void runQuickTests() throws RunnerException {
        System.out.println("=== DataForge 快速性能测试 ===");
        
        Options opts = new OptionsBuilder()
                .include(".*DataGenerationBenchmark.*")
                .warmupIterations(1)
                .measurementIterations(2)
                .forks(1)
                .threads(1)
                .build();

        new Runner(opts).run();
    }

    /**
     * 运行内存性能测试
     */
    public static void runMemoryTests() throws RunnerException {
        System.out.println("=== DataForge 内存性能测试 ===");
        
        Options opts = new OptionsBuilder()
                .include(".*MemoryUsageBenchmark.*")
                .warmupIterations(2)
                .measurementIterations(3)
                .forks(1)
                .jvmArgs("-Xmx2g", "-XX:+UseG1GC")
                .build();

        new Runner(opts).run();
    }

    /**
     * 运行大数据量性能测试
     */
    public static void runLargeDataTests() throws RunnerException {
        System.out.println("=== DataForge 大数据量性能测试 ===");
        
        Options opts = new OptionsBuilder()
                .include(".*LargeDataBenchmark.*")
                .warmupIterations(2)
                .measurementIterations(3)
                .forks(1)
                .threads(1)
                .jvmArgs("-Xmx4g", "-XX:+UseG1GC")
                .build();

        new Runner(opts).run();
    }

    /**
     * 运行CPU利用率测试
     */
    public static void runCPUTests() throws RunnerException {
        System.out.println("=== DataForge CPU利用率测试 ===");
        
        Options opts = new OptionsBuilder()
                .include(".*CPUUtilizationBenchmark.*")
                .warmupIterations(2)
                .measurementIterations(3)
                .forks(1)
                .build();

        new Runner(opts).run();
    }

    /**
     * 运行性能回归测试
     */
    private static void runRegressionTests() {
        try {
            PerformanceBaseline baseline = new PerformanceBaseline();
            PerformanceBaseline.RegressionResult result = baseline.checkRegression();
            
            if (result.hasRegression) {
                System.out.println("警告: 检测到性能回归！");
                System.out.println("详情: " + result.details);
            } else {
                System.out.println("性能回归测试通过");
            }
        } catch (Exception e) {
            System.err.println("性能回归测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 运行JMH基准测试
     */
    private static void runJMHBenchmarks() throws RunnerException {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        String resultFile = "benchmark-results-" + timestamp + ".json";
        
        Options opts = new OptionsBuilder()
                .include("com.dataforge.performance.benchmark.*")
                .warmupIterations(3)
                .measurementIterations(5)
                .forks(1)
                .resultFormat(org.openjdk.jmh.results.format.ResultFormatType.JSON)
                .result(resultFile)
                .build();

        new Runner(opts).run();
        
        System.out.println("基准测试结果已保存到: " + resultFile);
    }

    /**
     * 建立性能基线
     */
    public static void establishBaseline() {
        System.out.println("=== 建立性能基线 ===");
        PerformanceBaseline baseline = new PerformanceBaseline();
        baseline.establishBaseline();
    }

    /**
     * 更新性能基线
     */
    public static void updateBaseline() {
        System.out.println("=== 更新性能基线 ===");
        PerformanceBaseline baseline = new PerformanceBaseline();
        baseline.updateBaseline();
    }

    /**
     * 生成性能报告
     */
    public static void generateReport() {
        System.out.println("=== 生成性能报告 ===");
        
        try {
            PerformanceReportGenerator generator = new PerformanceReportGenerator();
            String reportPath = generator.generateReport();
            System.out.println("性能报告已生成: " + reportPath);
        } catch (Exception e) {
            System.err.println("生成性能报告失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        try {
            if (args.length == 0) {
                printUsage();
                return;
            }

            String command = args[0].toLowerCase();
            switch (command) {
                case "all":
                    runAllTests();
                    break;
                case "quick":
                    runQuickTests();
                    break;
                case "memory":
                    runMemoryTests();
                    break;
                case "large":
                    runLargeDataTests();
                    break;
                case "cpu":
                    runCPUTests();
                    break;
                case "baseline":
                    establishBaseline();
                    break;
                case "update-baseline":
                    updateBaseline();
                    break;
                case "regression":
                    runRegressionTests();
                    break;
                case "report":
                    generateReport();
                    break;
                default:
                    System.out.println("未知命令: " + command);
                    printUsage();
            }
        } catch (Exception e) {
            System.err.println("执行性能测试时发生错误: " + e.getMessage());
            e.printStackTrace();
            System.exit(1);
        }
    }

    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.out.println("DataForge 性能测试运行器");
        System.out.println();
        System.out.println("用法: java PerformanceTestRunner <command>");
        System.out.println();
        System.out.println("可用命令:");
        System.out.println("  all              - 运行所有性能测试");
        System.out.println("  quick            - 运行快速性能测试");
        System.out.println("  memory           - 运行内存性能测试");
        System.out.println("  large            - 运行大数据量性能测试");
        System.out.println("  cpu              - 运行CPU利用率测试");
        System.out.println("  baseline         - 建立性能基线");
        System.out.println("  update-baseline  - 更新性能基线");
        System.out.println("  regression       - 运行性能回归测试");
        System.out.println("  report           - 生成性能报告");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  java PerformanceTestRunner all");
        System.out.println("  java PerformanceTestRunner quick");
        System.out.println("  java PerformanceTestRunner baseline");
    }

    /**
     * 性能报告生成器
     */
    private static class PerformanceReportGenerator {
        
        public String generateReport() throws IOException {
            String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
            Path reportPath = Paths.get("performance-report-" + timestamp + ".html");
            
            StringBuilder html = new StringBuilder();
            html.append("<!DOCTYPE html>\n");
            html.append("<html>\n<head>\n");
            html.append("<title>DataForge 性能测试报告</title>\n");
            html.append("<style>\n");
            html.append("body { font-family: Arial, sans-serif; margin: 20px; }\n");
            html.append("table { border-collapse: collapse; width: 100%; }\n");
            html.append("th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }\n");
            html.append("th { background-color: #f2f2f2; }\n");
            html.append(".metric { margin: 20px 0; }\n");
            html.append("</style>\n");
            html.append("</head>\n<body>\n");
            
            html.append("<h1>DataForge 性能测试报告</h1>\n");
            html.append("<p>生成时间: ").append(LocalDateTime.now()).append("</p>\n");
            
            html.append("<div class='metric'>\n");
            html.append("<h2>测试环境</h2>\n");
            html.append("<ul>\n");
            html.append("<li>Java版本: ").append(System.getProperty("java.version")).append("</li>\n");
            html.append("<li>操作系统: ").append(System.getProperty("os.name")).append("</li>\n");
            html.append("<li>处理器核心数: ").append(Runtime.getRuntime().availableProcessors()).append("</li>\n");
            html.append("<li>最大内存: ").append(Runtime.getRuntime().maxMemory() / 1024 / 1024).append(" MB</li>\n");
            html.append("</ul>\n");
            html.append("</div>\n");
            
            html.append("<div class='metric'>\n");
            html.append("<h2>性能测试说明</h2>\n");
            html.append("<p>本报告包含了DataForge数据生成工具的全面性能测试结果，包括：</p>\n");
            html.append("<ul>\n");
            html.append("<li>微基准测试 - 使用JMH测试各个组件的性能</li>\n");
            html.append("<li>大数据量生成测试 - 测试系统在大规模数据生成时的表现</li>\n");
            html.append("<li>内存使用测试 - 监控内存使用情况和GC压力</li>\n");
            html.append("<li>CPU利用率测试 - 测试多核CPU的利用效率</li>\n");
            html.append("<li>性能回归测试 - 与基线数据对比检测性能退化</li>\n");
            html.append("</ul>\n");
            html.append("</div>\n");
            
            html.append("<div class='metric'>\n");
            html.append("<h2>测试结果摘要</h2>\n");
            html.append("<p>详细的测试结果请查看对应的JSON格式基准测试报告文件。</p>\n");
            html.append("<p>性能基线数据存储在 performance-baseline.properties 文件中。</p>\n");
            html.append("</div>\n");
            
            html.append("</body>\n</html>");
            
            Files.write(reportPath, html.toString().getBytes());
            return reportPath.toString();
        }
    }
}