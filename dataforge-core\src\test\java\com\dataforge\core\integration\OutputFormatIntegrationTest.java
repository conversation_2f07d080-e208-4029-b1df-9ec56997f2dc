package com.dataforge.core.integration;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.FieldConfig;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.service.DataForgeService;
import com.dataforge.core.output.OutputWriterFactory;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;

import static org.assertj.core.api.Assertions.*;

/**
 * 多种输出格式集成测试
 */
@DisplayName("多种输出格式集成测试")
class OutputFormatIntegrationTest {

    @TempDir
    Path tempDir;

    private DataForgeService dataForgeService;
    private OutputWriterFactory outputWriterFactory;

    @BeforeEach
    void setUp() {
        dataForgeService = new DataForgeService();
        outputWriterFactory = new OutputWriterFactory();
    }

    @Test
    @DisplayName("CSV格式输出集成测试")
    void testCsvFormatIntegration() throws IOException {
        // 创建测试配置
        GenerationConfig config = createTestConfig();

        // 设置CSV输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve("test.csv").toString());
        outputConfig.setIncludeHeader(true);
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证CSV文件
        Path csvFile = tempDir.resolve("test.csv");
        assertThat(csvFile).exists();

        List<String> lines = Files.readAllLines(csvFile);
        assertThat(lines).isNotEmpty();

        // 验证CSV格式
        String header = lines.get(0);
        assertThat(header).isEqualTo("name,age,email");

        // 验证数据行
        for (int i = 1; i < lines.size(); i++) {
            String[] fields = lines.get(i).split(",");
            assertThat(fields).hasSize(3);
            assertThat(fields[0]).isNotEmpty(); // name
            assertThat(fields[1]).matches("\\d+"); // age
            assertThat(fields[2]).contains("@"); // email
        }
    }

    @Test
    @DisplayName("JSON格式输出集成测试")
    void testJsonFormatIntegration() throws IOException {
        // 创建测试配置
        GenerationConfig config = createTestConfig();

        // 设置JSON输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("json");
        outputConfig.setFilePath(tempDir.resolve("test.json").toString());
        outputConfig.setPrettyPrint(true);
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证JSON文件
        Path jsonFile = tempDir.resolve("test.json");
        assertThat(jsonFile).exists();

        String content = Files.readString(jsonFile);
        assertThat(content).isNotEmpty();

        // 验证JSON格式
        assertThat(content).startsWith("[");
        assertThat(content).endsWith("]");
        assertThat(content).contains("\"name\":");
        assertThat(content).contains("\"age\":");
        assertThat(content).contains("\"email\":");

        // 验证JSON结构（简单验证）
        assertThat(content.split("\\{").length - 1).isEqualTo(config.getCount());
    }

    @Test
    @DisplayName("XML格式输出集成测试")
    void testXmlFormatIntegration() throws IOException {
        // 创建测试配置
        GenerationConfig config = createTestConfig();

        // 设置XML输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("xml");
        outputConfig.setFilePath(tempDir.resolve("test.xml").toString());
        outputConfig.setRootElement("records");
        outputConfig.setRecordElement("record");
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证XML文件
        Path xmlFile = tempDir.resolve("test.xml");
        assertThat(xmlFile).exists();

        String content = Files.readString(xmlFile);
        assertThat(content).isNotEmpty();

        // 验证XML格式
        assertThat(content).contains("<?xml version=\"1.0\"");
        assertThat(content).contains("<records>");
        assertThat(content).contains("</records>");
        assertThat(content).contains("<record>");
        assertThat(content).contains("</record>");
        assertThat(content).contains("<name>");
        assertThat(content).contains("<age>");
        assertThat(content).contains("<email>");
    }

    @Test
    @DisplayName("Excel格式输出集成测试")
    void testExcelFormatIntegration() throws IOException {
        // 创建测试配置
        GenerationConfig config = createTestConfig();

        // 设置Excel输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("excel");
        outputConfig.setFilePath(tempDir.resolve("test.xlsx").toString());
        outputConfig.setSheetName("TestData");
        outputConfig.setIncludeHeader(true);
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证Excel文件
        Path excelFile = tempDir.resolve("test.xlsx");
        assertThat(excelFile).exists();

        // 验证文件大小（Excel文件应该有一定大小）
        long fileSize = Files.size(excelFile);
        assertThat(fileSize).isGreaterThan(1000);
    }

    @Test
    @DisplayName("SQL格式输出集成测试")
    void testSqlFormatIntegration() throws IOException {
        // 创建测试配置
        GenerationConfig config = createTestConfig();

        // 设置SQL输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("sql");
        outputConfig.setFilePath(tempDir.resolve("test.sql").toString());
        outputConfig.setTableName("test_table");
        outputConfig.setIncludeCreateTable(true);
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证SQL文件
        Path sqlFile = tempDir.resolve("test.sql");
        assertThat(sqlFile).exists();

        String content = Files.readString(sqlFile);
        assertThat(content).isNotEmpty();

        // 验证SQL格式
        assertThat(content).contains("CREATE TABLE");
        assertThat(content).contains("test_table");
        assertThat(content).contains("INSERT INTO");
        assertThat(content).contains("VALUES");

        // 验证INSERT语句数量
        long insertCount = content.lines()
                .filter(line -> line.trim().startsWith("INSERT INTO"))
                .count();
        assertThat(insertCount).isEqualTo(config.getCount());
    }

    @Test
    @DisplayName("控制台输出集成测试")
    void testConsoleFormatIntegration() throws IOException {
        // 创建测试配置
        GenerationConfig config = createTestConfig();
        config.setCount(5); // 减少数量以便测试

        // 设置控制台输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("console");
        outputConfig.setTableFormat(true);
        config.setOutputConfig(outputConfig);

        // 执行数据生成（这里主要测试不抛异常）
        assertThatCode(() -> dataForgeService.generateData(config))
                .doesNotThrowAnyException();
    }

    @Test
    @DisplayName("多格式同时输出集成测试")
    void testMultipleFormatIntegration() throws IOException {
        // 创建测试配置
        GenerationConfig config = createTestConfig();

        // 设置多个输出格式
        OutputConfig csvConfig = new OutputConfig();
        csvConfig.setFormat("csv");
        csvConfig.setFilePath(tempDir.resolve("multi_test.csv").toString());

        OutputConfig jsonConfig = new OutputConfig();
        jsonConfig.setFormat("json");
        jsonConfig.setFilePath(tempDir.resolve("multi_test.json").toString());

        config.setOutputConfigs(Arrays.asList(csvConfig, jsonConfig));

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证两个输出文件都存在
        Path csvFile = tempDir.resolve("multi_test.csv");
        Path jsonFile = tempDir.resolve("multi_test.json");

        assertThat(csvFile).exists();
        assertThat(jsonFile).exists();

        // 验证文件内容
        List<String> csvLines = Files.readAllLines(csvFile);
        String jsonContent = Files.readString(jsonFile);

        assertThat(csvLines).hasSize(config.getCount() + 1); // +1 for header
        assertThat(jsonContent).contains("\"name\":");
    }

    @Test
    @DisplayName("自定义分隔符CSV输出测试")
    void testCustomDelimiterCsvIntegration() throws IOException {
        // 创建测试配置
        GenerationConfig config = createTestConfig();

        // 设置自定义分隔符CSV输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve("custom_delimiter.csv").toString());
        outputConfig.setDelimiter("|");
        outputConfig.setIncludeHeader(true);
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证自定义分隔符
        Path csvFile = tempDir.resolve("custom_delimiter.csv");
        assertThat(csvFile).exists();

        List<String> lines = Files.readAllLines(csvFile);
        assertThat(lines).isNotEmpty();

        // 验证分隔符
        String header = lines.get(0);
        assertThat(header).isEqualTo("name|age|email");

        for (int i = 1; i < lines.size(); i++) {
            assertThat(lines.get(i)).contains("|");
            String[] fields = lines.get(i).split("\\|");
            assertThat(fields).hasSize(3);
        }
    }

    @Test
    @DisplayName("压缩输出集成测试")
    void testCompressedOutputIntegration() throws IOException {
        // 创建测试配置
        GenerationConfig config = createTestConfig();
        config.setCount(1000); // 增加数据量以便压缩效果明显

        // 设置压缩输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve("compressed_test.csv.gz").toString());
        outputConfig.setCompressionEnabled(true);
        outputConfig.setCompressionType("gzip");
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证压缩文件
        Path compressedFile = tempDir.resolve("compressed_test.csv.gz");
        assertThat(compressedFile).exists();

        // 验证文件确实被压缩了（压缩文件应该比原始文件小）
        long compressedSize = Files.size(compressedFile);
        assertThat(compressedSize).isGreaterThan(0);
    }

    @Test
    @DisplayName("编码格式输出集成测试")
    void testEncodingFormatIntegration() throws IOException {
        // 创建包含中文的测试配置
        GenerationConfig config = new GenerationConfig();
        config.setCount(10);

        FieldConfig nameField = new FieldConfig("name", "name");
        nameField.getParameters().put("nameType", "CHINESE");
        config.setFields(Arrays.asList(nameField));

        // 设置UTF-8编码输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve("encoding_test.csv").toString());
        outputConfig.setEncoding("UTF-8");
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证编码
        Path csvFile = tempDir.resolve("encoding_test.csv");
        assertThat(csvFile).exists();

        // 使用UTF-8读取文件
        List<String> lines = Files.readAllLines(csvFile, java.nio.charset.StandardCharsets.UTF_8);
        assertThat(lines).isNotEmpty();

        // 验证中文字符正确显示
        for (int i = 1; i < lines.size(); i++) {
            String line = lines.get(i);
            assertThat(line).isNotEmpty();
            // 中文字符应该正确显示，不是乱码
            assertThat(line).doesNotContain("?");
        }
    }

    /**
     * 创建测试配置
     */
    private GenerationConfig createTestConfig() {
        GenerationConfig config = new GenerationConfig();
        config.setCount(50);

        FieldConfig nameField = new FieldConfig("name", "name");
        FieldConfig ageField = new FieldConfig("age", "age");
        FieldConfig emailField = new FieldConfig("email", "email");

        config.setFields(Arrays.asList(nameField, ageField, emailField));

        return config;
    }
}