package com.dataforge.generators.security;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * SQL注入payload生成器单元测试
 */
@DisplayName("SQL注入payload生成器测试")
class SqlInjectionPayloadGeneratorTest {

    private SqlInjectionPayloadGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new SqlInjectionPayloadGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext(parameters, 12345L);
    }

    @Test
    @DisplayName("基本SQL注入payload生成测试")
    void testBasicGeneration() {
        String payload = generator.generate(context);

        assertThat(payload).isNotNull();
        assertThat(payload).isNotEmpty();
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证多样性")
    void testRepeatedGeneration() {
        String payload = generator.generate(context);

        assertThat(payload).isNotNull();
        assertThat(payload).isNotEmpty();
        assertThat(generator.validate(payload)).isTrue();
    }

    @Test
    @DisplayName("指定数据库类型生成测试")
    void testGenerationWithDatabaseType() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("databaseType", "MYSQL");
        GenerationContext contextMySQL = new GenerationContext(parameters, 12345L);

        String payload = generator.generate(contextMySQL);

        assertThat(payload).isNotNull();
        assertThat(generator.validate(payload)).isTrue();

        // 测试其他数据库类型
        parameters.put("databaseType", "ORACLE");
        GenerationContext contextOracle = new GenerationContext(parameters, 12345L);

        payload = generator.generate(contextOracle);
        assertThat(payload).isNotNull();
        assertThat(generator.validate(payload)).isTrue();
    }

    @Test
    @DisplayName("指定注入类型生成测试")
    void testGenerationWithInjectionType() {
        String[] injectionTypes = { "UNION", "BOOLEAN", "TIME_BASED", "ERROR_BASED" };

        for (String type : injectionTypes) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("injectionType", type);
            GenerationContext typeContext = new GenerationContext(parameters, 12345L);

            String payload = generator.generate(typeContext);
            assertThat(payload).isNotNull();
            assertThat(generator.validate(payload)).isTrue();
        }
    }

    @Test
    @DisplayName("UNION注入payload测试")
    void testUnionInjectionPayload() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("injectionType", "UNION");
        GenerationContext unionContext = new GenerationContext(parameters, 12345L);

        String payload = generator.generate(unionContext);

        assertThat(payload).isNotNull();
        assertThat(payload.toUpperCase()).contains("UNION");
        assertThat(payload.toUpperCase()).contains("SELECT");
    }

    @Test
    @DisplayName("布尔盲注payload测试")
    void testBooleanBlindInjectionPayload() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("injectionType", "BOOLEAN");
        GenerationContext booleanContext = new GenerationContext(parameters, 12345L);

        String payload = generator.generate(booleanContext);

        assertThat(payload).isNotNull();
        // 布尔盲注通常包含条件判断
        assertThat(payload).containsAnyOf("AND", "OR", "=", ">", "<");
    }

    @Test
    @DisplayName("时间盲注payload测试")
    void testTimeBasedInjectionPayload() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("injectionType", "TIME_BASED");
        GenerationContext timeContext = new GenerationContext(parameters, 12345L);

        String payload = generator.generate(timeContext);

        assertThat(payload).isNotNull();
        // 时间盲注通常包含延时函数
        assertThat(payload.toUpperCase()).containsAnyOf("SLEEP", "WAITFOR", "DELAY", "BENCHMARK");
    }

    @Test
    @DisplayName("错误注入payload测试")
    void testErrorBasedInjectionPayload() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("injectionType", "ERROR_BASED");
        GenerationContext errorContext = new GenerationContext(parameters, 12345L);

        String payload = generator.generate(errorContext);

        assertThat(payload).isNotNull();
        // 错误注入通常包含会产生错误的函数
        assertThat(payload.toUpperCase()).containsAnyOf("EXTRACTVALUE", "UPDATEXML", "CONVERT");
    }

    @Test
    @DisplayName("编码绕过测试")
    void testEncodingBypass() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("encoding", "URL");
        GenerationContext urlContext = new GenerationContext(parameters, 12345L);

        String payload = generator.generate(urlContext);

        assertThat(payload).isNotNull();
        // URL编码的payload应该包含%符号
        if (payload.contains("%")) {
            assertThat(payload).matches(".*%[0-9A-Fa-f]{2}.*");
        }

        // 测试十六进制编码
        parameters.put("encoding", "HEX");
        GenerationContext hexContext = new GenerationContext(parameters, 12345L);

        payload = generator.generate(hexContext);
        assertThat(payload).isNotNull();
    }

    @Test
    @DisplayName("payload校验测试 - 有效payload")
    void testValidationWithValidPayload() {
        String[] validPayloads = {
                "' OR '1'='1",
                "1' UNION SELECT NULL--",
                "'; DROP TABLE users;--",
                "1' AND SLEEP(5)--"
        };

        for (String payload : validPayloads) {
            assertThat(generator.validate(payload)).isTrue();
        }
    }

    @Test
    @DisplayName("payload校验测试 - 无效payload")
    void testValidationWithInvalidPayload() {
        String[] invalidPayloads = {
                "",
                "   ",
                "normal text without injection"
        };

        for (String payload : invalidPayloads) {
            assertThat(generator.validate(payload)).isFalse();
        }

        // 测试null值
        assertThat(generator.validate(null)).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("sql_injection");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        List<GeneratorParameter> parameters = generator.getSupportedParameters();

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("databaseType", "injectionType", "encoding");
    }

    @Test
    @DisplayName("不同数据库类型payload测试")
    void testDifferentDatabaseTypes() {
        String[] dbTypes = { "MYSQL", "POSTGRESQL", "ORACLE", "MSSQL", "SQLITE" };

        for (String dbType : dbTypes) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("databaseType", dbType);
            GenerationContext dbContext = new GenerationContext(parameters, 12345L);

            String payload = generator.generate(dbContext);
            assertThat(payload).isNotNull();
            assertThat(generator.validate(payload)).isTrue();
        }
    }

    @Test
    @DisplayName("复杂payload生成测试")
    void testComplexPayloadGeneration() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("complexity", "HIGH");
        parameters.put("includeComments", true);
        GenerationContext complexContext = new GenerationContext(parameters, 12345L);

        String payload = generator.generate(complexContext);

        assertThat(payload).isNotNull();
        // 复杂payload可能包含注释
        assertThat(payload).containsAnyOf("--", "/*", "#");
    }

    @Test
    @DisplayName("WAF绕过payload测试")
    void testWAFBypassPayload() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("bypassWAF", true);
        GenerationContext wafContext = new GenerationContext(parameters, 12345L);

        String payload = generator.generate(wafContext);

        assertThat(payload).isNotNull();
        assertThat(generator.validate(payload)).isTrue();
    }

    @Test
    @DisplayName("payload长度测试")
    void testPayloadLength() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("maxLength", 50);
        GenerationContext lengthContext = new GenerationContext(parameters, 12345L);

        String payload = generator.generate(lengthContext);

        assertThat(payload).isNotNull();
        assertThat(payload.length()).isLessThanOrEqualTo(50);
    }

    @Test
    @DisplayName("特殊字符处理测试")
    void testSpecialCharacterHandling() {
        String payload = generator.generate(context);

        assertThat(payload).isNotNull();
        // SQL注入payload通常包含特殊字符
        assertThat(payload).containsAnyOf("'", "\"", ";", "--", "/*", "*/");
    }

    @Test
    @DisplayName("payload多样性测试")
    void testPayloadDiversity() {
        java.util.Set<String> payloads = new java.util.HashSet<>();

        for (int i = 0; i < 100; i++) {
            String payload = generator.generate(context);
            payloads.add(payload);
        }

        // 应该生成多种不同的payload
        assertThat(payloads.size()).isGreaterThan(10);
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("databaseType", "INVALID_DB");
        parameters.put("injectionType", "INVALID_TYPE");
        GenerationContext invalidContext = new GenerationContext(parameters, 12345L);

        // 应该能够处理无效参数并生成有效的payload
        String payload = generator.generate(invalidContext);
        assertThat(payload).isNotNull();
        assertThat(generator.validate(payload)).isTrue();
    }

    @Test
    @DisplayName("性能测试")
    void testPerformance() {
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 1000; i++) {
            String payload = generator.generate(context);
            assertThat(payload).isNotNull();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertThat(duration).isLessThan(2000); // 应该在2秒内完成
    }

    @Test
    @DisplayName("SQL关键字包含测试")
    void testSQLKeywordInclusion() {
        String[] sqlKeywords = { "SELECT", "UNION", "INSERT", "UPDATE", "DELETE", "DROP", "CREATE" };
        boolean foundKeyword = false;

        for (int i = 0; i < 50; i++) {
            String payload = generator.generate(context).toUpperCase();
            for (String keyword : sqlKeywords) {
                if (payload.contains(keyword)) {
                    foundKeyword = true;
                    break;
                }
            }
            if (foundKeyword)
                break;
        }

        assertThat(foundKeyword).isTrue();
    }

    @Test
    @DisplayName("注释符号测试")
    void testCommentSymbols() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("includeComments", true);
        GenerationContext commentContext = new GenerationContext(parameters, 12345L);

        boolean foundComment = false;
        String[] commentSymbols = { "--", "/*", "*/", "#" };

        for (int i = 0; i < 20; i++) {
            String payload = generator.generate(commentContext);
            for (String symbol : commentSymbols) {
                if (payload.contains(symbol)) {
                    foundComment = true;
                    break;
                }
            }
            if (foundComment)
                break;
        }

        assertThat(foundComment).isTrue();
    }
}