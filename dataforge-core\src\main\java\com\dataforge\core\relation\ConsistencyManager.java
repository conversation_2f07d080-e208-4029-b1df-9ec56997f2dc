package com.dataforge.core.relation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 数据一致性管理器
 * 
 * 负责确保关联数据之间的一致性，处理关联冲突和异常情况。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class ConsistencyManager {

    private static final Logger logger = LoggerFactory.getLogger(ConsistencyManager.class);

    private final Map<String, ConsistencyRule> consistencyRules;
    private final Map<String, Set<String>> fieldDependencies;

    /**
     * 构造函数
     */
    public ConsistencyManager() {
        this.consistencyRules = new ConcurrentHashMap<>();
        this.fieldDependencies = new ConcurrentHashMap<>();
        initializeDefaultRules();
    }

    /**
     * 注册一致性规则
     * 
     * @param rule 一致性规则
     */
    public void registerConsistencyRule(ConsistencyRule rule) {
        if (rule == null) {
            logger.warn("尝试注册空的一致性规则");
            return;
        }

        consistencyRules.put(rule.getRuleName(), rule);

        // 更新字段依赖关系
        for (String field : rule.getInvolvedFields()) {
            fieldDependencies.computeIfAbsent(field, k -> new HashSet<>())
                    .addAll(rule.getInvolvedFields());
        }

        if (logger.isDebugEnabled()) {
            logger.debug("注册一致性规则: {}", rule.getRuleName());
        }
    }

    /**
     * 检查数据一致性
     * 
     * @param fieldValues 字段值映射
     * @return 一致性检查结果
     */
    public ConsistencyResult checkConsistency(Map<String, Object> fieldValues) {
        if (fieldValues == null || fieldValues.isEmpty()) {
            return ConsistencyResult.success("没有数据需要检查");
        }

        List<String> violations = new ArrayList<>();
        List<String> warnings = new ArrayList<>();

        // 检查所有一致性规则
        for (ConsistencyRule rule : consistencyRules.values()) {
            try {
                ConsistencyResult ruleResult = rule.check(fieldValues);

                if (ruleResult.hasViolations()) {
                    violations.addAll(ruleResult.getViolations());
                }

                if (ruleResult.hasWarnings()) {
                    warnings.addAll(ruleResult.getWarnings());
                }

            } catch (Exception e) {
                logger.error("执行一致性规则失败: {}", rule.getRuleName(), e);
                violations.add("规则执行异常: " + rule.getRuleName() + " - " + e.getMessage());
            }
        }

        if (violations.isEmpty()) {
            return warnings.isEmpty() ? ConsistencyResult.success("数据一致性检查通过")
                    : ConsistencyResult.successWithWarnings(warnings);
        } else {
            return ConsistencyResult.failure(violations, warnings);
        }
    }

    /**
     * 修复数据一致性
     * 
     * @param fieldValues 字段值映射
     * @return 修复结果
     */
    public ConsistencyFixResult fixConsistency(Map<String, Object> fieldValues) {
        if (fieldValues == null || fieldValues.isEmpty()) {
            return ConsistencyFixResult.noActionNeeded("没有数据需要修复");
        }

        Map<String, Object> fixedValues = new HashMap<>(fieldValues);
        List<String> appliedFixes = new ArrayList<>();
        List<String> failedFixes = new ArrayList<>();

        // 按优先级排序规则
        List<ConsistencyRule> sortedRules = consistencyRules.values().stream()
                .sorted(Comparator.comparingInt(ConsistencyRule::getPriority))
                .toList();

        for (ConsistencyRule rule : sortedRules) {
            try {
                if (rule.canFix()) {
                    ConsistencyFixResult ruleFixResult = rule.fix(fixedValues);

                    if (ruleFixResult.isFixed()) {
                        fixedValues.putAll(ruleFixResult.getFixedValues());
                        appliedFixes.addAll(ruleFixResult.getAppliedFixes());
                    } else if (ruleFixResult.hasFailed()) {
                        failedFixes.addAll(ruleFixResult.getFailedFixes());
                    }
                }
            } catch (Exception e) {
                logger.error("执行一致性修复失败: {}", rule.getRuleName(), e);
                failedFixes.add("修复规则执行异常: " + rule.getRuleName() + " - " + e.getMessage());
            }
        }

        if (appliedFixes.isEmpty() && failedFixes.isEmpty()) {
            return ConsistencyFixResult.noActionNeeded("数据已经一致，无需修复");
        } else if (failedFixes.isEmpty()) {
            return ConsistencyFixResult.success(fixedValues, appliedFixes);
        } else {
            return ConsistencyFixResult.partialSuccess(fixedValues, appliedFixes, failedFixes);
        }
    }

    /**
     * 获取字段的依赖关系
     * 
     * @param fieldName 字段名
     * @return 依赖的字段集合
     */
    public Set<String> getFieldDependencies(String fieldName) {
        return fieldDependencies.getOrDefault(fieldName, Collections.emptySet());
    }

    /**
     * 检查字段是否有依赖关系
     * 
     * @param fieldName 字段名
     * @return 如果有依赖返回true，否则返回false
     */
    public boolean hasFieldDependencies(String fieldName) {
        return fieldDependencies.containsKey(fieldName) &&
                !fieldDependencies.get(fieldName).isEmpty();
    }

    /**
     * 获取所有一致性规则
     * 
     * @return 一致性规则映射的副本
     */
    public Map<String, ConsistencyRule> getAllConsistencyRules() {
        return new HashMap<>(consistencyRules);
    }

    /**
     * 移除一致性规则
     * 
     * @param ruleName 规则名称
     * @return 被移除的规则，如果不存在返回null
     */
    public ConsistencyRule removeConsistencyRule(String ruleName) {
        ConsistencyRule removed = consistencyRules.remove(ruleName);

        if (removed != null) {
            // 重新计算字段依赖关系
            rebuildFieldDependencies();

            if (logger.isDebugEnabled()) {
                logger.debug("移除一致性规则: {}", ruleName);
            }
        }

        return removed;
    }

    /**
     * 获取一致性规则数量
     * 
     * @return 规则数量
     */
    public int getRuleCount() {
        return consistencyRules.size();
    }

    /**
     * 检查是否为空
     * 
     * @return 如果没有规则返回true，否则返回false
     */
    public boolean isEmpty() {
        return consistencyRules.isEmpty();
    }

    /**
     * 初始化默认一致性规则
     */
    private void initializeDefaultRules() {
        // 身份证号与年龄一致性规则
        registerConsistencyRule(ConsistencyRuleFactory.createIdCardAgeConsistencyRule());

        // 身份证号与性别一致性规则
        registerConsistencyRule(ConsistencyRuleFactory.createIdCardGenderConsistencyRule());

        // 姓名与性别一致性规则
        registerConsistencyRule(ConsistencyRuleFactory.createNameGenderConsistencyRule());

        // 邮箱与姓名一致性规则
        registerConsistencyRule(ConsistencyRuleFactory.createEmailNameConsistencyRule());

        logger.info("已初始化 {} 个默认一致性规则", consistencyRules.size());
    }

    /**
     * 重新构建字段依赖关系
     */
    private void rebuildFieldDependencies() {
        fieldDependencies.clear();

        for (ConsistencyRule rule : consistencyRules.values()) {
            for (String field : rule.getInvolvedFields()) {
                fieldDependencies.computeIfAbsent(field, k -> new HashSet<>())
                        .addAll(rule.getInvolvedFields());
            }
        }
    }

    @Override
    public String toString() {
        return String.format("ConsistencyManager{rules=%d, dependencies=%d}",
                consistencyRules.size(), fieldDependencies.size());
    }
}