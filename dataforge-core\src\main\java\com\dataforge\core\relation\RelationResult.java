package com.dataforge.core.relation;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * 关联结果
 * 
 * 封装字段关联执行的结果，包括成功状态、结果数据和错误信息。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class RelationResult {

    private final boolean success;
    private final Map<String, Object> resultData;
    private final String errorMessage;

    /**
     * 私有构造函数
     * 
     * @param success      是否成功
     * @param resultData   结果数据
     * @param errorMessage 错误信息
     */
    private RelationResult(boolean success, Map<String, Object> resultData, String errorMessage) {
        this.success = success;
        this.resultData = resultData != null ? Collections.unmodifiableMap(new HashMap<>(resultData))
                : Collections.emptyMap();
        this.errorMessage = errorMessage;
    }

    /**
     * 创建成功结果
     * 
     * @param resultData 结果数据
     * @return 成功结果
     */
    public static RelationResult success(Map<String, Object> resultData) {
        return new RelationResult(true, resultData, null);
    }

    /**
     * 创建成功结果（单个值）
     * 
     * @param fieldName 字段名
     * @param value     字段值
     * @return 成功结果
     */
    public static RelationResult success(String fieldName, Object value) {
        return new RelationResult(true, Collections.singletonMap(fieldName, value), null);
    }

    /**
     * 创建空成功结果
     * 
     * @return 空成功结果
     */
    public static RelationResult success() {
        return new RelationResult(true, Collections.emptyMap(), null);
    }

    /**
     * 创建失败结果
     * 
     * @param errorMessage 错误信息
     * @return 失败结果
     */
    public static RelationResult failure(String errorMessage) {
        return new RelationResult(false, Collections.emptyMap(), errorMessage);
    }

    /**
     * 检查是否成功
     * 
     * @return 如果成功返回true，否则返回false
     */
    public boolean isSuccess() {
        return success;
    }

    /**
     * 检查是否失败
     * 
     * @return 如果失败返回true，否则返回false
     */
    public boolean isFailure() {
        return !success;
    }

    /**
     * 获取结果数据
     * 
     * @return 结果数据映射
     */
    public Map<String, Object> getResultData() {
        return Collections.unmodifiableMap(resultData);
    }

    /**
     * 获取指定字段的值
     * 
     * @param fieldName 字段名
     * @return 字段值，如果不存在返回null
     */
    public Object getValue(String fieldName) {
        return resultData.get(fieldName);
    }

    /**
     * 获取指定字段的值（带类型转换）
     * 
     * @param fieldName 字段名
     * @param type      期望的类型
     * @param <T>       类型参数
     * @return 字段值，如果不存在或类型不匹配返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getValue(String fieldName, Class<T> type) {
        Object value = resultData.get(fieldName);
        if (value != null && type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }
        return null;
    }

    /**
     * 检查是否包含指定字段
     * 
     * @param fieldName 字段名
     * @return 如果包含返回true，否则返回false
     */
    public boolean hasField(String fieldName) {
        return resultData.containsKey(fieldName);
    }

    /**
     * 获取错误信息
     * 
     * @return 错误信息，如果成功返回null
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 获取结果数据的字段数量
     * 
     * @return 字段数量
     */
    public int size() {
        return resultData.size();
    }

    /**
     * 检查结果数据是否为空
     * 
     * @return 如果为空返回true，否则返回false
     */
    public boolean isEmpty() {
        return resultData.isEmpty();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        RelationResult that = (RelationResult) o;
        return success == that.success &&
                Objects.equals(resultData, that.resultData) &&
                Objects.equals(errorMessage, that.errorMessage);
    }

    @Override
    public int hashCode() {
        return Objects.hash(success, resultData, errorMessage);
    }

    @Override
    public String toString() {
        if (success) {
            return String.format("RelationResult{success=true, data=%s}", resultData);
        } else {
            return String.format("RelationResult{success=false, error='%s'}", errorMessage);
        }
    }
}