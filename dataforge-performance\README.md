# DataForge 性能测试套件

这个模块包含了DataForge数据生成工具的完整性能测试套件，使用JMH（Java Microbenchmark Harness）进行微基准测试，并提供性能回归检测功能。

## 功能特性

### 1. JMH微基准测试

- **DataGenerationBenchmark**: 各种数据生成器的性能基准测试
- **MemoryUsageBenchmark**: 内存使用和GC压力测试
- **LargeDataBenchmark**: 大数据量生成性能测试
- **CPUUtilizationBenchmark**: CPU利用率和多核扩展性测试

### 2. 性能回归测试

- **PerformanceBaseline**: 建立和维护性能基线
- 自动检测性能回归
- 历史性能数据追踪

### 3. 综合测试运行器

- **PerformanceTestRunner**: 统一的测试执行入口
- 支持多种测试模式
- 自动生成性能报告

## 快速开始

### 构建性能测试

```bash
mvn clean package -pl dataforge-performance
```

### 运行所有性能测试

```bash
java -jar dataforge-performance/target/dataforge-performance-benchmarks.jar all
```

### 运行快速测试

```bash
java -jar dataforge-performance/target/dataforge-performance-benchmarks.jar quick
```

## 测试类型

### 1. 基础性能测试

测试各种数据生成器的基本性能：

```bash
java -cp target/dataforge-performance-benchmarks.jar com.dataforge.performance.runner.PerformanceTestRunner quick
```

### 2. 内存性能测试

测试内存使用情况和GC压力：

```bash
java -cp target/dataforge-performance-benchmarks.jar com.dataforge.performance.runner.PerformanceTestRunner memory
```

### 3. 大数据量测试

测试大规模数据生成的性能：

```bash
java -cp target/dataforge-performance-benchmarks.jar com.dataforge.performance.runner.PerformanceTestRunner large
```

### 4. CPU利用率测试

测试CPU使用效率和多核扩展性：

```bash
java -cp target/dataforge-performance-benchmarks.jar com.dataforge.performance.runner.PerformanceTestRunner cpu
```

## 性能基线管理

### 建立性能基线

首次运行时需要建立性能基线：

```bash
java -cp target/dataforge-performance-benchmarks.jar com.dataforge.performance.runner.PerformanceTestRunner baseline
```

### 检查性能回归

```bash
java -cp target/dataforge-performance-benchmarks.jar com.dataforge.performance.runner.PerformanceTestRunner regression
```

### 更新性能基线

```bash
java -cp target/dataforge-performance-benchmarks.jar com.dataforge.performance.runner.PerformanceTestRunner update-baseline
```

## 直接运行JMH基准测试

### 运行所有基准测试

```bash
java -jar target/dataforge-performance-benchmarks.jar
```

### 运行特定基准测试

```bash
java -jar target/dataforge-performance-benchmarks.jar ".*DataGenerationBenchmark.*"
```

### 自定义JMH参数

```bash
java -jar target/dataforge-performance-benchmarks.jar -wi 5 -i 10 -f 2 ".*MemoryUsageBenchmark.*"
```

## 性能指标说明

### 吞吐量 (Throughput)

- 单位：操作数/秒 (ops/sec)
- 越高越好
- 表示每秒能完成的操作数量

### 平均时间 (Average Time)

- 单位：毫秒/操作 (ms/op)
- 越低越好
- 表示每个操作的平均耗时

### 内存使用

- 单位：字节 (bytes)
- 包括堆内存使用和每记录内存消耗
- 越低越好

### CPU利用率

- 单位：百分比 (%)
- 表示CPU资源的使用效率
- 需要在性能和资源消耗之间平衡

## 测试环境要求

### 硬件要求

- 最小内存：2GB
- 推荐内存：4GB或更多
- 多核CPU（用于并行测试）

### JVM参数建议

```bash
-Xmx4g -XX:+UseG1GC -XX:+UnlockExperimentalVMOptions
```

### Maven Profile

使用性能测试profile：

```bash
mvn test -Pbenchmark -pl dataforge-performance
```

## 结果分析

### 基准测试结果

- JSON格式结果文件：`benchmark-results-{timestamp}.json`
- 可以使用JMH Visualizer等工具进行可视化分析

### 性能基线数据

- 基线文件：`performance-baseline.properties`
- 历史数据：`baseline-history/` 目录

### 性能报告

生成HTML格式的性能报告：

```bash
java -cp target/dataforge-performance-benchmarks.jar com.dataforge.performance.runner.PerformanceTestRunner report
```

## 持续集成集成

### CI/CD脚本示例

```bash
#!/bin/bash
# 构建项目
mvn clean package -pl dataforge-performance

# 运行性能回归测试
java -cp dataforge-performance/target/dataforge-performance-benchmarks.jar \
  com.dataforge.performance.runner.PerformanceTestRunner regression

# 检查退出码
if [ $? -ne 0 ]; then
    echo "性能回归测试失败！"
    exit 1
fi

echo "性能测试通过"
```

### GitHub Actions示例

```yaml
- name: Run Performance Tests
  run: |
    mvn clean package -pl dataforge-performance
    java -cp dataforge-performance/target/dataforge-performance-benchmarks.jar \
      com.dataforge.performance.runner.PerformanceTestRunner regression
```

## 故障排除

### 常见问题

1. **内存不足错误**
   - 增加JVM堆内存：`-Xmx4g`
   - 减少测试数据量

2. **测试超时**
   - 增加JMH超时设置
   - 检查系统资源使用情况

3. **基线数据缺失**
   - 首先运行 `baseline` 命令建立基线
   - 检查 `performance-baseline.properties` 文件

### 调试模式

启用详细日志输出：

```bash
java -Dlogback.configurationFile=logback-debug.xml -jar target/dataforge-performance-benchmarks.jar
```

## 扩展开发

### 添加新的基准测试

1. 在 `com.dataforge.performance.benchmark` 包下创建新的测试类
2. 使用JMH注解标记测试方法
3. 重新构建项目

### 自定义性能指标

1. 扩展 `PerformanceBaseline.PerformanceMetrics` 类
2. 修改测量逻辑
3. 更新报告生成器

## 版本兼容性

- Java 17+
- JMH 1.37
- Maven 3.6+

## 许可证

本模块遵循与主项目相同的许可证。
