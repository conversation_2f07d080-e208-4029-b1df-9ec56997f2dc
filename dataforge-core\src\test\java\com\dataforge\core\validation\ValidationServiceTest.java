package com.dataforge.core.validation;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.ValidationConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * 数据校验服务单元测试
 */
@DisplayName("数据校验服务测试")
@ExtendWith(MockitoExtension.class)
class ValidationServiceTest {

    private ValidationService validationService;

    @BeforeEach
    void setUp() {
        validationService = new ValidationService();
    }

    @Test
    @DisplayName("身份证号校验测试")
    void testIdCardValidation() {
        // 有效身份证号
        String validIdCard = "11010519491231002X";
        ValidationResult result = validationService.validateIdCard(validIdCard);
        assertThat(result.isValid()).isTrue();
        assertThat(result.getErrors()).isEmpty();

        // 无效身份证号 - 长度错误
        String invalidIdCard = "1234567890";
        result = validationService.validateIdCard(invalidIdCard);
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).isNotEmpty();

        // 无效身份证号 - 校验位错误
        String wrongCheckDigit = "110105194912310021";
        result = validationService.validateIdCard(wrongCheckDigit);
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).contains("校验位错误");
    }

    @Test
    @DisplayName("银行卡号校验测试")
    void testBankCardValidation() {
        // 有效银行卡号（通过Luhn算法）
        String validBankCard = "****************";
        ValidationResult result = validationService.validateBankCard(validBankCard);
        assertThat(result.isValid()).isTrue();

        // 无效银行卡号 - Luhn算法失败
        String invalidBankCard = "****************";
        result = validationService.validateBankCard(invalidBankCard);
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).contains("Luhn算法校验失败");

        // 无效银行卡号 - 长度错误
        String shortBankCard = "411111";
        result = validationService.validateBankCard(shortBankCard);
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).contains("银行卡号长度不正确");
    }

    @Test
    @DisplayName("统一社会信用代码校验测试")
    void testUSCCValidation() {
        // 有效统一社会信用代码
        String validUSCC = "****************0A";
        ValidationResult result = validationService.validateUSCC(validUSCC);
        // 注意：这里需要根据实际实现的校验算法来验证
        assertThat(result).isNotNull();

        // 无效统一社会信用代码 - 长度错误
        String invalidUSCC = "****************";
        result = validationService.validateUSCC(invalidUSCC);
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).contains("长度不正确");

        // 无效字符
        String invalidCharUSCC = "****************0I"; // I是无效字符
        result = validationService.validateUSCC(invalidCharUSCC);
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).contains("包含无效字符");
    }

    @Test
    @DisplayName("手机号校验测试")
    void testPhoneValidation() {
        // 有效手机号
        String validPhone = "13800138000";
        ValidationResult result = validationService.validatePhone(validPhone);
        assertThat(result.isValid()).isTrue();

        // 无效手机号 - 长度错误
        String invalidPhone = "1380013800";
        result = validationService.validatePhone(invalidPhone);
        assertThat(result.isValid()).isFalse();

        // 无效手机号 - 不以1开头
        String wrongStartPhone = "23800138000";
        result = validationService.validatePhone(wrongStartPhone);
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).contains("手机号格式不正确");
    }

    @Test
    @DisplayName("邮箱地址校验测试")
    void testEmailValidation() {
        // 有效邮箱
        String validEmail = "<EMAIL>";
        ValidationResult result = validationService.validateEmail(validEmail);
        assertThat(result.isValid()).isTrue();

        // 无效邮箱 - 缺少@符号
        String invalidEmail = "testexample.com";
        result = validationService.validateEmail(invalidEmail);
        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).contains("邮箱格式不正确");

        // 无效邮箱 - 缺少域名
        String noDomainEmail = "test@";
        result = validationService.validateEmail(noDomainEmail);
        assertThat(result.isValid()).isFalse();
    }

    @Test
    @DisplayName("批量数据校验测试")
    void testBatchValidation() {
        List<Map<String, Object>> dataList = Arrays.asList(
                createDataRecord("idcard", "11010519491231002X"),
                createDataRecord("idcard", "invalid_id"),
                createDataRecord("phone", "13800138000"),
                createDataRecord("phone", "invalid_phone"));

        ValidationConfig config = new ValidationConfig();
        config.setEnabled(true);
        config.setStrictMode(true);

        BatchValidationResult result = validationService.validateBatch(dataList, config);

        assertThat(result).isNotNull();
        assertThat(result.getTotalCount()).isEqualTo(4);
        assertThat(result.getValidCount()).isEqualTo(2);
        assertThat(result.getInvalidCount()).isEqualTo(2);
    }

    @Test
    @DisplayName("记录级校验测试")
    void testRecordValidation() {
        Map<String, Object> record = new HashMap<>();
        record.put("idcard", "11010519491231002X");
        record.put("phone", "13800138000");
        record.put("email", "<EMAIL>");

        RecordValidationResult result = validationService.validateRecord(record);

        assertThat(result).isNotNull();
        assertThat(result.isValid()).isTrue();
        assertThat(result.getFieldResults()).hasSize(3);

        // 测试包含无效字段的记录
        record.put("phone", "invalid_phone");
        result = validationService.validateRecord(record);

        assertThat(result.isValid()).isFalse();
        assertThat(result.getFieldResults().get("phone").isValid()).isFalse();
    }

    @Test
    @DisplayName("校验统计信息测试")
    void testValidationStatistics() {
        List<Map<String, Object>> dataList = Arrays.asList(
                createDataRecord("idcard", "11010519491231002X"),
                createDataRecord("idcard", "invalid_id"),
                createDataRecord("phone", "13800138000"));

        ValidationConfig config = new ValidationConfig();
        config.setEnabled(true);

        BatchValidationResult result = validationService.validateBatch(dataList, config);
        ValidationStatistics stats = result.getStatistics();

        assertThat(stats).isNotNull();
        assertThat(stats.getTotalCount()).isEqualTo(3);
        assertThat(stats.getValidCount()).isEqualTo(2);
        assertThat(stats.getInvalidCount()).isEqualTo(1);
        assertThat(stats.getValidationRate()).isBetween(0.6, 0.7);
    }

    @Test
    @DisplayName("自定义校验规则测试")
    void testCustomValidationRules() {
        // 添加自定义校验规则
        validationService.addCustomRule("custom_field", value -> {
            if (value == null)
                return ValidationResult.failure("值不能为空");
            String str = value.toString();
            if (str.length() < 5) {
                return ValidationResult.failure("长度不能少于5个字符");
            }
            return ValidationResult.success();
        });

        Map<String, Object> record = new HashMap<>();
        record.put("custom_field", "test");

        RecordValidationResult result = validationService.validateRecord(record);
        assertThat(result.getFieldResults().get("custom_field").isValid()).isFalse();

        record.put("custom_field", "test_value");
        result = validationService.validateRecord(record);
        assertThat(result.getFieldResults().get("custom_field").isValid()).isTrue();
    }

    @Test
    @DisplayName("校验配置测试")
    void testValidationConfiguration() {
        ValidationConfig config = new ValidationConfig();
        config.setEnabled(false);

        List<Map<String, Object>> dataList = Arrays.asList(
                createDataRecord("idcard", "invalid_id"));

        BatchValidationResult result = validationService.validateBatch(dataList, config);

        // 当校验被禁用时，所有数据都应该被认为是有效的
        assertThat(result.getValidCount()).isEqualTo(1);
        assertThat(result.getInvalidCount()).isEqualTo(0);
    }

    @Test
    @DisplayName("严格模式校验测试")
    void testStrictModeValidation() {
        ValidationConfig strictConfig = new ValidationConfig();
        strictConfig.setEnabled(true);
        strictConfig.setStrictMode(true);

        ValidationConfig normalConfig = new ValidationConfig();
        normalConfig.setEnabled(true);
        normalConfig.setStrictMode(false);

        List<Map<String, Object>> dataList = Arrays.asList(
                createDataRecord("idcard", "11010519491231002X"));

        // 在严格模式下，校验应该更加严格
        BatchValidationResult strictResult = validationService.validateBatch(dataList, strictConfig);
        BatchValidationResult normalResult = validationService.validateBatch(dataList, normalConfig);

        assertThat(strictResult).isNotNull();
        assertThat(normalResult).isNotNull();
    }

    @Test
    @DisplayName("并发校验测试")
    void testConcurrentValidation() throws InterruptedException {
        List<Map<String, Object>> largeDataList = new java.util.ArrayList<>();
        for (int i = 0; i < 1000; i++) {
            largeDataList.add(createDataRecord("phone", "1380013800" + (i % 10)));
        }

        ValidationConfig config = new ValidationConfig();
        config.setEnabled(true);
        config.setParallelValidation(true);

        long startTime = System.currentTimeMillis();
        BatchValidationResult result = validationService.validateBatch(largeDataList, config);
        long endTime = System.currentTimeMillis();

        assertThat(result).isNotNull();
        assertThat(result.getTotalCount()).isEqualTo(1000);
        assertThat(endTime - startTime).isLessThan(5000); // 应该在5秒内完成
    }

    @Test
    @DisplayName("错误信息本地化测试")
    void testErrorMessageLocalization() {
        ValidationConfig config = new ValidationConfig();
        config.setEnabled(true);
        config.setLocale("zh_CN");

        String invalidIdCard = "invalid_id";
        ValidationResult result = validationService.validateIdCard(invalidIdCard);

        assertThat(result.isValid()).isFalse();
        assertThat(result.getErrors()).isNotEmpty();
        // 错误信息应该是中文
        assertThat(result.getErrors().get(0)).contains("格式");
    }

    @Test
    @DisplayName("性能测试")
    void testPerformance() {
        List<Map<String, Object>> dataList = new java.util.ArrayList<>();
        for (int i = 0; i < 10000; i++) {
            dataList.add(createDataRecord("phone", "13800138000"));
        }

        ValidationConfig config = new ValidationConfig();
        config.setEnabled(true);

        long startTime = System.currentTimeMillis();
        BatchValidationResult result = validationService.validateBatch(dataList, config);
        long endTime = System.currentTimeMillis();

        assertThat(result.getTotalCount()).isEqualTo(10000);
        assertThat(endTime - startTime).isLessThan(10000); // 应该在10秒内完成
    }

    private Map<String, Object> createDataRecord(String type, String value) {
        Map<String, Object> record = new HashMap<>();
        record.put(type, value);
        return record;
    }
}