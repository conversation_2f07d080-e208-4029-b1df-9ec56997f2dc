package com.dataforge.cli;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.model.PerformanceConfig;
import org.apache.commons.cli.*;

import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.Map;

/**
 * 命令行接口
 * 
 * 负责解析命令行参数，将其转换为GenerationConfig对象。
 * 支持各种数据类型的参数配置和输出选项。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class CommandLineInterface {

    private final Options options;
    private final CommandLineParser parser;

    /**
     * 构造函数
     */
    public CommandLineInterface() {
        this.options = createOptions();
        this.parser = new DefaultParser();
    }

    /**
     * 解析命令行参数
     * 
     * @param args 命令行参数数组
     * @return 生成配置对象
     * @throws ParseException 当参数解析失败时抛出
     */
    public GenerationConfig parseArguments(String[] args) throws ParseException {
        try {
            CommandLine cmd = parser.parse(options, args);
            GenerationConfig config = buildGenerationConfig(cmd);
            return config;
        } catch (ParseException e) {
            throw e;
        }
    }

    /**
     * 检查是否请求帮助信息
     * 
     * @param args 命令行参数数组
     * @return 如果请求帮助返回true，否则返回false
     */
    public boolean isHelpRequested(String[] args) {
        try {
            CommandLine cmd = parser.parse(options, args, true);
            return cmd.hasOption("help") || cmd.hasOption("h");
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 检查是否请求版本信息
     * 
     * @param args 命令行参数数组
     * @return 如果请求版本返回true，否则返回false
     */
    public boolean isVersionRequested(String[] args) {
        try {
            CommandLine cmd = parser.parse(options, args, true);
            return cmd.hasOption("version") || cmd.hasOption("v");
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 获取选项定义
     * 
     * @return Options对象
     */
    public Options getOptions() {
        return options;
    }

    /**
     * 创建命令行选项定义
     * 
     * @return Options对象
     */
    private Options createOptions() {
        Options opts = new Options();

        // 基本选项
        opts.addOption(Option.builder("h")
                .longOpt("help")
                .desc("显示帮助信息")
                .build());

        opts.addOption(Option.builder("v")
                .longOpt("version")
                .desc("显示版本信息")
                .build());

        opts.addOption(Option.builder("c")
                .longOpt("config")
                .hasArg()
                .argName("file")
                .desc("指定配置文件路径")
                .build());

        // 数据生成选项
        opts.addOption(Option.builder("t")
                .longOpt("type")
                .hasArg()
                .argName("type")
                .desc("数据类型 (name, phone, email, idcard, bankcard, etc.)")
                .build());

        opts.addOption(Option.builder("n")
                .longOpt("count")
                .hasArg()
                .argName("number")
                .type(Number.class)
                .desc("生成数据数量 (默认: 10)")
                .build());

        opts.addOption(Option.builder("s")
                .longOpt("seed")
                .hasArg()
                .argName("number")
                .type(Number.class)
                .desc("随机种子")
                .build());

        // 输出选项
        opts.addOption(Option.builder("f")
                .longOpt("format")
                .hasArg()
                .argName("format")
                .desc("输出格式 (csv, json, xml, sql, console)")
                .build());

        opts.addOption(Option.builder("o")
                .longOpt("output")
                .hasArg()
                .argName("file")
                .desc("输出文件路径")
                .build());

        opts.addOption(Option.builder()
                .longOpt("pretty")
                .desc("格式化输出 (适用于JSON/XML)")
                .build());

        opts.addOption(Option.builder()
                .longOpt("encoding")
                .hasArg()
                .argName("encoding")
                .desc("文件编码 (默认: UTF-8)")
                .build());

        // 校验选项
        opts.addOption(Option.builder()
                .longOpt("validate")
                .hasArg()
                .argName("boolean")
                .desc("是否启用数据校验 (true/false, 默认: true)")
                .build());

        opts.addOption(Option.builder()
                .longOpt("strict")
                .desc("启用严格校验模式")
                .build());

        opts.addOption(Option.builder()
                .longOpt("skip-invalid")
                .desc("跳过无效数据")
                .build());

        // 性能选项
        opts.addOption(Option.builder()
                .longOpt("threads")
                .hasArg()
                .argName("number")
                .type(Number.class)
                .desc("线程池大小")
                .build());

        opts.addOption(Option.builder()
                .longOpt("batch-size")
                .hasArg()
                .argName("number")
                .type(Number.class)
                .desc("批处理大小")
                .build());

        opts.addOption(Option.builder()
                .longOpt("no-parallel")
                .desc("禁用并行处理")
                .build());

        // 数据类型特定选项
        addDataTypeSpecificOptions(opts);

        return opts;
    }

    /**
     * 添加数据类型特定的选项
     * 
     * @param opts Options对象
     */
    private void addDataTypeSpecificOptions(Options opts) {
        // 姓名相关选项
        opts.addOption(Option.builder()
                .longOpt("name.type")
                .hasArg()
                .argName("type")
                .desc("姓名类型 (CN, EN, BOTH)")
                .build());

        opts.addOption(Option.builder()
                .longOpt("name.gender")
                .hasArg()
                .argName("gender")
                .desc("性别 (MALE, FEMALE, ANY)")
                .build());

        // 手机号相关选项
        opts.addOption(Option.builder()
                .longOpt("phone.region")
                .hasArg()
                .argName("region")
                .desc("手机号地区 (CN)")
                .build());

        opts.addOption(Option.builder()
                .longOpt("phone.prefix")
                .hasArg()
                .argName("prefixes")
                .desc("手机号前缀列表 (逗号分隔)")
                .build());

        // 身份证相关选项
        opts.addOption(Option.builder()
                .longOpt("idcard.region")
                .hasArg()
                .argName("code")
                .desc("身份证地区代码")
                .build());

        opts.addOption(Option.builder()
                .longOpt("idcard.birth-date-range")
                .hasArg()
                .argName("range")
                .desc("出生日期范围 (YYYY-MM-DD,YYYY-MM-DD)")
                .build());

        opts.addOption(Option.builder()
                .longOpt("idcard.gender")
                .hasArg()
                .argName("gender")
                .desc("身份证性别 (MALE, FEMALE, ANY)")
                .build());

        // 银行卡相关选项
        opts.addOption(Option.builder()
                .longOpt("bankcard.type")
                .hasArg()
                .argName("type")
                .desc("银行卡类型 (DEBIT, CREDIT, BOTH)")
                .build());

        opts.addOption(Option.builder()
                .longOpt("bankcard.issuer")
                .hasArg()
                .argName("issuer")
                .desc("卡组织 (VISA, MC, UNIONPAY, JCB, ANY)")
                .build());

        // 邮箱相关选项
        opts.addOption(Option.builder()
                .longOpt("email.domains")
                .hasArg()
                .argName("domains")
                .desc("邮箱域名列表 (逗号分隔)")
                .build());

        opts.addOption(Option.builder()
                .longOpt("email.username-length")
                .hasArg()
                .argName("range")
                .desc("用户名长度范围 (min,max)")
                .build());

        // 年龄相关选项
        opts.addOption(Option.builder()
                .longOpt("age.min")
                .hasArg()
                .argName("number")
                .type(Number.class)
                .desc("最小年龄")
                .build());

        opts.addOption(Option.builder()
                .longOpt("age.max")
                .hasArg()
                .argName("number")
                .type(Number.class)
                .desc("最大年龄")
                .build());

        // 通用选项
        opts.addOption(Option.builder()
                .longOpt("valid")
                .hasArg()
                .argName("boolean")
                .desc("生成有效数据 (true/false)")
                .build());
    }

    /**
     * 根据解析的命令行构建生成配置
     * 
     * @param cmd 解析后的命令行
     * @return 生成配置对象
     */
    private GenerationConfig buildGenerationConfig(CommandLine cmd) {
        GenerationConfig config = new GenerationConfig();

        // 基本配置
        if (cmd.hasOption("type")) {
            config.setDataType(cmd.getOptionValue("type"));
        }

        if (cmd.hasOption("count")) {
            try {
                int count = ((Number) cmd.getParsedOptionValue("count")).intValue();
                config.setCount(count);
            } catch (ParseException e) {
                // 使用默认值
            }
        }

        if (cmd.hasOption("seed")) {
            try {
                long seed = ((Number) cmd.getParsedOptionValue("seed")).longValue();
                config.setSeed(seed);
            } catch (ParseException e) {
                // 忽略无效种子参数
            }
        }

        // 构建参数映射
        Map<String, Object> parameters = buildParameters(cmd);
        config.setParameters(parameters);

        // 输出配置
        OutputConfig outputConfig = buildOutputConfig(cmd);
        config.setOutputConfig(outputConfig);

        // 校验配置
        ValidationConfig validationConfig = buildValidationConfig(cmd);
        config.setValidationConfig(validationConfig);

        // 性能配置
        PerformanceConfig performanceConfig = buildPerformanceConfig(cmd);
        config.setPerformanceConfig(performanceConfig);

        return config;
    }

    /**
     * 构建参数映射
     * 
     * @param cmd 解析后的命令行
     * @return 参数映射
     */
    private Map<String, Object> buildParameters(CommandLine cmd) {
        Map<String, Object> parameters = new HashMap<>();

        // 遍历所有选项，提取参数
        for (Option option : cmd.getOptions()) {
            String longOpt = option.getLongOpt();
            if (longOpt != null && longOpt.contains(".")) {
                // 这是一个数据类型特定的参数
                String value = option.getValue();
                if (value != null) {
                    parameters.put(longOpt, parseParameterValue(value));
                } else if (!option.hasArg()) {
                    // 布尔选项
                    parameters.put(longOpt, true);
                }
            }
        }

        // 处理通用参数
        if (cmd.hasOption("valid")) {
            parameters.put("valid", Boolean.parseBoolean(cmd.getOptionValue("valid", "true")));
        }

        return parameters;
    }

    /**
     * 构建输出配置
     * 
     * @param cmd 解析后的命令行
     * @return 输出配置对象
     */
    private OutputConfig buildOutputConfig(CommandLine cmd) {
        OutputConfig outputConfig = new OutputConfig();

        if (cmd.hasOption("format")) {
            outputConfig.setFormat(cmd.getOptionValue("format"));
        }

        if (cmd.hasOption("output")) {
            outputConfig.setFile(cmd.getOptionValue("output"));
        }

        if (cmd.hasOption("pretty")) {
            outputConfig.setPretty(true);
        }

        if (cmd.hasOption("encoding")) {
            outputConfig.setEncoding(cmd.getOptionValue("encoding"));
        }

        return outputConfig;
    }

    /**
     * 构建校验配置
     * 
     * @param cmd 解析后的命令行
     * @return 校验配置对象
     */
    private ValidationConfig buildValidationConfig(CommandLine cmd) {
        ValidationConfig validationConfig = new ValidationConfig();

        if (cmd.hasOption("validate")) {
            boolean enabled = Boolean.parseBoolean(cmd.getOptionValue("validate", "true"));
            validationConfig.setEnabled(enabled);
        }

        if (cmd.hasOption("strict")) {
            validationConfig.setStrictMode(true);
        }

        if (cmd.hasOption("skip-invalid")) {
            validationConfig.setSkipInvalidData(true);
        }

        return validationConfig;
    }

    /**
     * 构建性能配置
     * 
     * @param cmd 解析后的命令行
     * @return 性能配置对象
     */
    private PerformanceConfig buildPerformanceConfig(CommandLine cmd) {
        PerformanceConfig performanceConfig = new PerformanceConfig();

        if (cmd.hasOption("threads")) {
            try {
                int threads = ((Number) cmd.getParsedOptionValue("threads")).intValue();
                performanceConfig.setThreadPoolSize(threads);
            } catch (ParseException e) {
                // 忽略无效线程数参数
            }
        }

        if (cmd.hasOption("batch-size")) {
            try {
                int batchSize = ((Number) cmd.getParsedOptionValue("batch-size")).intValue();
                performanceConfig.setBatchSize(batchSize);
            } catch (ParseException e) {
                // 忽略无效批处理大小参数
            }
        }

        if (cmd.hasOption("no-parallel")) {
            performanceConfig.setEnableParallel(false);
        }

        return performanceConfig;
    }

    /**
     * 解析参数值
     * 
     * @param value 参数值字符串
     * @return 解析后的参数值
     */
    private Object parseParameterValue(String value) {
        if (value == null) {
            return null;
        }

        // 尝试解析为数字
        try {
            if (value.contains(".")) {
                return Double.parseDouble(value);
            } else {
                return Integer.parseInt(value);
            }
        } catch (NumberFormatException e) {
            // 不是数字，继续其他类型的解析
        }

        // 尝试解析为布尔值
        if ("true".equalsIgnoreCase(value) || "false".equalsIgnoreCase(value)) {
            return Boolean.parseBoolean(value);
        }

        // 默认返回字符串
        return value;
    }
}