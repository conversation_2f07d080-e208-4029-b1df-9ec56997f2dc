# DataForge示例配置文件
# 此文件展示了如何配置DataForge来生成不同类型的数据

# 基本配置
dataType: "name"          # 数据类型：name, phone, email, address, number
count: 100               # 生成数量
seed: 12345              # 随机种子（可选，用于可重现的结果）
requestId: "sample-001"  # 请求ID（可选，用于跟踪）

# 生成器参数
parameters:
  gender: "random"       # 姓名生成器：male, female, random
  nameLength: 2          # 姓名长度：1-3

# 字段配置（用于复杂数据结构）
fields:
  - name: "name"
    type: "name"
    parameters:
      gender: "female"
      nameLength: 2
  - name: "phone"
    type: "phone"
    parameters:
      carrier: "mobile"
      format: "dash"
  - name: "email"
    type: "email"
    parameters:
      domainType: "common"
      usernameLength: 8

# 输出配置
outputConfig:
  format: "json"         # 输出格式：json, csv, xml, txt
  filePath: "./output/sample-data.json"
  encoding: "UTF-8"
  prettyPrint: true
  includeMetadata: true

# 验证配置
validationConfig:
  enabled: true
  skipInvalidData: true
  maxErrors: 10

# 性能配置
performanceConfig:
  useParallel: true
  threadCount: 4
  batchSize: 1000
  memoryLimit: "256MB"

---
# 手机号生成示例
dataType: "phone"
count: 50
parameters:
  carrier: "mobile"      # 运营商：mobile, unicom, telecom, random
  format: "dash"         # 格式：plain, dash, space, parentheses

outputConfig:
  format: "csv"
  filePath: "./output/phones.csv"

---
# 邮箱生成示例
dataType: "email"
count: 200
parameters:
  domainType: "common"   # 域名类型：common, business, custom
  usernameType: "name"   # 用户名类型：random, name, prefix
  usernameLength: 10
  includeNumbers: true
  includeDots: false

outputConfig:
  format: "txt"
  filePath: "./output/emails.txt"

---
# 地址生成示例
dataType: "address"
count: 30
parameters:
  format: "full"         # 格式：province, city, district, full
  includePostalCode: true
  customProvince: "北京市"

outputConfig:
  format: "json"
  filePath: "./output/addresses.json"
  prettyPrint: true

---
# 数字生成示例
dataType: "number"
count: 1000
parameters:
  numberType: "decimal"  # 类型：integer, decimal
  min: 0.0
  max: 1000.0
  precision: 2
  format: "comma"        # 格式：plain, comma, currency, percentage
  distribution: "normal" # 分布：uniform, normal, exponential

outputConfig:
  format: "csv"
  filePath: "./output/numbers.csv"

performanceConfig:
  useParallel: true
  threadCount: 2