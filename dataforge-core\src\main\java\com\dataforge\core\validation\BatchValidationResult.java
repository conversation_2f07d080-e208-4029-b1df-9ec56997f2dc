package com.dataforge.core.validation;

import java.util.Collections;
import java.util.List;

/**
 * 批量校验结果
 * 
 * 封装批量数据校验的结果，包括所有单个校验结果和统计信息。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class BatchValidationResult {

    private final List<ValidationResult> results;
    private final int successCount;
    private final int failureCount;
    private final String message;

    /**
     * 构造函数
     * 
     * @param results      校验结果列表
     * @param successCount 成功数量
     * @param failureCount 失败数量
     */
    public BatchValidationResult(List<ValidationResult> results, int successCount, int failureCount) {
        this(results, successCount, failureCount, null);
    }

    /**
     * 构造函数
     * 
     * @param results      校验结果列表
     * @param successCount 成功数量
     * @param failureCount 失败数量
     * @param message      消息
     */
    public BatchValidationResult(List<ValidationResult> results, int successCount, int failureCount, String message) {
        this.results = results != null ? List.copyOf(results) : Collections.emptyList();
        this.successCount = successCount;
        this.failureCount = failureCount;
        this.message = message;
    }

    /**
     * 创建空的批量校验结果
     * 
     * @param message 消息
     * @return 空的批量校验结果
     */
    public static BatchValidationResult empty(String message) {
        return new BatchValidationResult(Collections.emptyList(), 0, 0, message);
    }

    /**
     * 获取所有校验结果
     * 
     * @return 校验结果列表
     */
    public List<ValidationResult> getResults() {
        return results;
    }

    /**
     * 获取成功数量
     * 
     * @return 成功数量
     */
    public int getSuccessCount() {
        return successCount;
    }

    /**
     * 获取失败数量
     * 
     * @return 失败数量
     */
    public int getFailureCount() {
        return failureCount;
    }

    /**
     * 获取有效数量（兼容方法）
     * 
     * @return 有效数量
     */
    public int getValidCount() {
        return successCount;
    }

    /**
     * 获取无效数量（兼容方法）
     * 
     * @return 无效数量
     */
    public int getInvalidCount() {
        return failureCount;
    }

    /**
     * 获取统计信息（兼容方法）
     * 
     * @return 统计信息
     */
    public ValidationStatistics getStatistics() {
        return new ValidationStatistics(getTotalCount(), successCount, failureCount, null);
    }

    /**
     * 获取总数量
     * 
     * @return 总数量
     */
    public int getTotalCount() {
        return successCount + failureCount;
    }

    /**
     * 获取成功率
     * 
     * @return 成功率（0.0-1.0）
     */
    public double getSuccessRate() {
        int total = getTotalCount();
        return total > 0 ? (double) successCount / total : 0.0;
    }

    /**
     * 获取失败率
     * 
     * @return 失败率（0.0-1.0）
     */
    public double getFailureRate() {
        int total = getTotalCount();
        return total > 0 ? (double) failureCount / total : 0.0;
    }

    /**
     * 获取消息
     * 
     * @return 消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 检查是否有消息
     * 
     * @return 如果有消息返回true，否则返回false
     */
    public boolean hasMessage() {
        return message != null && !message.trim().isEmpty();
    }

    /**
     * 检查是否为空
     * 
     * @return 如果为空返回true，否则返回false
     */
    public boolean isEmpty() {
        return results.isEmpty();
    }

    /**
     * 检查是否全部成功
     * 
     * @return 如果全部成功返回true，否则返回false
     */
    public boolean isAllSuccess() {
        return !isEmpty() && failureCount == 0;
    }

    /**
     * 检查是否全部失败
     * 
     * @return 如果全部失败返回true，否则返回false
     */
    public boolean isAllFailure() {
        return !isEmpty() && successCount == 0;
    }

    /**
     * 检查是否有失败
     * 
     * @return 如果有失败返回true，否则返回false
     */
    public boolean hasFailures() {
        return failureCount > 0;
    }

    /**
     * 获取失败的校验结果
     * 
     * @return 失败的校验结果列表
     */
    public List<ValidationResult> getFailureResults() {
        return results.stream()
                .filter(ValidationResult::isInvalid)
                .toList();
    }

    /**
     * 获取成功的校验结果
     * 
     * @return 成功的校验结果列表
     */
    public List<ValidationResult> getSuccessResults() {
        return results.stream()
                .filter(ValidationResult::isValid)
                .toList();
    }

    /**
     * 获取指定索引的校验结果
     * 
     * @param index 索引
     * @return 校验结果，如果索引无效返回null
     */
    public ValidationResult getResult(int index) {
        if (index >= 0 && index < results.size()) {
            return results.get(index);
        }
        return null;
    }

    /**
     * 获取摘要信息
     * 
     * @return 摘要信息
     */
    public String getSummary() {
        if (isEmpty()) {
            return hasMessage() ? message : "无数据校验";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("总计: ").append(getTotalCount()).append("条");
        sb.append(", 成功: ").append(successCount).append("条");
        sb.append(", 失败: ").append(failureCount).append("条");
        sb.append(", 成功率: ").append(String.format("%.1f%%", getSuccessRate() * 100));

        if (hasMessage()) {
            sb.append(" (").append(message).append(")");
        }

        return sb.toString();
    }

    @Override
    public String toString() {
        return String.format("BatchValidationResult{total=%d, success=%d, failure=%d, rate=%.1f%%}",
                getTotalCount(), successCount, failureCount, getSuccessRate() * 100);
    }
}