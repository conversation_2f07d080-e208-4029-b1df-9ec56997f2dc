# DataForge 数据关联配置示例
# 展示如何配置字段之间的关联关系

# 数据生成配置
generation:
  count: 100
  output:
    format: csv
    file: "generated_data.csv"

# 字段定义
fields:
  - name: "idcard"
    type: "identifier"
    generator: "IdCardNumberGenerator"
    parameters:
      region: "110101"  # 北京市东城区
      ageRange: "20-60"
    
  - name: "name"
    type: "text"
    generator: "NameGenerator"
    parameters:
      locale: "zh_CN"
      gender: "auto"  # 根据身份证号自动确定
    
  - name: "phone"
    type: "text"
    generator: "PhoneGenerator"
    parameters:
      format: "mobile"
      region: "CN"
    
  - name: "email"
    type: "text"
    generator: "EmailGenerator"
    parameters:
      domain: "example.com"
      usernameSource: "name"  # 基于姓名生成用户名

# 字段关联规则配置
relations:
  # 身份证号关联规则
  - source: "idcard"
    type: "extraction"
    targets:
      - field: "age"
        rule: "extractAgeFromIdCard"
      - field: "gender"
        rule: "extractGenderFromIdCard"
      - field: "birth_date"
        rule: "extractBirthDateFromIdCard"
      - field: "region_code"
        rule: "extractRegionFromIdCard"
    description: "从身份证号提取基本信息"
    
  # 姓名关联规则
  - source: "name"
    type: "extraction"
    targets:
      - field: "surname"
        rule: "extractSurname"
      - field: "given_name"
        rule: "extractGivenName"
      - field: "name_pinyin"
        rule: "generatePinyin"
    description: "从姓名提取姓氏、名字和拼音"
    
  # 邮箱用户名关联规则
  - source: "name"
    type: "formatting"
    targets:
      - field: "email_username"
        rule: "generateEmailUsername"
    description: "基于姓名生成邮箱用户名"
    
  # 年龄关联规则
  - source: "age"
    type: "calculation"
    targets:
      - field: "birth_year"
        rule: "calculateBirthYear"
    description: "根据年龄计算出生年份"

# 数据一致性规则
consistency:
  # 确保性别与姓名的一致性
  - rule: "genderNameConsistency"
    description: "确保生成的姓名与身份证号中的性别信息一致"
    fields: ["idcard", "name", "gender"]
    
  # 确保年龄与身份证号的一致性
  - rule: "ageIdCardConsistency"
    description: "确保年龄与身份证号中的出生日期一致"
    fields: ["idcard", "age", "birth_date"]

# 验证规则
validation:
  enabled: true
  rules:
    - field: "idcard"
      validator: "IdCardValidator"
      description: "验证身份证号格式和校验位"
      
    - field: "phone"
      validator: "PhoneValidator"
      description: "验证手机号格式"
      
    - field: "email"
      validator: "EmailValidator"
      description: "验证邮箱格式"
      
    - field: "age"
      validator: "RangeValidator"
      parameters:
        min: 18
        max: 65
      description: "验证年龄范围"

# 输出字段配置
output:
  fields:
    - "idcard"
    - "name"
    - "age"
    - "gender"
    - "phone"
    - "email"
    - "birth_date"
    - "region_code"
  headers: true
  encoding: "UTF-8"