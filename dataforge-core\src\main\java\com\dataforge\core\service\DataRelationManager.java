package com.dataforge.core.service;

import com.dataforge.core.relation.FieldRelation;
import com.dataforge.core.relation.RelationResult;
import com.dataforge.core.relation.RelationRuleFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;

/**
 * 数据关联管理器
 * 
 * 负责管理数据字段之间的关联关系，确保生成数据的逻辑一致性。
 * 例如：身份证号与年龄、性别、出生日期的关联，地址与邮编的关联等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class DataRelationManager {

    private static final Logger logger = LoggerFactory.getLogger(DataRelationManager.class);

    private final Map<String, Object> sharedContext;
    private final Map<String, Function<Object, Object>> relationRules;
    private final Map<String, FieldRelation> fieldRelations;

    /**
     * 构造函数
     */
    public DataRelationManager() {
        this.sharedContext = new ConcurrentHashMap<>();
        this.relationRules = new ConcurrentHashMap<>();
        this.fieldRelations = new ConcurrentHashMap<>();
        initializeDefaultRules();
        initializeDefaultFieldRelations();
    }

    /**
     * 设置关联值
     * 
     * @param key   关联键
     * @param value 关联值
     */
    public void setRelatedValue(String key, Object value) {
        if (key == null) {
            logger.warn("尝试设置关联值时键为空");
            return;
        }

        Object oldValue = sharedContext.put(key, value);

        if (logger.isDebugEnabled()) {
            logger.debug("设置关联值: {} = {} (旧值: {})", key, value, oldValue);
        }

        // 触发相关联的值更新
        triggerRelatedUpdates(key, value);
    }

    /**
     * 获取关联值
     * 
     * @param key  关联键
     * @param type 值类型
     * @param <T>  类型参数
     * @return 关联值，如果不存在返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getRelatedValue(String key, Class<T> type) {
        Object value = sharedContext.get(key);
        if (value == null) {
            return null;
        }

        if (type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }

        logger.warn("关联值类型不匹配: key={}, 期望类型={}, 实际类型={}",
                key, type.getSimpleName(), value.getClass().getSimpleName());
        return null;
    }

    /**
     * 获取关联值
     * 
     * @param key 关联键
     * @return 关联值，如果不存在返回null
     */
    public Object getRelatedValue(String key) {
        return sharedContext.get(key);
    }

    /**
     * 检查是否存在关联
     * 
     * @param key 关联键
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasRelation(String key) {
        return sharedContext.containsKey(key);
    }

    /**
     * 移除关联值
     * 
     * @param key 关联键
     * @return 被移除的值，如果不存在返回null
     */
    public Object removeRelatedValue(String key) {
        Object removedValue = sharedContext.remove(key);

        if (logger.isDebugEnabled() && removedValue != null) {
            logger.debug("移除关联值: {} = {}", key, removedValue);
        }

        return removedValue;
    }

    /**
     * 清空所有关联值
     */
    public void clearAll() {
        int size = sharedContext.size();
        sharedContext.clear();

        if (logger.isDebugEnabled()) {
            logger.debug("清空所有关联值，共清空 {} 个", size);
        }
    }

    /**
     * 注册关联规则
     * 
     * @param sourceKey 源键
     * @param rule      关联规则函数
     */
    public void registerRelationRule(String sourceKey, Function<Object, Object> rule) {
        relationRules.put(sourceKey, rule);

        if (logger.isDebugEnabled()) {
            logger.debug("注册关联规则: {}", sourceKey);
        }
    }

    /**
     * 注册字段关联
     * 
     * @param fieldRelation 字段关联
     */
    public void registerFieldRelation(FieldRelation fieldRelation) {
        if (fieldRelation == null) {
            logger.warn("尝试注册空的字段关联");
            return;
        }

        fieldRelations.put(fieldRelation.getSourceField(), fieldRelation);

        if (logger.isDebugEnabled()) {
            logger.debug("注册字段关联: {}", fieldRelation);
        }
    }

    /**
     * 获取字段关联
     * 
     * @param sourceField 源字段名
     * @return 字段关联，如果不存在返回null
     */
    public FieldRelation getFieldRelation(String sourceField) {
        return fieldRelations.get(sourceField);
    }

    /**
     * 获取所有字段关联
     * 
     * @return 字段关联映射的副本
     */
    public Map<String, FieldRelation> getAllFieldRelations() {
        return new HashMap<>(fieldRelations);
    }

    /**
     * 移除字段关联
     * 
     * @param sourceField 源字段名
     * @return 被移除的字段关联，如果不存在返回null
     */
    public FieldRelation removeFieldRelation(String sourceField) {
        FieldRelation removed = fieldRelations.remove(sourceField);

        if (logger.isDebugEnabled() && removed != null) {
            logger.debug("移除字段关联: {}", removed);
        }

        return removed;
    }

    /**
     * 检查是否存在字段关联
     * 
     * @param sourceField 源字段名
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasFieldRelation(String sourceField) {
        return fieldRelations.containsKey(sourceField);
    }

    /**
     * 获取所有关联值的副本
     * 
     * @return 关联值映射的副本
     */
    public Map<String, Object> getAllRelatedValues() {
        return new ConcurrentHashMap<>(sharedContext);
    }

    /**
     * 获取关联值数量
     * 
     * @return 关联值数量
     */
    public int size() {
        return sharedContext.size();
    }

    /**
     * 检查是否为空
     * 
     * @return 如果为空返回true，否则返回false
     */
    public boolean isEmpty() {
        return sharedContext.isEmpty();
    }

    /**
     * 初始化默认关联规则
     */
    private void initializeDefaultRules() {
        // 旧的关联规则已被新的FieldRelation系统替代
        // 保留此方法以维持向后兼容性，但不注册冲突的规则

        // 地址相关关联规则（暂时保留，因为新系统中地址解析较简单）
        registerRelationRule("address", this::extractInfoFromAddress);
    }

    /**
     * 初始化默认字段关联
     */
    private void initializeDefaultFieldRelations() {
        // 注册身份证号关联
        registerFieldRelation(RelationRuleFactory.createIdCardRelation());

        // 注册姓名关联
        registerFieldRelation(RelationRuleFactory.createNameRelation());

        // 注册邮箱关联
        registerFieldRelation(RelationRuleFactory.createEmailRelation());

        // 注册地址关联
        registerFieldRelation(RelationRuleFactory.createAddressRelation());

        // 注册年龄关联
        registerFieldRelation(RelationRuleFactory.createAgeRelation());

        logger.info("已初始化 {} 个默认字段关联", fieldRelations.size());
    }

    /**
     * 触发相关联的值更新
     * 
     * @param sourceKey   源键
     * @param sourceValue 源值
     */
    private void triggerRelatedUpdates(String sourceKey, Object sourceValue) {
        // 处理新的字段关联
        FieldRelation fieldRelation = fieldRelations.get(sourceKey);
        if (fieldRelation != null) {
            try {
                RelationResult result = fieldRelation.execute(sourceValue);
                if (result.isSuccess()) {
                    // 将关联结果设置到共享上下文中
                    for (Map.Entry<String, Object> entry : result.getResultData().entrySet()) {
                        String targetField = entry.getKey();
                        Object targetValue = entry.getValue();

                        // 避免循环更新
                        if (!targetField.equals(sourceKey)) {
                            Object oldValue = sharedContext.put(targetField, targetValue);

                            if (logger.isDebugEnabled()) {
                                logger.debug("字段关联更新: {} -> {} = {} (旧值: {})",
                                        sourceKey, targetField, targetValue, oldValue);
                            }
                        }
                    }
                } else {
                    logger.warn("字段关联执行失败: sourceKey={}, error={}", sourceKey, result.getErrorMessage());
                }
            } catch (Exception e) {
                logger.error("执行字段关联失败: sourceKey={}, sourceValue={}", sourceKey, sourceValue, e);
            }
        }

        // 处理旧的关联规则（向后兼容）
        Function<Object, Object> rule = relationRules.get(sourceKey);
        if (rule != null) {
            try {
                Object relatedValue = rule.apply(sourceValue);
                if (relatedValue != null) {
                    logger.debug("触发关联更新: {} -> {}", sourceKey, relatedValue);
                }
            } catch (Exception e) {
                logger.error("执行关联规则失败: sourceKey={}, sourceValue={}", sourceKey, sourceValue, e);
            }
        }
    }

    /**
     * 从身份证号提取信息
     * 
     * @param idCard 身份证号
     * @return 提取的信息
     */
    private Object extractInfoFromIdCard(Object idCard) {
        if (idCard == null) {
            return null;
        }

        String idCardStr = idCard.toString();
        if (idCardStr.length() != 18) {
            return null;
        }

        try {
            // 提取出生日期
            String birthDate = idCardStr.substring(6, 14);
            setRelatedValue("birth_date", birthDate);

            // 提取性别（倒数第二位，奇数为男，偶数为女）
            int genderCode = Integer.parseInt(idCardStr.substring(16, 17));
            String gender = (genderCode % 2 == 1) ? "MALE" : "FEMALE";
            setRelatedValue("gender", gender);

            // 提取地区代码
            String regionCode = idCardStr.substring(0, 6);
            setRelatedValue("region_code", regionCode);

            return "extracted";
        } catch (Exception e) {
            logger.warn("从身份证号提取信息失败: {}", idCardStr, e);
            return null;
        }
    }

    /**
     * 从地址提取信息
     * 
     * @param address 地址
     * @return 提取的信息
     */
    private Object extractInfoFromAddress(Object address) {
        if (address == null) {
            return null;
        }

        // 这里可以实现地址解析逻辑，提取省市区信息
        // 实际实现中需要根据地址格式和数据源来解析

        return "extracted";
    }

    /**
     * 从姓名提取信息
     * 
     * @param name 姓名
     * @return 提取的信息
     */
    private Object extractInfoFromName(Object name) {
        if (name == null) {
            return null;
        }

        String nameStr = name.toString();

        // 提取姓氏和名字
        if (nameStr.length() >= 2) {
            String surname = nameStr.substring(0, 1);
            String givenName = nameStr.substring(1);

            setRelatedValue("surname", surname);
            setRelatedValue("given_name", givenName);

            // 生成拼音（简化实现）
            // 实际实现中需要使用拼音转换库
            setRelatedValue("name_pinyin", generatePinyin(nameStr));
        }

        return "extracted";
    }

    /**
     * 生成拼音（简化实现）
     * 
     * @param name 姓名
     * @return 拼音
     */
    private String generatePinyin(String name) {
        // 这里是简化实现，实际应该使用专业的拼音转换库
        return name.toLowerCase().replaceAll("[^a-zA-Z]", "");
    }

    @Override
    public String toString() {
        return String.format("DataRelationManager{contextSize=%d, legacyRules=%d, fieldRelations=%d}",
                sharedContext.size(), relationRules.size(), fieldRelations.size());
    }
}