package com.dataforge.core.service;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.model.PerformanceConfig;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.dataformat.yaml.YAMLFactory;

import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置管理服务
 * 
 * 负责加载、保存和管理配置文件，支持YAML和JSON格式。
 * 提供配置合并、验证和转换功能，实现配置优先级管理。
 * 
 * 配置优先级（从高到低）：
 * 1. CLI参数（最高优先级）
 * 2. 配置文件参数
 * 3. 默认参数（最低优先级）
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class ConfigurationManager {

    private final ObjectMapper yamlMapper;
    private final ObjectMapper jsonMapper;

    /**
     * 构造函数
     */
    public ConfigurationManager() {
        this.yamlMapper = new ObjectMapper(new YAMLFactory());
        this.jsonMapper = new ObjectMapper();
    }

    /**
     * 从文件加载配置
     * 
     * @param filePath 配置文件路径
     * @return 生成配置对象
     * @throws IOException 当文件读取或解析失败时抛出
     */
    public GenerationConfig loadFromFile(String filePath) throws IOException {
        Path path = Paths.get(filePath);
        if (!Files.exists(path)) {
            throw new IOException("配置文件不存在: " + filePath);
        }

        try {
            ObjectMapper mapper = selectMapper(filePath);
            GenerationConfig config = mapper.readValue(path.toFile(), GenerationConfig.class);
            return config;
        } catch (Exception e) {
            throw new IOException("配置文件解析失败: " + e.getMessage(), e);
        }
    }

    /**
     * 加载配置（兼容方法）
     * 
     * @param filePath 配置文件路径
     * @return 生成配置对象
     * @throws IOException 当文件读取或解析失败时抛出
     */
    public GenerationConfig loadConfiguration(String filePath) throws IOException {
        return loadFromFile(filePath);
    }

    /**
     * 保存配置到文件
     * 
     * @param config   生成配置对象
     * @param filePath 文件路径
     * @throws IOException 当文件写入失败时抛出
     */
    public void saveToFile(GenerationConfig config, String filePath) throws IOException {
        try {
            ObjectMapper mapper = selectMapper(filePath);
            mapper.writerWithDefaultPrettyPrinter().writeValue(new File(filePath), config);
        } catch (Exception e) {
            throw new IOException("配置文件保存失败: " + e.getMessage(), e);
        }
    }

    /**
     * 合并两个配置
     * 第二个配置的非空值会覆盖第一个配置的值
     * 
     * @param baseConfig     基础配置
     * @param overrideConfig 覆盖配置
     * @return 合并后的配置
     */
    public GenerationConfig mergeConfigurations(GenerationConfig baseConfig, GenerationConfig overrideConfig) {
        return mergeConfigurationsEnhanced(baseConfig, overrideConfig);
    }

    /**
     * 创建默认配置
     * 
     * @return 默认配置对象
     */
    public GenerationConfig createDefaultConfiguration() {
        GenerationConfig defaultConfig = new GenerationConfig();

        // 设置默认值
        defaultConfig.setCount(10);

        // 设置默认输出配置
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv"); // 使用与OutputConfig默认值一致的格式
        outputConfig.setEncoding("UTF-8");
        defaultConfig.setOutputConfig(outputConfig);

        // 设置默认校验配置
        ValidationConfig validationConfig = new ValidationConfig();
        validationConfig.setEnabled(true);
        validationConfig.setStrictMode(false);
        validationConfig.setSkipInvalidData(false);
        defaultConfig.setValidationConfig(validationConfig);

        // 设置默认性能配置
        PerformanceConfig performanceConfig = new PerformanceConfig();
        performanceConfig.setThreadPoolSize(Runtime.getRuntime().availableProcessors());
        performanceConfig.setBatchSize(1000);
        performanceConfig.setEnableParallel(true);
        performanceConfig.setCacheEnabled(true);
        defaultConfig.setPerformanceConfig(performanceConfig);

        return defaultConfig;
    }

    /**
     * 从CLI参数创建配置
     * 
     * @param cliParameters CLI参数映射
     * @return CLI配置对象
     */
    public GenerationConfig createConfigurationFromCLI(Map<String, Object> cliParameters) {
        if (cliParameters == null || cliParameters.isEmpty()) {
            return new GenerationConfig();
        }

        GenerationConfig cliConfig = new GenerationConfig();

        // 基本配置
        if (cliParameters.containsKey("dataType")) {
            cliConfig.setDataType((String) cliParameters.get("dataType"));
        }

        if (cliParameters.containsKey("count")) {
            Object countValue = cliParameters.get("count");
            if (countValue instanceof Integer) {
                cliConfig.setCount((Integer) countValue);
            } else if (countValue instanceof String) {
                try {
                    cliConfig.setCount(Integer.parseInt((String) countValue));
                } catch (NumberFormatException e) {
                    // 忽略无效的数值
                }
            }
        }

        if (cliParameters.containsKey("seed")) {
            Object seedValue = cliParameters.get("seed");
            if (seedValue instanceof Long) {
                cliConfig.setSeed((Long) seedValue);
            } else if (seedValue instanceof String) {
                try {
                    cliConfig.setSeed(Long.parseLong((String) seedValue));
                } catch (NumberFormatException e) {
                    // 忽略无效的数值
                }
            }
        }

        // 参数配置
        Map<String, Object> parameters = new HashMap<>();
        for (Map.Entry<String, Object> entry : cliParameters.entrySet()) {
            String key = entry.getKey();
            // 提取以数据类型为前缀的参数
            if (key.contains(".") && !key.equals("output.format") && !key.equals("output.file")
                    && !key.equals("output.encoding") && !key.startsWith("validation.")
                    && !key.startsWith("performance.")) {
                parameters.put(key, entry.getValue());
            }
        }
        if (!parameters.isEmpty()) {
            cliConfig.setParameters(parameters);
        }

        // 输出配置
        OutputConfig outputConfig = new OutputConfig();
        boolean hasOutputConfig = false;

        if (cliParameters.containsKey("output.format")) {
            outputConfig.setFormat((String) cliParameters.get("output.format"));
            hasOutputConfig = true;
        }

        if (cliParameters.containsKey("output.file")) {
            outputConfig.setFile((String) cliParameters.get("output.file"));
            hasOutputConfig = true;
        }

        if (cliParameters.containsKey("output.encoding")) {
            outputConfig.setEncoding((String) cliParameters.get("output.encoding"));
            hasOutputConfig = true;
        }

        if (hasOutputConfig) {
            cliConfig.setOutputConfig(outputConfig);
        }

        // 校验配置
        ValidationConfig validationConfig = new ValidationConfig();
        boolean hasValidationConfig = false;

        if (cliParameters.containsKey("validation.enabled")) {
            Object value = cliParameters.get("validation.enabled");
            if (value instanceof Boolean) {
                validationConfig.setEnabled((Boolean) value);
                hasValidationConfig = true;
            } else if (value instanceof String) {
                validationConfig.setEnabled(Boolean.parseBoolean((String) value));
                hasValidationConfig = true;
            }
        }

        if (cliParameters.containsKey("validation.strict")) {
            Object value = cliParameters.get("validation.strict");
            if (value instanceof Boolean) {
                validationConfig.setStrictMode((Boolean) value);
                hasValidationConfig = true;
            } else if (value instanceof String) {
                validationConfig.setStrictMode(Boolean.parseBoolean((String) value));
                hasValidationConfig = true;
            }
        }

        if (hasValidationConfig) {
            cliConfig.setValidationConfig(validationConfig);
        }

        // 性能配置
        PerformanceConfig performanceConfig = new PerformanceConfig();
        boolean hasPerformanceConfig = false;

        if (cliParameters.containsKey("performance.threads")) {
            Object value = cliParameters.get("performance.threads");
            if (value instanceof Integer) {
                performanceConfig.setThreadPoolSize((Integer) value);
                hasPerformanceConfig = true;
            } else if (value instanceof String) {
                try {
                    performanceConfig.setThreadPoolSize(Integer.parseInt((String) value));
                    hasPerformanceConfig = true;
                } catch (NumberFormatException e) {
                    // 忽略无效的数值
                }
            }
        }

        if (cliParameters.containsKey("performance.batch")) {
            Object value = cliParameters.get("performance.batch");
            if (value instanceof Integer) {
                performanceConfig.setBatchSize((Integer) value);
                hasPerformanceConfig = true;
            } else if (value instanceof String) {
                try {
                    performanceConfig.setBatchSize(Integer.parseInt((String) value));
                    hasPerformanceConfig = true;
                } catch (NumberFormatException e) {
                    // 忽略无效的数值
                }
            }
        }

        if (cliParameters.containsKey("performance.parallel")) {
            Object value = cliParameters.get("performance.parallel");
            if (value instanceof Boolean) {
                performanceConfig.setEnableParallel((Boolean) value);
                hasPerformanceConfig = true;
            } else if (value instanceof String) {
                performanceConfig.setEnableParallel(Boolean.parseBoolean((String) value));
                hasPerformanceConfig = true;
            }
        }

        if (hasPerformanceConfig) {
            cliConfig.setPerformanceConfig(performanceConfig);
        }

        return cliConfig;
    }

    /**
     * 应用配置优先级管理
     * 按照优先级顺序合并配置：CLI参数 > 配置文件 > 默认配置
     * 
     * @param cliParameters  CLI参数映射
     * @param configFilePath 配置文件路径（可选）
     * @return 合并后的最终配置
     * @throws IOException 当配置文件读取失败时抛出
     */
    public GenerationConfig applyConfigurationPriority(Map<String, Object> cliParameters, String configFilePath)
            throws IOException {

        // 1. 创建默认配置（最低优先级）
        GenerationConfig finalConfig = createDefaultConfiguration();

        // 2. 如果有配置文件，加载并合并（中等优先级）
        if (configFilePath != null && !configFilePath.trim().isEmpty()) {
            try {
                GenerationConfig fileConfig = loadFromFile(configFilePath);
                finalConfig = mergeConfigurations(finalConfig, fileConfig);
            } catch (IOException e) {
                // 配置文件加载失败，记录警告但继续使用默认配置
                throw new IOException("配置文件加载失败: " + e.getMessage(), e);
            }
        }

        // 3. 应用CLI参数（最高优先级）
        if (cliParameters != null && !cliParameters.isEmpty()) {
            GenerationConfig cliConfig = createConfigurationFromCLI(cliParameters);
            finalConfig = mergeConfigurations(finalConfig, cliConfig);
        }

        return finalConfig;
    }

    /**
     * 应用配置优先级管理（忽略配置文件错误）
     * 
     * @param cliParameters  CLI参数映射
     * @param configFilePath 配置文件路径（可选）
     * @return 合并后的最终配置
     */
    public GenerationConfig applyConfigurationPriorityIgnoreErrors(Map<String, Object> cliParameters,
            String configFilePath) {
        try {
            return applyConfigurationPriority(cliParameters, configFilePath);
        } catch (IOException e) {
            // 忽略配置文件错误，使用默认配置和CLI参数
            GenerationConfig finalConfig = createDefaultConfiguration();

            if (cliParameters != null && !cliParameters.isEmpty()) {
                GenerationConfig cliConfig = createConfigurationFromCLI(cliParameters);
                finalConfig = mergeConfigurations(finalConfig, cliConfig);
            }

            return finalConfig;
        }
    }

    /**
     * 增强的配置合并方法
     * 支持更细粒度的配置合并，包括嵌套对象的部分合并
     * 
     * @param baseConfig     基础配置
     * @param overrideConfig 覆盖配置
     * @return 合并后的配置
     */
    public GenerationConfig mergeConfigurationsEnhanced(GenerationConfig baseConfig, GenerationConfig overrideConfig) {
        if (baseConfig == null) {
            return overrideConfig != null ? overrideConfig.copy() : new GenerationConfig();
        }

        if (overrideConfig == null) {
            return baseConfig.copy();
        }

        GenerationConfig mergedConfig = baseConfig.copy();

        // 合并基本属性
        if (overrideConfig.getDataType() != null) {
            mergedConfig.setDataType(overrideConfig.getDataType());
        }

        if (overrideConfig.getCount() > 0) {
            mergedConfig.setCount(overrideConfig.getCount());
        }

        if (overrideConfig.getSeed() != null) {
            mergedConfig.setSeed(overrideConfig.getSeed());
        }

        if (overrideConfig.getRequestId() != null) {
            mergedConfig.setRequestId(overrideConfig.getRequestId());
        }

        // 合并参数（增量合并）
        if (overrideConfig.getParameters() != null && !overrideConfig.getParameters().isEmpty()) {
            mergedConfig.getParameters().putAll(overrideConfig.getParameters());
        }

        // 合并字段配置
        if (overrideConfig.getFields() != null && !overrideConfig.getFields().isEmpty()) {
            mergedConfig.setFields(overrideConfig.getFields());
        }

        // 合并输出配置（部分合并）
        if (overrideConfig.getOutputConfig() != null) {
            OutputConfig mergedOutput = mergedConfig.getOutputConfig();
            OutputConfig overrideOutput = overrideConfig.getOutputConfig();

            if (overrideOutput.getFormat() != null) {
                mergedOutput.setFormat(overrideOutput.getFormat());
            }
            if (overrideOutput.getFile() != null) {
                mergedOutput.setFile(overrideOutput.getFile());
            }
            if (overrideOutput.getEncoding() != null) {
                mergedOutput.setEncoding(overrideOutput.getEncoding());
            }
        }

        // 合并校验配置（部分合并）
        if (overrideConfig.getValidationConfig() != null) {
            ValidationConfig mergedValidation = mergedConfig.getValidationConfig();
            ValidationConfig overrideValidation = overrideConfig.getValidationConfig();

            // 只有当覆盖配置明确设置了值时才合并
            if (overrideValidation.isEnabled() != mergedValidation.isEnabled()) {
                mergedValidation.setEnabled(overrideValidation.isEnabled());
            }
            if (overrideValidation.isStrictMode() != mergedValidation.isStrictMode()) {
                mergedValidation.setStrictMode(overrideValidation.isStrictMode());
            }
            if (overrideValidation.shouldSkipInvalidData() != mergedValidation.shouldSkipInvalidData()) {
                mergedValidation.setSkipInvalidData(overrideValidation.shouldSkipInvalidData());
            }
        }

        // 合并性能配置（部分合并）
        if (overrideConfig.getPerformanceConfig() != null) {
            PerformanceConfig mergedPerformance = mergedConfig.getPerformanceConfig();
            PerformanceConfig overridePerformance = overrideConfig.getPerformanceConfig();

            if (overridePerformance.getThreadPoolSize() != mergedPerformance.getThreadPoolSize()) {
                mergedPerformance.setThreadPoolSize(overridePerformance.getThreadPoolSize());
            }
            if (overridePerformance.getBatchSize() != mergedPerformance.getBatchSize()) {
                mergedPerformance.setBatchSize(overridePerformance.getBatchSize());
            }
            if (overridePerformance.isEnableParallel() != mergedPerformance.isEnableParallel()) {
                mergedPerformance.setEnableParallel(overridePerformance.isEnableParallel());
            }
            if (overridePerformance.isCacheEnabled() != mergedPerformance.isCacheEnabled()) {
                mergedPerformance.setCacheEnabled(overridePerformance.isCacheEnabled());
            }
        }

        return mergedConfig;
    }

    /**
     * 根据文件扩展名选择合适的ObjectMapper
     * 
     * @param filePath 文件路径
     * @return 对应的ObjectMapper
     */
    private ObjectMapper selectMapper(String filePath) {
        String lowerPath = filePath.toLowerCase();
        if (lowerPath.endsWith(".yaml") || lowerPath.endsWith(".yml")) {
            return yamlMapper;
        } else if (lowerPath.endsWith(".json")) {
            return jsonMapper;
        } else {
            // 默认使用YAML
            return yamlMapper;
        }
    }
}