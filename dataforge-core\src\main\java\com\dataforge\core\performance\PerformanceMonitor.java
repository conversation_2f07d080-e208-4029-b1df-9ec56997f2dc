package com.dataforge.core.performance;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 性能监控器
 * 
 * 监控数据生成的性能指标，包括吞吐量、延迟、错误率等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class PerformanceMonitor {

    private static final Logger logger = LoggerFactory.getLogger(PerformanceMonitor.class);

    private final ConcurrentHashMap<String, GenerationMetrics> metricsMap = new ConcurrentHashMap<>();
    private final AtomicLong totalGeneratedCount = new AtomicLong(0);
    private final AtomicLong totalErrorCount = new AtomicLong(0);
    private final long startTime = System.currentTimeMillis();

    /**
     * 记录生成开始
     * 
     * @param generatorType 生成器类型
     * @param count         生成数量
     * @return 操作ID，用于后续记录完成时间
     */
    public String recordGenerationStart(String generatorType, int count) {
        String operationId = generateOperationId();
        GenerationMetrics metrics = metricsMap.computeIfAbsent(generatorType, k -> new GenerationMetrics());
        metrics.recordStart(operationId, count);
        return operationId;
    }

    /**
     * 记录生成完成
     * 
     * @param generatorType 生成器类型
     * @param operationId   操作ID
     * @param actualCount   实际生成数量
     * @param success       是否成功
     */
    public void recordGenerationEnd(String generatorType, String operationId, int actualCount, boolean success) {
        GenerationMetrics metrics = metricsMap.get(generatorType);
        if (metrics != null) {
            metrics.recordEnd(operationId, actualCount, success);

            if (success) {
                totalGeneratedCount.addAndGet(actualCount);
            } else {
                totalErrorCount.incrementAndGet();
            }
        }
    }

    /**
     * 获取生成器的性能指标
     * 
     * @param generatorType 生成器类型
     * @return 性能指标，如果不存在返回null
     */
    public GenerationStats getStats(String generatorType) {
        GenerationMetrics metrics = metricsMap.get(generatorType);
        return metrics != null ? metrics.getStats() : null;
    }

    /**
     * 获取总体性能指标
     * 
     * @return 总体性能指标
     */
    public OverallStats getOverallStats() {
        long currentTime = System.currentTimeMillis();
        long totalTime = currentTime - startTime;
        long totalGenerated = totalGeneratedCount.get();
        long totalErrors = totalErrorCount.get();

        double throughput = totalTime > 0 ? (totalGenerated * 1000.0) / totalTime : 0;
        double errorRate = totalGenerated > 0 ? (totalErrors * 100.0) / totalGenerated : 0;

        return new OverallStats(
                totalGenerated,
                totalErrors,
                throughput,
                errorRate,
                totalTime,
                metricsMap.size());
    }

    /**
     * 重置所有统计信息
     */
    public void reset() {
        metricsMap.clear();
        totalGeneratedCount.set(0);
        totalErrorCount.set(0);
        logger.info("Performance monitor reset");
    }

    /**
     * 生成操作ID
     */
    private String generateOperationId() {
        return "op_" + System.nanoTime();
    }

    /**
     * 单个生成器的性能指标
     */
    private static class GenerationMetrics {
        private final ConcurrentHashMap<String, OperationRecord> activeOperations = new ConcurrentHashMap<>();
        private final LongAdder totalCount = new LongAdder();
        private final LongAdder successCount = new LongAdder();
        private final LongAdder errorCount = new LongAdder();
        private final LongAdder totalDuration = new LongAdder();
        private volatile long minDuration = Long.MAX_VALUE;
        private volatile long maxDuration = Long.MIN_VALUE;

        void recordStart(String operationId, int count) {
            activeOperations.put(operationId, new OperationRecord(count, System.currentTimeMillis()));
        }

        void recordEnd(String operationId, int actualCount, boolean success) {
            OperationRecord record = activeOperations.remove(operationId);
            if (record != null) {
                long duration = System.currentTimeMillis() - record.startTime;

                totalCount.add(actualCount);
                totalDuration.add(duration);

                if (success) {
                    successCount.add(actualCount);
                } else {
                    errorCount.increment();
                }

                // 更新最小最大持续时间
                updateMinMax(duration);
            }
        }

        private synchronized void updateMinMax(long duration) {
            if (duration < minDuration) {
                minDuration = duration;
            }
            if (duration > maxDuration) {
                maxDuration = duration;
            }
        }

        GenerationStats getStats() {
            long total = totalCount.sum();
            long success = successCount.sum();
            long errors = errorCount.sum();
            long duration = totalDuration.sum();

            double avgDuration = total > 0 ? (double) duration / total : 0;
            double throughput = duration > 0 ? (total * 1000.0) / duration : 0;
            double errorRate = total > 0 ? (errors * 100.0) / total : 0;

            return new GenerationStats(
                    total,
                    success,
                    errors,
                    throughput,
                    errorRate,
                    avgDuration,
                    minDuration == Long.MAX_VALUE ? 0 : minDuration,
                    maxDuration == Long.MIN_VALUE ? 0 : maxDuration);
        }
    }

    /**
     * 操作记录
     */
    private static class OperationRecord {
        final int count;
        final long startTime;

        OperationRecord(int count, long startTime) {
            this.count = count;
            this.startTime = startTime;
        }
    }

    /**
     * 生成统计信息
     */
    public static class GenerationStats {
        private final long totalCount;
        private final long successCount;
        private final long errorCount;
        private final double throughput;
        private final double errorRate;
        private final double avgDuration;
        private final long minDuration;
        private final long maxDuration;

        public GenerationStats(long totalCount, long successCount, long errorCount,
                double throughput, double errorRate, double avgDuration,
                long minDuration, long maxDuration) {
            this.totalCount = totalCount;
            this.successCount = successCount;
            this.errorCount = errorCount;
            this.throughput = throughput;
            this.errorRate = errorRate;
            this.avgDuration = avgDuration;
            this.minDuration = minDuration;
            this.maxDuration = maxDuration;
        }

        // Getters
        public long getTotalCount() {
            return totalCount;
        }

        public long getSuccessCount() {
            return successCount;
        }

        public long getErrorCount() {
            return errorCount;
        }

        public double getThroughput() {
            return throughput;
        }

        public double getErrorRate() {
            return errorRate;
        }

        public double getAvgDuration() {
            return avgDuration;
        }

        public long getMinDuration() {
            return minDuration;
        }

        public long getMaxDuration() {
            return maxDuration;
        }

        @Override
        public String toString() {
            return String.format(
                    "GenerationStats{total=%d, success=%d, errors=%d, throughput=%.2f/s, errorRate=%.2f%%, avgDuration=%.2fms}",
                    totalCount, successCount, errorCount, throughput, errorRate, avgDuration);
        }
    }

    /**
     * 总体统计信息
     */
    public static class OverallStats {
        private final long totalGenerated;
        private final long totalErrors;
        private final double overallThroughput;
        private final double overallErrorRate;
        private final long totalTime;
        private final int generatorCount;

        public OverallStats(long totalGenerated, long totalErrors, double overallThroughput,
                double overallErrorRate, long totalTime, int generatorCount) {
            this.totalGenerated = totalGenerated;
            this.totalErrors = totalErrors;
            this.overallThroughput = overallThroughput;
            this.overallErrorRate = overallErrorRate;
            this.totalTime = totalTime;
            this.generatorCount = generatorCount;
        }

        // Getters
        public long getTotalGenerated() {
            return totalGenerated;
        }

        public long getTotalErrors() {
            return totalErrors;
        }

        public double getOverallThroughput() {
            return overallThroughput;
        }

        public double getOverallErrorRate() {
            return overallErrorRate;
        }

        public long getTotalTime() {
            return totalTime;
        }

        public int getGeneratorCount() {
            return generatorCount;
        }

        @Override
        public String toString() {
            return String.format(
                    "OverallStats{generated=%d, errors=%d, throughput=%.2f/s, errorRate=%.2f%%, time=%dms, generators=%d}",
                    totalGenerated, totalErrors, overallThroughput, overallErrorRate, totalTime, generatorCount);
        }
    }
}