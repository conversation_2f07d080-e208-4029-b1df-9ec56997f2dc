package com.dataforge.generators.text;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 手机号码生成器
 * 
 * 生成符合中国大陆手机号码规则的号码，支持指定运营商和号段。
 * 确保生成的号码格式正确且符合实际使用规范。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class PhoneGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "phone";
    private static final String DESCRIPTION = "生成中国大陆手机号码";

    // 手机号码验证正则表达式
    private static final Pattern PHONE_PATTERN = Pattern.compile("^1[3-9]\\d{9}$");

    // 常见号段前缀
    private static final List<String> COMMON_PREFIXES = Arrays.asList(
            "130", "131", "132", "133", "134", "135", "136", "137", "138", "139",
            "150", "151", "152", "153", "155", "156", "157", "158", "159",
            "180", "181", "182", "183", "184", "185", "186", "187", "188", "189");

    private final Random random;

    /**
     * 构造函数
     */
    public PhoneGenerator() {
        this.random = new Random();
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("format", String.class, "plain",
                "输出格式，可选值：plain（纯数字）、dash（带短横线）", false));
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        String format = context.getParameter("format", "plain");

        // 选择号段前缀
        String prefix = COMMON_PREFIXES.get(random.nextInt(COMMON_PREFIXES.size()));

        // 生成后8位数字
        StringBuilder phone = new StringBuilder(prefix);
        for (int i = 0; i < 8; i++) {
            phone.append(random.nextInt(10));
        }

        // 格式化输出
        String phoneNumber = phone.toString();
        if ("dash".equals(format)) {
            return phoneNumber.substring(0, 3) + "-" + phoneNumber.substring(3, 7) + "-" + phoneNumber.substring(7);
        }

        return phoneNumber;
    }

    @Override
    public ValidationResult validateWithDetails(String data) {
        if (data == null) {
            return ValidationResult.error("手机号码不能为空");
        }

        // 清理格式字符
        String cleanPhone = data.replaceAll("[\\s\\-()]", "");

        if (cleanPhone.isEmpty()) {
            return ValidationResult.error("手机号码不能为空");
        }

        if (!PHONE_PATTERN.matcher(cleanPhone).matches()) {
            return ValidationResult.error("手机号码格式不正确");
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }
}