/**
 * 缓存管理包
 * 
 * 提供高性能的内存缓存功能，用于缓存生成器使用的各种数据源。
 * 基于Caffeine缓存库实现，支持过期策略、大小限制和统计监控。
 * 
 * <h2>主要组件：</h2>
 * <ul>
 * <li>{@link com.dataforge.core.cache.CacheManager} - 缓存管理器</li>
 * <li>{@link com.dataforge.core.cache.CacheConfig} - 缓存配置</li>
 * <li>{@link com.dataforge.core.cache.DataSourceCacheService} - 数据源缓存服务</li>
 * </ul>
 * 
 * <h2>缓存策略：</h2>
 * <ul>
 * <li>基于大小的淘汰策略</li>
 * <li>基于时间的过期策略（写入后过期、访问后过期）</li>
 * <li>自动刷新机制</li>
 * <li>统计信息收集</li>
 * </ul>
 * 
 * <h2>使用示例：</h2>
 * 
 * <pre>{@code
 * // 创建缓存管理器
 * CacheConfig config = CacheConfig.defaultConfig()
 *         .setMaximumSize(1000)
 *         .setExpireAfterWrite(Duration.ofHours(1));
 * 
 * CacheManager cacheManager = new CacheManager(config);
 * 
 * // 创建数据源缓存服务
 * DataSourceCacheService cacheService = new DataSourceCacheService(cacheManager);
 * 
 * // 预热缓存
 * cacheService.warmUpAll();
 * 
 * // 获取缓存数据
 * List<String> surnames = cacheService.getChineseSurnames();
 * List<String> areaCodes = cacheService.getAreaCodes();
 * 
 * // 获取缓存统计
 * Map<String, Object> stats = cacheService.getCacheStatistics();
 * }</pre>
 * 
 * <h2>性能特性：</h2>
 * <ul>
 * <li>高并发读写支持</li>
 * <li>内存使用优化</li>
 * <li>懒加载和预热机制</li>
 * <li>自动过期和清理</li>
 * <li>详细的统计信息</li>
 * </ul>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
package com.dataforge.core.cache;