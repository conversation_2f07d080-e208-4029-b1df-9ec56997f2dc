package com.dataforge.generators.security;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * XSS攻击脚本生成器单元测试
 */
@DisplayName("XSS攻击脚本生成器测试")
class XssAttackScriptGeneratorTest {

    private XssAttackScriptGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new XssAttackScriptGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext(parameters, 12345L);
    }

    @Test
    @DisplayName("基本XSS脚本生成测试")
    void testBasicGeneration() {
        String xssScript = generator.generate(context);

        assertThat(xssScript).isNotNull();
        assertThat(xssScript).isNotEmpty();
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证多样性")
    void testRepeatedGeneration() {
        String xssScript = generator.generate(context);

        assertThat(xssScript).isNotNull();
        assertThat(xssScript).isNotEmpty();
        assertThat(generator.validate(xssScript)).isTrue();
    }

    @Test
    @DisplayName("指定XSS类型生成测试")
    void testGenerationWithXssType() {
        String[] xssTypes = { "REFLECTED", "STORED", "DOM_BASED" };

        for (String type : xssTypes) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("xssType", type);
            GenerationContext typeContext = new GenerationContext(parameters, 12345L);

            String xssScript = generator.generate(typeContext);
            assertThat(xssScript).isNotNull();
            assertThat(generator.validate(xssScript)).isTrue();
        }
    }

    @Test
    @DisplayName("反射型XSS脚本测试")
    void testReflectedXssScript() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("xssType", "REFLECTED");
        GenerationContext reflectedContext = new GenerationContext(parameters, 12345L);

        String xssScript = generator.generate(reflectedContext);

        assertThat(xssScript).isNotNull();
        assertThat(xssScript).containsAnyOf("<script>", "javascript:", "onerror=");
    }

    @Test
    @DisplayName("存储型XSS脚本测试")
    void testStoredXssScript() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("xssType", "STORED");
        GenerationContext storedContext = new GenerationContext(parameters, 12345L);

        String xssScript = generator.generate(storedContext);

        assertThat(xssScript).isNotNull();
        assertThat(xssScript).containsAnyOf("<script>", "<img", "<iframe");
    }

    @Test
    @DisplayName("DOM型XSS脚本测试")
    void testDomBasedXssScript() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("xssType", "DOM_BASED");
        GenerationContext domContext = new GenerationContext(parameters, 12345L);

        String xssScript = generator.generate(domContext);

        assertThat(xssScript).isNotNull();
        assertThat(xssScript).containsAnyOf("document.", "window.", "location.");
    }

    @Test
    @DisplayName("编码绕过测试")
    void testEncodingBypass() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("encoding", "HTML_ENTITY");
        GenerationContext htmlContext = new GenerationContext(parameters, 12345L);

        String xssScript = generator.generate(htmlContext);

        assertThat(xssScript).isNotNull();
        // HTML实体编码的脚本应该包含&符号
        if (xssScript.contains("&")) {
            assertThat(xssScript).matches(".*&#?[a-zA-Z0-9]+;.*");
        }

        // 测试URL编码
        parameters.put("encoding", "URL");
        GenerationContext urlContext = new GenerationContext(parameters, 12345L);

        xssScript = generator.generate(urlContext);
        assertThat(xssScript).isNotNull();
    }

    @Test
    @DisplayName("XSS脚本校验测试 - 有效脚本")
    void testValidationWithValidXssScript() {
        String[] validScripts = {
                "<script>alert('XSS')</script>",
                "<img src=x onerror=alert('XSS')>",
                "javascript:alert('XSS')",
                "<iframe src=javascript:alert('XSS')></iframe>"
        };

        for (String script : validScripts) {
            assertThat(generator.validate(script)).isTrue();
        }
    }

    @Test
    @DisplayName("XSS脚本校验测试 - 无效脚本")
    void testValidationWithInvalidXssScript() {
        String[] invalidScripts = {
                "",
                "   ",
                "normal text without xss",
                "hello world"
        };

        for (String script : invalidScripts) {
            assertThat(generator.validate(script)).isFalse();
        }

        // 测试null值
        assertThat(generator.validate(null)).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("xss_attack");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        List<GeneratorParameter> parameters = generator.getSupportedParameters();

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("xssType", "encoding", "payload");
    }

    @Test
    @DisplayName("不同payload类型测试")
    void testDifferentPayloadTypes() {
        String[] payloadTypes = { "ALERT", "COOKIE_STEAL", "REDIRECT", "KEYLOGGER" };

        for (String payloadType : payloadTypes) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("payload", payloadType);
            GenerationContext payloadContext = new GenerationContext(parameters, 12345L);

            String xssScript = generator.generate(payloadContext);
            assertThat(xssScript).isNotNull();
            assertThat(generator.validate(xssScript)).isTrue();
        }
    }

    @Test
    @DisplayName("Cookie窃取脚本测试")
    void testCookieStealPayload() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("payload", "COOKIE_STEAL");
        GenerationContext cookieContext = new GenerationContext(parameters, 12345L);

        String xssScript = generator.generate(cookieContext);

        assertThat(xssScript).isNotNull();
        assertThat(xssScript.toLowerCase()).containsAnyOf("document.cookie", "cookie");
    }

    @Test
    @DisplayName("重定向脚本测试")
    void testRedirectPayload() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("payload", "REDIRECT");
        GenerationContext redirectContext = new GenerationContext(parameters, 12345L);

        String xssScript = generator.generate(redirectContext);

        assertThat(xssScript).isNotNull();
        assertThat(xssScript.toLowerCase()).containsAnyOf("location", "window.open", "href");
    }

    @Test
    @DisplayName("WAF绕过脚本测试")
    void testWafBypassScript() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("bypassWAF", true);
        GenerationContext wafContext = new GenerationContext(parameters, 12345L);

        String xssScript = generator.generate(wafContext);

        assertThat(xssScript).isNotNull();
        assertThat(generator.validate(xssScript)).isTrue();
    }

    @Test
    @DisplayName("脚本长度限制测试")
    void testScriptLengthLimit() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("maxLength", 50);
        GenerationContext lengthContext = new GenerationContext(parameters, 12345L);

        String xssScript = generator.generate(lengthContext);

        assertThat(xssScript).isNotNull();
        assertThat(xssScript.length()).isLessThanOrEqualTo(50);
    }

    @Test
    @DisplayName("特殊字符处理测试")
    void testSpecialCharacterHandling() {
        String xssScript = generator.generate(context);

        assertThat(xssScript).isNotNull();
        // XSS脚本通常包含特殊字符
        assertThat(xssScript).containsAnyOf("<", ">", "'", "\"", "(", ")");
    }

    @Test
    @DisplayName("脚本多样性测试")
    void testScriptDiversity() {
        java.util.Set<String> scripts = new java.util.HashSet<>();

        for (int i = 0; i < 50; i++) {
            String xssScript = generator.generate(context);
            scripts.add(xssScript);
        }

        // 应该生成多种不同的脚本
        assertThat(scripts.size()).isGreaterThan(5);
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("xssType", "INVALID_TYPE");
        parameters.put("payload", "INVALID_PAYLOAD");
        GenerationContext invalidContext = new GenerationContext(parameters, 12345L);

        // 应该能够处理无效参数并生成有效的XSS脚本
        String xssScript = generator.generate(invalidContext);
        assertThat(xssScript).isNotNull();
        assertThat(generator.validate(xssScript)).isTrue();
    }

    @Test
    @DisplayName("性能测试")
    void testPerformance() {
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 1000; i++) {
            String xssScript = generator.generate(context);
            assertThat(xssScript).isNotNull();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertThat(duration).isLessThan(2000); // 应该在2秒内完成
    }

    @Test
    @DisplayName("JavaScript关键字包含测试")
    void testJavaScriptKeywordInclusion() {
        String[] jsKeywords = { "alert", "document", "window", "eval", "function", "var", "let", "const" };
        boolean foundKeyword = false;

        for (int i = 0; i < 20; i++) {
            String xssScript = generator.generate(context).toLowerCase();
            for (String keyword : jsKeywords) {
                if (xssScript.contains(keyword)) {
                    foundKeyword = true;
                    break;
                }
            }
            if (foundKeyword)
                break;
        }

        assertThat(foundKeyword).isTrue();
    }

    @Test
    @DisplayName("HTML标签测试")
    void testHtmlTags() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("useHtmlTags", true);
        GenerationContext htmlContext = new GenerationContext(parameters, 12345L);

        boolean foundHtmlTag = false;
        String[] htmlTags = { "<script>", "<img", "<iframe", "<object", "<embed", "<svg" };

        for (int i = 0; i < 20; i++) {
            String xssScript = generator.generate(htmlContext);
            for (String tag : htmlTags) {
                if (xssScript.contains(tag)) {
                    foundHtmlTag = true;
                    break;
                }
            }
            if (foundHtmlTag)
                break;
        }

        assertThat(foundHtmlTag).isTrue();
    }

    @Test
    @DisplayName("事件处理器测试")
    void testEventHandlers() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("useEventHandlers", true);
        GenerationContext eventContext = new GenerationContext(parameters, 12345L);

        boolean foundEventHandler = false;
        String[] eventHandlers = { "onerror=", "onload=", "onclick=", "onmouseover=", "onfocus=" };

        for (int i = 0; i < 20; i++) {
            String xssScript = generator.generate(eventContext);
            for (String handler : eventHandlers) {
                if (xssScript.contains(handler)) {
                    foundEventHandler = true;
                    break;
                }
            }
            if (foundEventHandler)
                break;
        }

        assertThat(foundEventHandler).isTrue();
    }
}