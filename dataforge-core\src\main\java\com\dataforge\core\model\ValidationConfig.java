package com.dataforge.core.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.Objects;

/**
 * 校验配置
 * 
 * 定义数据校验的相关配置，包括是否启用校验、严格模式、校验规则等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ValidationConfig {

    @JsonProperty("enabled")
    private boolean enabled = true;

    @JsonProperty("strictMode")
    private boolean strictMode = false;

    @JsonProperty("failOnValidationError")
    private boolean failOnValidationError = false;

    @JsonProperty("maxErrorCount")
    private int maxErrorCount = 100;

    @JsonProperty("reportInvalidData")
    private boolean reportInvalidData = true;

    @JsonProperty("skipInvalidData")
    private boolean skipInvalidData = false;

    /**
     * 默认构造函数
     */
    public ValidationConfig() {
    }

    /**
     * 构造函数
     * 
     * @param enabled 是否启用校验
     */
    public ValidationConfig(boolean enabled) {
        this.enabled = enabled;
    }

    /**
     * 构造函数
     * 
     * @param enabled    是否启用校验
     * @param strictMode 是否启用严格模式
     */
    public ValidationConfig(boolean enabled, boolean strictMode) {
        this.enabled = enabled;
        this.strictMode = strictMode;
    }

    // Getters and Setters

    public boolean isEnabled() {
        return enabled;
    }

    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }

    public boolean isStrictMode() {
        return strictMode;
    }

    public void setStrictMode(boolean strictMode) {
        this.strictMode = strictMode;
    }

    public boolean isFailOnValidationError() {
        return failOnValidationError;
    }

    public void setFailOnValidationError(boolean failOnValidationError) {
        this.failOnValidationError = failOnValidationError;
    }

    public int getMaxErrorCount() {
        return maxErrorCount;
    }

    public void setMaxErrorCount(int maxErrorCount) {
        this.maxErrorCount = Math.max(1, maxErrorCount);
    }

    public boolean isReportInvalidData() {
        return reportInvalidData;
    }

    public void setReportInvalidData(boolean reportInvalidData) {
        this.reportInvalidData = reportInvalidData;
    }

    public boolean isSkipInvalidData() {
        return skipInvalidData;
    }

    public void setSkipInvalidData(boolean skipInvalidData) {
        this.skipInvalidData = skipInvalidData;
    }

    /**
     * 设置并行验证
     * 
     * @param parallelValidation 是否启用并行验证
     */
    public void setParallelValidation(boolean parallelValidation) {
        // 这个方法是为了兼容性，实际通过options存储
        // 可以在这里添加并行验证的逻辑
    }

    /**
     * 设置区域设置
     * 
     * @param locale 区域设置
     */
    public void setLocale(String locale) {
        // 这个方法是为了兼容性，实际通过options存储
        // 可以在这里添加区域设置的逻辑
    }

    /**
     * 检查是否应该在校验错误时停止生成
     * 
     * @return 如果应该停止返回true，否则返回false
     */
    public boolean shouldStopOnError() {
        return enabled && failOnValidationError;
    }

    /**
     * 检查是否应该跳过无效数据
     * 
     * @return 如果应该跳过返回true，否则返回false
     */
    public boolean shouldSkipInvalidData() {
        return enabled && skipInvalidData;
    }

    /**
     * 检查是否应该报告无效数据
     * 
     * @return 如果应该报告返回true，否则返回false
     */
    public boolean shouldReportInvalidData() {
        return enabled && reportInvalidData;
    }

    /**
     * 验证校验配置的有效性
     * 
     * @return 验证结果
     */
    public ValidationResult validate() {
        ValidationResult result = new ValidationResult();

        if (maxErrorCount <= 0) {
            result.addError("最大错误数量必须大于0");
        }

        // 逻辑一致性检查
        if (!enabled && (failOnValidationError || skipInvalidData || reportInvalidData)) {
            result.addWarning("校验已禁用，但相关选项仍然启用");
        }

        return result;
    }

    /**
     * 创建校验配置的副本
     * 
     * @return 校验配置副本
     */
    public ValidationConfig copy() {
        ValidationConfig copy = new ValidationConfig();
        copy.enabled = this.enabled;
        copy.strictMode = this.strictMode;
        copy.failOnValidationError = this.failOnValidationError;
        copy.maxErrorCount = this.maxErrorCount;
        copy.reportInvalidData = this.reportInvalidData;
        copy.skipInvalidData = this.skipInvalidData;
        return copy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        ValidationConfig that = (ValidationConfig) o;
        return enabled == that.enabled &&
                strictMode == that.strictMode &&
                failOnValidationError == that.failOnValidationError &&
                maxErrorCount == that.maxErrorCount &&
                reportInvalidData == that.reportInvalidData &&
                skipInvalidData == that.skipInvalidData;
    }

    @Override
    public int hashCode() {
        return Objects.hash(enabled, strictMode, failOnValidationError, maxErrorCount, reportInvalidData,
                skipInvalidData);
    }

    @Override
    public String toString() {
        return String.format("ValidationConfig{enabled=%s, strictMode=%s, failOnError=%s, maxErrors=%d}",
                enabled, strictMode, failOnValidationError, maxErrorCount);
    }
}