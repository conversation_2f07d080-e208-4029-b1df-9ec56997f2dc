package com.dataforge.core.generator;

import java.util.Objects;

/**
 * 生成器参数定义
 * 
 * 描述数据生成器支持的参数信息，包括参数名称、类型、默认值、描述等。
 * 用于CLI帮助信息生成和参数验证。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class GeneratorParameter {

    private final String name;
    private final Class<?> type;
    private final Object defaultValue;
    private final String description;
    private final boolean required;
    private final String[] allowedValues;

    /**
     * 构造函数
     * 
     * @param name          参数名称
     * @param type          参数类型
     * @param defaultValue  默认值
     * @param description   参数描述
     * @param required      是否必需
     * @param allowedValues 允许的值列表（可选）
     */
    public GeneratorParameter(String name, Class<?> type, Object defaultValue,
            String description, boolean required, String... allowedValues) {
        this.name = Objects.requireNonNull(name, "参数名称不能为空");
        this.type = Objects.requireNonNull(type, "参数类型不能为空");
        this.defaultValue = defaultValue;
        this.description = description != null ? description : "";
        this.required = required;
        this.allowedValues = allowedValues != null ? allowedValues : new String[0];
    }

    /**
     * 创建必需参数
     * 
     * @param name        参数名称
     * @param type        参数类型
     * @param description 参数描述
     * @return 参数对象
     */
    public static GeneratorParameter required(String name, Class<?> type, String description) {
        return new GeneratorParameter(name, type, null, description, true);
    }

    /**
     * 创建可选参数
     * 
     * @param name         参数名称
     * @param type         参数类型
     * @param defaultValue 默认值
     * @param description  参数描述
     * @return 参数对象
     */
    public static GeneratorParameter optional(String name, Class<?> type, Object defaultValue, String description) {
        return new GeneratorParameter(name, type, defaultValue, description, false);
    }

    /**
     * 创建枚举参数
     * 
     * @param name          参数名称
     * @param defaultValue  默认值
     * @param description   参数描述
     * @param allowedValues 允许的值列表
     * @return 参数对象
     */
    public static GeneratorParameter enumeration(String name, String defaultValue, String description,
            String... allowedValues) {
        return new GeneratorParameter(name, String.class, defaultValue, description, false, allowedValues);
    }

    // Getters
    public String getName() {
        return name;
    }

    public Class<?> getType() {
        return type;
    }

    public Object getDefaultValue() {
        return defaultValue;
    }

    public String getDescription() {
        return description;
    }

    public boolean isRequired() {
        return required;
    }

    public String[] getAllowedValues() {
        return allowedValues.clone();
    }

    public boolean hasAllowedValues() {
        return allowedValues.length > 0;
    }

    /**
     * 验证参数值是否有效
     * 
     * @param value 参数值
     * @return 如果有效返回true，否则返回false
     */
    public boolean isValidValue(Object value) {
        if (value == null) {
            return !required;
        }

        if (!type.isAssignableFrom(value.getClass())) {
            return false;
        }

        if (hasAllowedValues()) {
            String stringValue = value.toString();
            for (String allowedValue : allowedValues) {
                if (allowedValue.equals(stringValue)) {
                    return true;
                }
            }
            return false;
        }

        return true;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        GeneratorParameter that = (GeneratorParameter) o;
        return Objects.equals(name, that.name);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name);
    }

    @Override
    public String toString() {
        return String.format("GeneratorParameter{name='%s', type=%s, required=%s, defaultValue=%s}",
                name, type.getSimpleName(), required, defaultValue);
    }
}