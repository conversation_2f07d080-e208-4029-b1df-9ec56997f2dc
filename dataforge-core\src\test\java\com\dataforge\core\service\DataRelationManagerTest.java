package com.dataforge.core.service;

import com.dataforge.core.relation.FieldRelation;
import com.dataforge.core.relation.RelationResult;
import com.dataforge.core.relation.RelationType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Map;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DataRelationManager 单元测试
 */
class DataRelationManagerTest {

    private DataRelationManager relationManager;

    @BeforeEach
    void setUp() {
        relationManager = new DataRelationManager();
    }

    @Test
    void testSetAndGetRelatedValue() {
        relationManager.setRelatedValue("test_key", "test_value");

        assertEquals("test_value", relationManager.getRelatedValue("test_key"));
        assertEquals("test_value", relationManager.getRelatedValue("test_key", String.class));
        assertNull(relationManager.getRelatedValue("test_key", Integer.class));
    }

    @Test
    void testSetRelatedValueWithNull() {
        relationManager.setRelatedValue(null, "value");
        assertEquals(0, relationManager.size());
    }

    @Test
    void testHasRelation() {
        assertFalse(relationManager.hasRelation("test_key"));

        relationManager.setRelatedValue("test_key", "test_value");
        assertTrue(relationManager.hasRelation("test_key"));
    }

    @Test
    void testRemoveRelatedValue() {
        relationManager.setRelatedValue("test_key", "test_value");

        Object removed = relationManager.removeRelatedValue("test_key");
        assertEquals("test_value", removed);
        assertFalse(relationManager.hasRelation("test_key"));

        Object notFound = relationManager.removeRelatedValue("non_existent");
        assertNull(notFound);
    }

    @Test
    void testClearAll() {
        relationManager.setRelatedValue("key1", "value1");
        relationManager.setRelatedValue("key2", "value2");
        assertEquals(2, relationManager.size());

        relationManager.clearAll();
        assertEquals(0, relationManager.size());
        assertTrue(relationManager.isEmpty());
    }

    @Test
    void testGetAllRelatedValues() {
        relationManager.setRelatedValue("key1", "value1");
        relationManager.setRelatedValue("key2", "value2");

        Map<String, Object> allValues = relationManager.getAllRelatedValues();
        assertEquals(2, allValues.size());
        assertEquals("value1", allValues.get("key1"));
        assertEquals("value2", allValues.get("key2"));

        // 测试返回的是副本，修改不会影响原数据
        allValues.put("key3", "value3");
        assertFalse(relationManager.hasRelation("key3"));
    }

    @Test
    void testRegisterAndGetFieldRelation() {
        FieldRelation relation = FieldRelation.builder()
                .source("test_source")
                .targets(Set.of("test_target"))
                .type(RelationType.ONE_TO_ONE)
                .function(value -> RelationResult.success("test_target", value.toString().toUpperCase()))
                .description("测试关联")
                .build();

        relationManager.registerFieldRelation(relation);

        FieldRelation retrieved = relationManager.getFieldRelation("test_source");
        assertEquals(relation, retrieved);
        assertTrue(relationManager.hasFieldRelation("test_source"));
    }

    @Test
    void testRegisterFieldRelationWithNull() {
        int initialSize = relationManager.getAllFieldRelations().size();
        relationManager.registerFieldRelation(null);
        // 注册null不应该改变字段关联的数量
        assertEquals(initialSize, relationManager.getAllFieldRelations().size());
    }

    @Test
    void testRemoveFieldRelation() {
        FieldRelation relation = FieldRelation.builder()
                .source("test_source")
                .targets(Set.of("test_target"))
                .type(RelationType.ONE_TO_ONE)
                .function(value -> RelationResult.success())
                .build();

        relationManager.registerFieldRelation(relation);
        assertTrue(relationManager.hasFieldRelation("test_source"));

        FieldRelation removed = relationManager.removeFieldRelation("test_source");
        assertEquals(relation, removed);
        assertFalse(relationManager.hasFieldRelation("test_source"));

        FieldRelation notFound = relationManager.removeFieldRelation("non_existent");
        assertNull(notFound);
    }

    @Test
    void testFieldRelationExecution() {
        // 创建一个简单的关联规则：将输入转换为大写并设置到目标字段
        FieldRelation relation = FieldRelation.builder()
                .source("input")
                .targets(Set.of("output"))
                .type(RelationType.ONE_TO_ONE)
                .function(value -> RelationResult.success("output", value.toString().toUpperCase()))
                .description("转换为大写")
                .build();

        relationManager.registerFieldRelation(relation);

        // 设置源字段值，应该自动触发关联更新
        relationManager.setRelatedValue("input", "hello");

        // 检查目标字段是否被正确设置
        assertEquals("HELLO", relationManager.getRelatedValue("output"));
    }

    @Test
    void testFieldRelationExecutionFailure() {
        // 创建一个会失败的关联规则
        FieldRelation relation = FieldRelation.builder()
                .source("input")
                .targets(Set.of("output"))
                .type(RelationType.ONE_TO_ONE)
                .function(value -> RelationResult.failure("测试失败"))
                .description("会失败的关联")
                .build();

        relationManager.registerFieldRelation(relation);

        // 设置源字段值
        relationManager.setRelatedValue("input", "test");

        // 目标字段不应该被设置
        assertNull(relationManager.getRelatedValue("output"));
    }

    @Test
    void testIdCardRelationIntegration() {
        // 测试内置的身份证号关联
        String idCard = "110101199001011234"; // 1990年1月1日出生的男性
        relationManager.setRelatedValue("idcard", idCard);

        // 检查关联字段是否被正确设置
        // 新的实现使用格式化的日期格式
        assertEquals("1990-01-01", relationManager.getRelatedValue("birth_date"));
        assertEquals("MALE", relationManager.getRelatedValue("gender"));
        assertEquals("110101", relationManager.getRelatedValue("region_code"));

        Integer age = relationManager.getRelatedValue("age", Integer.class);
        assertNotNull(age);
        assertTrue(age >= 30); // 假设当前年份至少是2020年
    }

    @Test
    void testNameRelationIntegration() {
        // 测试内置的姓名关联
        relationManager.setRelatedValue("name", "张三");

        // 检查关联字段是否被正确设置
        assertEquals("张", relationManager.getRelatedValue("surname"));
        assertEquals("三", relationManager.getRelatedValue("given_name"));
        assertNotNull(relationManager.getRelatedValue("name_pinyin"));
        assertNotNull(relationManager.getRelatedValue("email_username"));
    }

    @Test
    void testAgeRelationIntegration() {
        // 测试内置的年龄关联
        relationManager.setRelatedValue("age", 25);

        // 检查关联字段是否被正确设置
        Integer birthYear = relationManager.getRelatedValue("birth_year", Integer.class);
        assertNotNull(birthYear);
        assertTrue(birthYear > 1900 && birthYear < 2100);
    }

    @Test
    void testMultipleRelationsChain() {
        // 测试多个关联的链式执行
        relationManager.setRelatedValue("name", "李四");
        relationManager.setRelatedValue("age", 30);

        // 检查所有相关字段都被正确设置
        assertEquals("李", relationManager.getRelatedValue("surname"));
        assertEquals("四", relationManager.getRelatedValue("given_name"));
        assertNotNull(relationManager.getRelatedValue("email_username"));
        assertNotNull(relationManager.getRelatedValue("birth_year"));
    }

    @Test
    void testCircularReferenceAvoidance() {
        // 创建可能导致循环引用的关联
        FieldRelation relation1 = FieldRelation.builder()
                .source("field1")
                .targets(Set.of("field2"))
                .type(RelationType.ONE_TO_ONE)
                .function(value -> RelationResult.success("field2", "from_field1"))
                .build();

        FieldRelation relation2 = FieldRelation.builder()
                .source("field2")
                .targets(Set.of("field1"))
                .type(RelationType.ONE_TO_ONE)
                .function(value -> RelationResult.success("field1", "from_field2"))
                .build();

        relationManager.registerFieldRelation(relation1);
        relationManager.registerFieldRelation(relation2);

        // 设置初始值，不应该导致无限循环
        relationManager.setRelatedValue("field1", "initial");

        // 验证值被正确设置且没有无限循环
        assertEquals("initial", relationManager.getRelatedValue("field1"));
        assertEquals("from_field1", relationManager.getRelatedValue("field2"));
    }

    @Test
    void testToString() {
        relationManager.setRelatedValue("key1", "value1");

        String toString = relationManager.toString();
        assertTrue(toString.contains("DataRelationManager"));
        assertTrue(toString.contains("contextSize=1"));
    }

    @Test
    void testDefaultFieldRelationsInitialization() {
        // 验证默认的字段关联已经被初始化
        Map<String, FieldRelation> allRelations = relationManager.getAllFieldRelations();

        // 检查是否有预期的关联
        assertTrue(relationManager.hasFieldRelation("idcard"), "应该有身份证号关联");
        assertTrue(relationManager.hasFieldRelation("name"), "应该有姓名关联");

        // 检查总数量（至少应该有几个默认关联）
        assertTrue(allRelations.size() >= 3, "至少应该有3个默认关联，实际有: " + allRelations.size());
    }
}