package examples;

import com.dataforge.core.service.DataRelationManager;
import com.dataforge.core.relation.FieldRelation;
import com.dataforge.core.relation.RelationResult;
import com.dataforge.core.relation.RelationRuleFactory;

import java.util.Map;

/**
 * 字段关联规则演示程序
 * 
 * 演示如何使用DataRelationManager和RelationRuleFactory来处理数据字段间的关联关系。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class FieldRelationDemo {

    public static void main(String[] args) {
        System.out.println("=== DataForge 字段关联规则演示 ===\n");

        // 创建数据关联管理器
        DataRelationManager relationManager = new DataRelationManager();

        // 演示1: 身份证号关联规则
        demonstrateIdCardRelation(relationManager);

        // 演示2: 姓名关联规则
        demonstrateNameRelation(relationManager);

        // 演示3: 邮箱关联规则
        demonstrateEmailRelation(relationManager);

        // 演示4: 地址关联规则
        demonstrateAddressRelation(relationManager);

        // 演示5: 年龄关联规则
        demonstrateAgeRelation(relationManager);

        // 演示6: 数据关联管理器的使用
        demonstrateRelationManager(relationManager);

        System.out.println("\n=== 演示完成 ===");
    }

    /**
     * 演示身份证号关联规则
     */
    private static void demonstrateIdCardRelation(DataRelationManager relationManager) {
        System.out.println("1. 身份证号关联规则演示");
        System.out.println("----------------------------------------");

        FieldRelation idCardRelation = RelationRuleFactory.createIdCardRelation();
        String idCard = "110101199001011234";

        System.out.println("输入身份证号: " + idCard);

        RelationResult result = idCardRelation.execute(idCard);
        if (result.isSuccess()) {
            System.out.println("关联结果:");
            for (Map.Entry<String, Object> entry : result.getResultData().entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }
        } else {
            System.out.println("关联失败: " + result.getErrorMessage());
        }

        System.out.println();
    }

    /**
     * 演示姓名关联规则
     */
    private static void demonstrateNameRelation(DataRelationManager relationManager) {
        System.out.println("2. 姓名关联规则演示");
        System.out.println("----------------------------------------");

        FieldRelation nameRelation = RelationRuleFactory.createNameRelation();
        String name = "张三";

        System.out.println("输入姓名: " + name);

        RelationResult result = nameRelation.execute(name);
        if (result.isSuccess()) {
            System.out.println("关联结果:");
            for (Map.Entry<String, Object> entry : result.getResultData().entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }
        } else {
            System.out.println("关联失败: " + result.getErrorMessage());
        }

        System.out.println();
    }

    /**
     * 演示邮箱关联规则
     */
    private static void demonstrateEmailRelation(DataRelationManager relationManager) {
        System.out.println("3. 邮箱关联规则演示");
        System.out.println("----------------------------------------");

        FieldRelation emailRelation = RelationRuleFactory.createEmailRelation();
        String name = "李四";

        System.out.println("输入姓名: " + name);

        RelationResult result = emailRelation.execute(name);
        if (result.isSuccess()) {
            System.out.println("关联结果:");
            for (Map.Entry<String, Object> entry : result.getResultData().entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }
        } else {
            System.out.println("关联失败: " + result.getErrorMessage());
        }

        System.out.println();
    }

    /**
     * 演示地址关联规则
     */
    private static void demonstrateAddressRelation(DataRelationManager relationManager) {
        System.out.println("4. 地址关联规则演示");
        System.out.println("----------------------------------------");

        FieldRelation addressRelation = RelationRuleFactory.createAddressRelation();
        String address = "北京市朝阳区建国门外大街1号";

        System.out.println("输入地址: " + address);

        RelationResult result = addressRelation.execute(address);
        if (result.isSuccess()) {
            System.out.println("关联结果:");
            for (Map.Entry<String, Object> entry : result.getResultData().entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }
        } else {
            System.out.println("关联失败: " + result.getErrorMessage());
        }

        System.out.println();
    }

    /**
     * 演示年龄关联规则
     */
    private static void demonstrateAgeRelation(DataRelationManager relationManager) {
        System.out.println("5. 年龄关联规则演示");
        System.out.println("----------------------------------------");

        FieldRelation ageRelation = RelationRuleFactory.createAgeRelation();
        int age = 25;

        System.out.println("输入年龄: " + age);

        RelationResult result = ageRelation.execute(age);
        if (result.isSuccess()) {
            System.out.println("关联结果:");
            for (Map.Entry<String, Object> entry : result.getResultData().entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue());
            }
        } else {
            System.out.println("关联失败: " + result.getErrorMessage());
        }

        System.out.println();
    }

    /**
     * 演示数据关联管理器的使用
     */
    private static void demonstrateRelationManager(DataRelationManager relationManager) {
        System.out.println("6. 数据关联管理器演示");
        System.out.println("----------------------------------------");

        // 清空之前的数据
        relationManager.clearAll();

        // 设置身份证号，触发关联字段的自动生成
        String idCard = "110101199001011234";
        System.out.println("设置身份证号: " + idCard);
        relationManager.setRelatedValue("idcard", idCard);

        // 查看自动生成的关联字段
        System.out.println("\n自动生成的关联字段:");
        Map<String, Object> allValues = relationManager.getAllRelatedValues();
        for (Map.Entry<String, Object> entry : allValues.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }

        // 设置姓名，触发更多关联字段
        String name = "王五";
        System.out.println("\n设置姓名: " + name);
        relationManager.setRelatedValue("name", name);

        // 查看所有关联字段
        System.out.println("\n所有关联字段:");
        allValues = relationManager.getAllRelatedValues();
        for (Map.Entry<String, Object> entry : allValues.entrySet()) {
            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
        }

        // 显示统计信息
        System.out.println("\n关联管理器统计:");
        System.out.println("  关联值数量: " + relationManager.size());
        System.out.println("  字段关联数量: " + relationManager.getAllFieldRelations().size());
        System.out.println("  管理器状态: " + relationManager.toString());

        System.out.println();
    }
}