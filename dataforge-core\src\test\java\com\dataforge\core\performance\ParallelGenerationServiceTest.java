package com.dataforge.core.performance;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.generator.GenerationException;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Timeout;

import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicInteger;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 并行生成服务测试
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
class ParallelGenerationServiceTest {

    private ParallelGenerationService service;
    private ThreadPoolConfig config;

    @BeforeEach
    void setUp() {
        config = new ThreadPoolConfig();
        config.setCorePoolSize(2);
        config.setMaximumPoolSize(4);
        service = new ParallelGenerationService(config);
    }

    @AfterEach
    void tearDown() {
        if (service != null) {
            service.shutdown();
        }
    }

    @Test
    void testParallelGeneration() throws InterruptedException, ExecutionException, GenerationException {
        // 创建测试生成器
        TestDataGenerator generator = new TestDataGenerator();
        GenerationContext context = new GenerationContext.Builder().build();

        // 并行生成数据
        List<String> results = service.generateParallel(generator, context, 100);

        // 验证结果
        assertNotNull(results);
        assertEquals(100, results.size());

        // 验证所有结果都不为空
        for (String result : results) {
            assertNotNull(result);
            assertTrue(result.startsWith("data_"));
        }

        // 验证生成器被调用了正确的次数
        assertEquals(100, generator.getCallCount());
    }

    @Test
    void testSmallCountSequentialGeneration() throws InterruptedException, ExecutionException, GenerationException {
        TestDataGenerator generator = new TestDataGenerator();
        GenerationContext context = new GenerationContext.Builder().build();

        // 小数量应该使用顺序生成
        List<String> results = service.generateParallel(generator, context, 10);

        assertNotNull(results);
        assertEquals(10, results.size());
        assertEquals(10, generator.getCallCount());
    }

    @Test
    void testZeroCount() throws InterruptedException, ExecutionException, GenerationException {
        TestDataGenerator generator = new TestDataGenerator();
        GenerationContext context = new GenerationContext.Builder().build();

        List<String> results = service.generateParallel(generator, context, 0);

        assertNotNull(results);
        assertTrue(results.isEmpty());
        assertEquals(0, generator.getCallCount());
    }

    @Test
    void testNegativeCount() throws InterruptedException, ExecutionException, GenerationException {
        TestDataGenerator generator = new TestDataGenerator();
        GenerationContext context = new GenerationContext.Builder().build();

        List<String> results = service.generateParallel(generator, context, -5);

        assertNotNull(results);
        assertTrue(results.isEmpty());
        assertEquals(0, generator.getCallCount());
    }

    @Test
    @Timeout(10)
    void testLargeCountGeneration() throws InterruptedException, ExecutionException, GenerationException {
        TestDataGenerator generator = new TestDataGenerator();
        GenerationContext context = new GenerationContext.Builder().build();

        // 大数量测试
        List<String> results = service.generateParallel(generator, context, 10000);

        assertNotNull(results);
        assertEquals(10000, results.size());
        assertEquals(10000, generator.getCallCount());
    }

    @Test
    void testThreadPoolStatus() {
        ParallelGenerationService.ThreadPoolStatus status = service.getThreadPoolStatus();

        assertNotNull(status);
        assertEquals(2, status.getCorePoolSize());
        assertEquals(4, status.getMaximumPoolSize());
        assertTrue(status.getActiveCount() >= 0);
        assertTrue(status.getTaskCount() >= 0);
        assertTrue(status.getCompletedTaskCount() >= 0);
        assertTrue(status.getQueueSize() >= 0);
    }

    @Test
    void testGeneratorException() {
        // 创建会抛出异常的生成器
        DataGenerator<String> faultyGenerator = new DataGenerator<String>() {
            @Override
            public String generate(GenerationContext context) {
                throw new RuntimeException("Test exception");
            }

            @Override
            public String getType() {
                return "faulty";
            }

            @Override
            public GeneratorParameter[] getSupportedParameters() {
                return new GeneratorParameter[0];
            }

            @Override
            public ValidationResult validateWithDetails(String data) {
                ValidationResult result = new ValidationResult();
                if (data != null && !data.isEmpty()) {
                    // 数据有效，无需添加错误
                } else {
                    result.addError("数据不能为空");
                }
                return result;
            }

            @Override
            public String getDescription() {
                return "Faulty test generator";
            }
        };

        GenerationContext context = new GenerationContext.Builder().build();

        // 验证异常被正确传播
        assertThrows(ExecutionException.class, () -> {
            service.generateParallel(faultyGenerator, context, 10);
        });
    }

    /**
     * 测试用数据生成器
     */
    private static class TestDataGenerator implements DataGenerator<String> {
        private final AtomicInteger callCount = new AtomicInteger(0);

        @Override
        public String generate(GenerationContext context) throws GenerationException {
            int count = callCount.incrementAndGet();
            // 模拟一些处理时间
            try {
                Thread.sleep(1);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
            return "data_" + count + "_" + Thread.currentThread().getName();
        }

        @Override
        public String getType() {
            return "test";
        }

        @Override
        public GeneratorParameter[] getSupportedParameters() {
            return new GeneratorParameter[0];
        }

        @Override
        public ValidationResult validateWithDetails(String data) {
            ValidationResult result = new ValidationResult();
            if (data != null && !data.isEmpty()) {
                // 数据有效，无需添加错误
            } else {
                result.addError("数据不能为空");
            }
            return result;
        }

        @Override
        public String getDescription() {
            return "Test data generator";
        }

        public int getCallCount() {
            return callCount.get();
        }
    }
}