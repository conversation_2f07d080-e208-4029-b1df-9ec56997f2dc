package com.dataforge.core.service;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.List;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;

/**
 * GeneratorRegistry测试类
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
class GeneratorRegistryTest {

    private GeneratorRegistry generatorRegistry;

    @BeforeEach
    void setUp() {
        generatorRegistry = new GeneratorRegistry();
    }

    @Test
    void testRegisterGenerator() {
        TestGenerator testGenerator = new TestGenerator();

        // 注册生成器
        boolean registered = generatorRegistry.registerGenerator(testGenerator);
        assertTrue(registered, "生成器应该注册成功");

        // 验证注册成功
        assertTrue(generatorRegistry.hasGenerator("test"), "生成器应该存在");

        DataGenerator<?> retrievedGenerator = generatorRegistry.getGenerator("test");
        assertNotNull(retrievedGenerator, "应该能够获取到注册的生成器");
        assertEquals("test", retrievedGenerator.getType(), "生成器类型应该匹配");
    }

    @Test
    void testRegisterNullGenerator() {
        boolean registered = generatorRegistry.registerGenerator(null);
        assertFalse(registered, "注册null生成器应该失败");
    }

    @Test
    void testBatchRegisterGenerators() {
        TestGenerator testGenerator1 = new TestGenerator("test1");
        TestGenerator testGenerator2 = new TestGenerator("test2");

        List<DataGenerator<?>> generators = Arrays.asList(testGenerator1, testGenerator2);

        int registeredCount = generatorRegistry.registerGenerators(generators);
        assertEquals(2, registeredCount, "应该注册2个生成器");

        assertTrue(generatorRegistry.hasGenerator("test1"), "test1生成器应该存在");
        assertTrue(generatorRegistry.hasGenerator("test2"), "test2生成器应该存在");
    }

    @Test
    void testUnregisterGenerator() {
        TestGenerator testGenerator = new TestGenerator();

        // 注册生成器
        generatorRegistry.registerGenerator(testGenerator);
        assertTrue(generatorRegistry.hasGenerator("test"), "生成器应该注册成功");

        // 注销生成器
        boolean unregistered = generatorRegistry.unregisterGenerator("test");
        assertTrue(unregistered, "注销操作应该成功");
        assertFalse(generatorRegistry.hasGenerator("test"), "生成器应该被注销");

        // 再次注销不存在的生成器
        boolean unregisteredAgain = generatorRegistry.unregisterGenerator("test");
        assertFalse(unregisteredAgain, "注销不存在的生成器应该返回false");
    }

    @Test
    void testGetRegisteredTypes() {
        int initialCount = generatorRegistry.getGeneratorCount();

        TestGenerator testGenerator = new TestGenerator();
        generatorRegistry.registerGenerator(testGenerator);

        Set<String> types = generatorRegistry.getRegisteredTypes();
        assertNotNull(types, "注册类型集合不应该为null");
        assertEquals(initialCount + 1, types.size(), "类型数量应该正确");
        assertTrue(types.contains("test"), "应该包含test类型");
    }

    @Test
    void testGetGeneratorMetadata() {
        TestGenerator testGenerator = new TestGenerator();
        generatorRegistry.registerGenerator(testGenerator);

        GeneratorRegistry.GeneratorMetadata metadata = generatorRegistry.getGeneratorMetadata("test");

        assertNotNull(metadata, "元数据不应该为null");
        assertEquals("test", metadata.getType(), "类型应该匹配");
        assertEquals(testGenerator.getClass().getName(), metadata.getClassName(), "类名应该匹配");
        assertEquals(testGenerator.getDescription(), metadata.getDescription(), "描述应该匹配");
        assertEquals(GeneratorRegistry.GeneratorSource.RUNTIME, metadata.getSource(), "来源应该是RUNTIME");
    }

    @Test
    void testGetAllGeneratorMetadata() {
        TestGenerator testGenerator = new TestGenerator();
        generatorRegistry.registerGenerator(testGenerator);

        List<GeneratorRegistry.GeneratorMetadata> metadataList = generatorRegistry.getAllGeneratorMetadata();

        assertNotNull(metadataList, "元数据列表不应该为null");
        assertTrue(metadataList.size() > 0, "应该有元数据");

        boolean foundTest = metadataList.stream()
                .anyMatch(metadata -> "test".equals(metadata.getType()));
        assertTrue(foundTest, "应该找到test生成器的元数据");
    }

    @Test
    void testClear() {
        TestGenerator testGenerator = new TestGenerator();
        generatorRegistry.registerGenerator(testGenerator);

        assertTrue(generatorRegistry.getGeneratorCount() > 0, "应该有注册的生成器");

        generatorRegistry.clear();
        assertEquals(0, generatorRegistry.getGeneratorCount(), "清除后应该没有生成器");
        assertFalse(generatorRegistry.hasGenerator("test"), "test生成器应该被清除");
    }

    @Test
    void testBuiltinGenerators() {
        // 验证内置生成器是否被自动注册
        assertTrue(generatorRegistry.hasGenerator("name"), "应该有name生成器");
        assertTrue(generatorRegistry.hasGenerator("email"), "应该有email生成器");
        assertTrue(generatorRegistry.hasGenerator("phone"), "应该有phone生成器");

        // 验证内置生成器的元数据
        GeneratorRegistry.GeneratorMetadata nameMetadata = generatorRegistry.getGeneratorMetadata("name");
        assertNotNull(nameMetadata, "name生成器应该有元数据");
        assertEquals(GeneratorRegistry.GeneratorSource.BUILTIN, nameMetadata.getSource(),
                "内置生成器来源应该是BUILTIN");
    }

    /**
     * 测试用的生成器实现
     */
    private static class TestGenerator implements DataGenerator<String> {
        private final String type;

        public TestGenerator() {
            this("test");
        }

        public TestGenerator(String type) {
            this.type = type;
        }

        @Override
        public String generate(GenerationContext context) {
            return type + "-value";
        }

        @Override
        public ValidationResult validateWithDetails(String data) {
            return ValidationResult.success();
        }

        @Override
        public String getType() {
            return type;
        }

        @Override
        public String getDescription() {
            return "测试生成器: " + type;
        }

        @Override
        public GeneratorParameter[] getSupportedParameters() {
            return new GeneratorParameter[0];
        }

        @Override
        public boolean isThreadSafe() {
            return true;
        }
    }
}