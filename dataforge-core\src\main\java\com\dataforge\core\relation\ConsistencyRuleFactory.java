package com.dataforge.core.relation;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * 一致性规则工厂
 * 
 * 提供常见的数据一致性规则创建方法。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ConsistencyRuleFactory {

    /**
     * 创建身份证号与年龄一致性规则
     * 
     * @return 身份证号年龄一致性规则
     */
    public static ConsistencyRule createIdCardAgeConsistencyRule() {
        return new AbstractConsistencyRule(
                "idcard_age_consistency",
                "确保身份证号与年龄信息一致",
                Set.of("idcard", "age", "birth_date"),
                1) {
            @Override
            public ConsistencyResult check(Map<String, Object> fieldValues) {
                String idCard = getStringValue(fieldValues, "idcard");
                Integer age = getIntegerValue(fieldValues, "age");

                if (idCard == null || age == null) {
                    return ConsistencyResult.success("缺少必要字段，跳过检查");
                }

                if (idCard.length() != 18) {
                    return ConsistencyResult.success("身份证号格式不正确，跳过检查");
                }

                try {
                    String birthDateStr = idCard.substring(6, 14);
                    LocalDate birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
                    int calculatedAge = Period.between(birthDate, LocalDate.now()).getYears();

                    if (Math.abs(calculatedAge - age) > 1) {
                        return ConsistencyResult.failure(List.of(
                                String.format("年龄不一致：身份证号显示年龄为%d，但设置的年龄为%d", calculatedAge, age)));
                    }

                    return ConsistencyResult.success("身份证号与年龄一致");
                } catch (Exception e) {
                    return ConsistencyResult.successWithWarnings(List.of("身份证号格式错误，无法验证年龄一致性"));
                }
            }

            @Override
            public ConsistencyFixResult fix(Map<String, Object> fieldValues) {
                String idCard = getStringValue(fieldValues, "idcard");

                if (idCard == null || idCard.length() != 18) {
                    return ConsistencyFixResult.failure(List.of("身份证号无效，无法修复年龄"));
                }

                try {
                    String birthDateStr = idCard.substring(6, 14);
                    LocalDate birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
                    int calculatedAge = Period.between(birthDate, LocalDate.now()).getYears();

                    Map<String, Object> fixedValues = new HashMap<>();
                    fixedValues.put("age", calculatedAge);
                    fixedValues.put("birth_date", birthDate.toString());

                    return ConsistencyFixResult.success(fixedValues, List.of("根据身份证号修复年龄和出生日期"));
                } catch (Exception e) {
                    return ConsistencyFixResult.failure(List.of("身份证号格式错误，无法修复年龄"));
                }
            }
        };
    }

    /**
     * 创建身份证号与性别一致性规则
     * 
     * @return 身份证号性别一致性规则
     */
    public static ConsistencyRule createIdCardGenderConsistencyRule() {
        return new AbstractConsistencyRule(
                "idcard_gender_consistency",
                "确保身份证号与性别信息一致",
                Set.of("idcard", "gender"),
                2) {
            @Override
            public ConsistencyResult check(Map<String, Object> fieldValues) {
                String idCard = getStringValue(fieldValues, "idcard");
                String gender = getStringValue(fieldValues, "gender");

                if (idCard == null || gender == null) {
                    return ConsistencyResult.success("缺少必要字段，跳过检查");
                }

                if (idCard.length() != 18) {
                    return ConsistencyResult.success("身份证号格式不正确，跳过检查");
                }

                try {
                    int genderCode = Integer.parseInt(idCard.substring(16, 17));
                    String expectedGender = (genderCode % 2 == 1) ? "MALE" : "FEMALE";

                    if (!expectedGender.equalsIgnoreCase(gender)) {
                        return ConsistencyResult.failure(List.of(
                                String.format("性别不一致：身份证号显示性别为%s，但设置的性别为%s", expectedGender, gender)));
                    }

                    return ConsistencyResult.success("身份证号与性别一致");
                } catch (Exception e) {
                    return ConsistencyResult.successWithWarnings(List.of("身份证号格式错误，无法验证性别一致性"));
                }
            }

            @Override
            public ConsistencyFixResult fix(Map<String, Object> fieldValues) {
                String idCard = getStringValue(fieldValues, "idcard");

                if (idCard == null || idCard.length() != 18) {
                    return ConsistencyFixResult.failure(List.of("身份证号无效，无法修复性别"));
                }

                try {
                    int genderCode = Integer.parseInt(idCard.substring(16, 17));
                    String correctGender = (genderCode % 2 == 1) ? "MALE" : "FEMALE";

                    Map<String, Object> fixedValues = new HashMap<>();
                    fixedValues.put("gender", correctGender);

                    return ConsistencyFixResult.success(fixedValues, List.of("根据身份证号修复性别"));
                } catch (Exception e) {
                    return ConsistencyFixResult.failure(List.of("身份证号格式错误，无法修复性别"));
                }
            }
        };
    }

    /**
     * 创建姓名与性别一致性规则
     * 
     * @return 姓名性别一致性规则
     */
    public static ConsistencyRule createNameGenderConsistencyRule() {
        return new AbstractConsistencyRule(
                "name_gender_consistency",
                "确保姓名与性别信息合理匹配",
                Set.of("name", "gender"),
                3) {
            @Override
            public ConsistencyResult check(Map<String, Object> fieldValues) {
                // 这是一个简化的实现，实际应该基于姓名数据库
                return ConsistencyResult.success("姓名性别一致性检查通过（简化实现）");
            }

            @Override
            public boolean canFix() {
                return false; // 姓名性别关系复杂，不提供自动修复
            }

            @Override
            public ConsistencyFixResult fix(Map<String, Object> fieldValues) {
                return ConsistencyFixResult.noActionNeeded("姓名性别关系复杂，不支持自动修复");
            }
        };
    }

    /**
     * 创建邮箱与姓名一致性规则
     * 
     * @return 邮箱姓名一致性规则
     */
    public static ConsistencyRule createEmailNameConsistencyRule() {
        return new AbstractConsistencyRule(
                "email_name_consistency",
                "确保邮箱用户名与姓名相关",
                Set.of("email", "name", "email_username"),
                4) {
            @Override
            public ConsistencyResult check(Map<String, Object> fieldValues) {
                String email = getStringValue(fieldValues, "email");
                String name = getStringValue(fieldValues, "name");

                if (email == null || name == null) {
                    return ConsistencyResult.success("缺少必要字段，跳过检查");
                }

                // 简化的检查逻辑
                String username = email.split("@")[0];
                if (username.length() < 3) {
                    return ConsistencyResult.successWithWarnings(List.of("邮箱用户名过短"));
                }

                return ConsistencyResult.success("邮箱与姓名一致性检查通过");
            }

            @Override
            public ConsistencyFixResult fix(Map<String, Object> fieldValues) {
                String name = getStringValue(fieldValues, "name");
                String email = getStringValue(fieldValues, "email");

                if (name == null) {
                    return ConsistencyFixResult.failure(List.of("缺少姓名信息，无法修复邮箱"));
                }

                // 生成基于姓名的邮箱用户名
                String username = generateEmailUsername(name);

                Map<String, Object> fixedValues = new HashMap<>();
                fixedValues.put("email_username", username);

                if (email != null && email.contains("@")) {
                    String domain = email.split("@")[1];
                    fixedValues.put("email", username + "@" + domain);
                }

                return ConsistencyFixResult.success(fixedValues, List.of("根据姓名修复邮箱用户名"));
            }

            private String generateEmailUsername(String name) {
                // 简化的用户名生成逻辑
                return name.toLowerCase().replaceAll("[^a-z0-9]", "") +
                        String.valueOf((int) (Math.random() * 1000));
            }
        };
    }

    /**
     * 抽象一致性规则基类
     */
    private static abstract class AbstractConsistencyRule implements ConsistencyRule {
        private final String ruleName;
        private final String description;
        private final Set<String> involvedFields;
        private final int priority;

        public AbstractConsistencyRule(String ruleName, String description,
                Set<String> involvedFields, int priority) {
            this.ruleName = ruleName;
            this.description = description;
            this.involvedFields = involvedFields;
            this.priority = priority;
        }

        @Override
        public String getRuleName() {
            return ruleName;
        }

        @Override
        public String getDescription() {
            return description;
        }

        @Override
        public Set<String> getInvolvedFields() {
            return involvedFields;
        }

        @Override
        public int getPriority() {
            return priority;
        }

        @Override
        public boolean canFix() {
            return true;
        }

        protected String getStringValue(Map<String, Object> fieldValues, String key) {
            Object value = fieldValues.get(key);
            return value != null ? value.toString() : null;
        }

        protected Integer getIntegerValue(Map<String, Object> fieldValues, String key) {
            Object value = fieldValues.get(key);
            if (value instanceof Integer) {
                return (Integer) value;
            } else if (value instanceof Number) {
                return ((Number) value).intValue();
            } else if (value instanceof String) {
                try {
                    return Integer.parseInt((String) value);
                } catch (NumberFormatException e) {
                    return null;
                }
            }
            return null;
        }
    }
}