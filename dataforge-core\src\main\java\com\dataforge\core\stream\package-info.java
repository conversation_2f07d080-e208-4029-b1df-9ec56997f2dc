/**
 * 流式处理包
 * 
 * 提供大数据量的流式数据生成和处理能力，支持背压控制、
 * 内存优化和并行处理。
 * 
 * <h2>主要组件：</h2>
 * <ul>
 * <li>{@link com.dataforge.core.stream.StreamDataGenerator} - 流式数据生成器接口</li>
 * <li>{@link com.dataforge.core.stream.AbstractStreamDataGenerator} -
 * 抽象流式数据生成器</li>
 * <li>{@link com.dataforge.core.stream.StreamGenerationService} - 流式数据生成服务</li>
 * <li>{@link com.dataforge.core.stream.StreamOutputProcessor} - 流式输出处理器</li>
 * </ul>
 * 
 * <h2>核心特性：</h2>
 * <ul>
 * <li>大数据量流式生成，避免内存溢出</li>
 * <li>支持并行流处理，提升性能</li>
 * <li>批次处理机制，优化I/O操作</li>
 * <li>背压控制，防止内存压力</li>
 * <li>进度跟踪和监控</li>
 * </ul>
 * 
 * <h2>使用示例：</h2>
 * 
 * <pre>{@code
 * // 创建流式生成服务
 * StreamGenerationService streamService = new StreamGenerationService(generatorFactory);
 * 
 * // 生成大量数据流
 * GenerationContext context = new GenerationContext.Builder().build();
 * Stream<String> nameStream = streamService.generateStream("name", context, 1000000);
 * 
 * // 并行处理
 * Stream<String> parallelStream = streamService.generateParallelStream("name", context, 1000000);
 * 
 * // 批次处理
 * Stream<StreamDataGenerator.Batch<String>> batchStream = streamService.generateBatchStream("name", context, 1000,
 *         1000);
 * 
 * // 流式输出
 * StreamOutputProcessor outputProcessor = new StreamOutputProcessor(outputWriterFactory);
 * StreamOutputProcessor.StreamOutputResult result = outputProcessor.processStream(nameStream, List.of("name"),
 *         outputConfig);
 * 
 * // 带进度跟踪的生成
 * Stream<String> progressStream = streamService.generateStreamWithProgress(
 *         "name", context, 1000000,
 *         (current, total, percentage) -> System.out.println("Progress: " + percentage + "%"));
 * }</pre>
 * 
 * <h2>性能优化：</h2>
 * <ul>
 * <li>懒加载：数据按需生成</li>
 * <li>批次处理：减少I/O操作次数</li>
 * <li>并行处理：充分利用多核CPU</li>
 * <li>内存控制：避免大对象创建</li>
 * <li>缓冲机制：平衡内存和性能</li>
 * </ul>
 * 
 * <h2>适用场景：</h2>
 * <ul>
 * <li>大数据量测试数据生成</li>
 * <li>实时数据流处理</li>
 * <li>批量数据导出</li>
 * <li>数据迁移和转换</li>
 * <li>性能测试数据准备</li>
 * </ul>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
package com.dataforge.core.stream;