package com.dataforge.core.relation;

import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RelationResult 单元测试
 */
class RelationResultTest {

    @Test
    void testSuccessResult() {
        Map<String, Object> data = new HashMap<>();
        data.put("field1", "value1");
        data.put("field2", 123);

        RelationResult result = RelationResult.success(data);

        assertTrue(result.isSuccess());
        assertFalse(result.isFailure());
        assertEquals(2, result.size());
        assertEquals("value1", result.getValue("field1"));
        assertEquals(123, result.getValue("field2"));
        assertNull(result.getErrorMessage());
    }

    @Test
    void testSuccessResultSingleValue() {
        RelationResult result = RelationResult.success("testField", "testValue");

        assertTrue(result.isSuccess());
        assertEquals(1, result.size());
        assertEquals("testValue", result.getValue("testField"));
        assertTrue(result.hasField("testField"));
        assertFalse(result.hasField("nonExistentField"));
    }

    @Test
    void testSuccessResultEmpty() {
        RelationResult result = RelationResult.success();

        assertTrue(result.isSuccess());
        assertEquals(0, result.size());
        assertTrue(result.isEmpty());
        assertNull(result.getErrorMessage());
    }

    @Test
    void testFailureResult() {
        RelationResult result = RelationResult.failure("测试错误信息");

        assertFalse(result.isSuccess());
        assertTrue(result.isFailure());
        assertEquals(0, result.size());
        assertTrue(result.isEmpty());
        assertEquals("测试错误信息", result.getErrorMessage());
    }

    @Test
    void testGetValueWithType() {
        RelationResult result = RelationResult.success("stringField", "test");

        String stringValue = result.getValue("stringField", String.class);
        assertEquals("test", stringValue);

        Integer intValue = result.getValue("stringField", Integer.class);
        assertNull(intValue); // 类型不匹配

        String nonExistentValue = result.getValue("nonExistent", String.class);
        assertNull(nonExistentValue); // 字段不存在
    }

    @Test
    void testGetValueWithTypeNumber() {
        RelationResult result = RelationResult.success("numberField", 42);

        Integer intValue = result.getValue("numberField", Integer.class);
        assertEquals(42, intValue);

        Number numberValue = result.getValue("numberField", Number.class);
        assertEquals(42, numberValue);
    }

    @Test
    void testResultDataImmutability() {
        Map<String, Object> originalData = new HashMap<>();
        originalData.put("field1", "value1");

        RelationResult result = RelationResult.success(originalData);
        Map<String, Object> resultData = result.getResultData();

        // 尝试修改返回的数据应该抛出异常
        assertThrows(UnsupportedOperationException.class, () -> {
            resultData.put("field2", "value2");
        });

        // 修改原始数据不应该影响结果
        originalData.put("field2", "value2");
        assertFalse(result.hasField("field2"));
    }

    @Test
    void testEqualsAndHashCode() {
        RelationResult result1 = RelationResult.success("field", "value");
        RelationResult result2 = RelationResult.success("field", "value");
        RelationResult result3 = RelationResult.success("field", "different");
        RelationResult result4 = RelationResult.failure("error");

        assertEquals(result1, result2);
        assertEquals(result1.hashCode(), result2.hashCode());

        assertNotEquals(result1, result3);
        assertNotEquals(result1, result4);
    }

    @Test
    void testToString() {
        RelationResult successResult = RelationResult.success("field", "value");
        String successString = successResult.toString();
        assertTrue(successString.contains("success=true"));
        assertTrue(successString.contains("field=value"));

        RelationResult failureResult = RelationResult.failure("error message");
        String failureString = failureResult.toString();
        assertTrue(failureString.contains("success=false"));
        assertTrue(failureString.contains("error message"));
    }
}