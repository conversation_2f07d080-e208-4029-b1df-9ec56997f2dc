package com.dataforge.core.output;

import java.util.HashMap;
import java.util.Map;

/**
 * 输出配置类
 * 
 * 封装输出相关的配置信息，包括输出格式、目标路径、格式化选项等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class OutputConfig {

    private String format;
    private String target;
    private boolean includeHeader;
    private String encoding;
    private Map<String, Object> formatOptions;

    /**
     * 默认构造函数
     */
    public OutputConfig() {
        this.format = "console";
        this.target = null;
        this.includeHeader = true;
        this.encoding = "UTF-8";
        this.formatOptions = new HashMap<>();
    }

    /**
     * 构造函数
     * 
     * @param format 输出格式
     * @param target 输出目标
     */
    public OutputConfig(String format, String target) {
        this();
        this.format = format;
        this.target = target;
    }

    /**
     * 获取输出格式
     * 
     * @return 输出格式
     */
    public String getFormat() {
        return format;
    }

    /**
     * 设置输出格式
     * 
     * @param format 输出格式
     */
    public void setFormat(String format) {
        this.format = format;
    }

    /**
     * 获取输出目标
     * 
     * @return 输出目标（文件路径或其他标识符）
     */
    public String getTarget() {
        return target;
    }

    /**
     * 设置输出目标
     * 
     * @param target 输出目标
     */
    public void setTarget(String target) {
        this.target = target;
    }

    /**
     * 是否包含表头
     * 
     * @return 如果包含表头返回true，否则返回false
     */
    public boolean isIncludeHeader() {
        return includeHeader;
    }

    /**
     * 设置是否包含表头
     * 
     * @param includeHeader 是否包含表头
     */
    public void setIncludeHeader(boolean includeHeader) {
        this.includeHeader = includeHeader;
    }

    /**
     * 获取字符编码
     * 
     * @return 字符编码
     */
    public String getEncoding() {
        return encoding;
    }

    /**
     * 设置字符编码
     * 
     * @param encoding 字符编码
     */
    public void setEncoding(String encoding) {
        this.encoding = encoding;
    }

    /**
     * 获取格式化选项
     * 
     * @return 格式化选项映射
     */
    public Map<String, Object> getFormatOptions() {
        return formatOptions;
    }

    /**
     * 设置格式化选项
     * 
     * @param formatOptions 格式化选项映射
     */
    public void setFormatOptions(Map<String, Object> formatOptions) {
        this.formatOptions = formatOptions != null ? formatOptions : new HashMap<>();
    }

    /**
     * 获取格式化选项值
     * 
     * @param key          选项键
     * @param defaultValue 默认值
     * @param <T>          值类型
     * @return 选项值
     */
    @SuppressWarnings("unchecked")
    public <T> T getFormatOption(String key, T defaultValue) {
        Object value = formatOptions.get(key);
        if (value != null && defaultValue != null && defaultValue.getClass().isInstance(value)) {
            return (T) value;
        }
        return defaultValue;
    }

    /**
     * 设置格式化选项值
     * 
     * @param key   选项键
     * @param value 选项值
     */
    public void setFormatOption(String key, Object value) {
        formatOptions.put(key, value);
    }

    /**
     * 检查是否为文件输出
     * 
     * @return 如果是文件输出返回true，否则返回false
     */
    public boolean isFileOutput() {
        return target != null && !target.isEmpty() && !"console".equalsIgnoreCase(target);
    }

    /**
     * 检查是否为控制台输出
     * 
     * @return 如果是控制台输出返回true，否则返回false
     */
    public boolean isConsoleOutput() {
        return target == null || target.isEmpty() || "console".equalsIgnoreCase(target);
    }

    /**
     * 复制配置
     * 
     * @return 配置副本
     */
    public OutputConfig copy() {
        OutputConfig copy = new OutputConfig();
        copy.format = this.format;
        copy.target = this.target;
        copy.includeHeader = this.includeHeader;
        copy.encoding = this.encoding;
        copy.formatOptions = new HashMap<>(this.formatOptions);
        return copy;
    }

    @Override
    public String toString() {
        return "OutputConfig{" +
                "format='" + format + '\'' +
                ", target='" + target + '\'' +
                ", includeHeader=" + includeHeader +
                ", encoding='" + encoding + '\'' +
                ", formatOptions=" + formatOptions +
                '}';
    }
}