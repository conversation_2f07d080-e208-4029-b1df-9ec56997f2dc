package examples;

import com.dataforge.core.service.GeneratorFactory;
import com.dataforge.core.service.GeneratorRegistry;
import com.dataforge.core.generator.DataGenerator;

/**
 * 生成器工厂演示
 * 
 * 演示GeneratorFactory和GeneratorRegistry的基本功能，
 * 包括生成器注册、获取、缓存管理等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class GeneratorFactoryDemo {

    public static void main(String[] args) {
        System.out.println("=== DataForge 生成器工厂演示 ===\n");

        // 创建生成器工厂
        GeneratorFactory factory = new GeneratorFactory();
        
        // 显示已注册的生成器
        System.out.println("1. 已注册的生成器类型:");
        for (String type : factory.getRegisteredTypes()) {
            System.out.println("   - " + type);
        }
        System.out.println("   总计: " + factory.getGeneratorCount() + " 个生成器\n");

        // 获取生成器注册表
        GeneratorRegistry registry = factory.getGeneratorRegistry();
        
        // 显示生成器元数据
        System.out.println("2. 生成器元数据信息:");
        registry.getAllGeneratorMetadata().forEach(metadata -> {
            System.out.println("   类型: " + metadata.getType());
            System.out.println("   类名: " + metadata.getClassName());
            System.out.println("   描述: " + metadata.getDescription());
            System.out.println("   来源: " + metadata.getSource());
            System.out.println("   版本: " + metadata.getVersion());
            System.out.println("   ---");
        });

        // 测试生成器获取
        System.out.println("\n3. 测试生成器获取:");
        testGeneratorRetrieval(factory, "name");
        testGeneratorRetrieval(factory, "email");
        testGeneratorRetrieval(factory, "phone");
        testGeneratorRetrieval(factory, "non-existent");

        // 测试缓存功能
        System.out.println("\n4. 测试缓存功能:");
        System.out.println("   缓存大小: " + factory.getCacheSize());
        
        // 获取生成器（会创建缓存）
        DataGenerator<?> nameGen1 = factory.getGenerator("name");
        System.out.println("   获取name生成器后缓存大小: " + factory.getCacheSize());
        
        // 再次获取（从缓存获取）
        DataGenerator<?> nameGen2 = factory.getGenerator("name");
        System.out.println("   再次获取name生成器，缓存大小: " + factory.getCacheSize());
        
        // 清除缓存
        factory.clearCache();
        System.out.println("   清除缓存后大小: " + factory.getCacheSize());

        // 测试动态注册
        System.out.println("\n5. 测试动态注册:");
        int beforeCount = factory.getGeneratorCount();
        System.out.println("   注册前生成器数量: " + beforeCount);
        
        // 这里可以注册自定义生成器（需要实现DataGenerator接口）
        // factory.registerGenerator("custom", new CustomGenerator());
        
        System.out.println("   注册后生成器数量: " + factory.getGeneratorCount());

        System.out.println("\n=== 演示完成 ===");
    }

    private static void testGeneratorRetrieval(GeneratorFactory factory, String type) {
        DataGenerator<?> generator = factory.getGenerator(type);
        if (generator != null) {
            System.out.println("   ✓ 成功获取 " + type + " 生成器: " + generator.getClass().getSimpleName());
        } else {
            System.out.println("   ✗ 未找到 " + type + " 生成器");
        }
    }
}