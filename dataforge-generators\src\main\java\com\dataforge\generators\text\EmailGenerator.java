package com.dataforge.generators.text;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 邮箱地址生成器
 * 
 * 生成符合RFC 5322规范的邮箱地址，支持多种域名和用户名格式。
 * 可以基于姓名拼音生成用户名，提供真实感强的邮箱地址。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class EmailGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "email";
    private static final String DESCRIPTION = "生成邮箱地址";

    // 邮箱验证正则表达式
    private static final Pattern EMAIL_PATTERN = Pattern.compile(
            "^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");

    // 常见域名
    private static final List<String> COMMON_DOMAINS = Arrays.asList(
            "gmail.com", "163.com", "qq.com", "126.com", "sina.com",
            "hotmail.com", "yahoo.com", "outlook.com", "sohu.com", "foxmail.com");

    // 用户名前缀
    private static final List<String> USERNAME_PREFIXES = Arrays.asList(
            "user", "test", "demo", "admin", "guest", "sample", "example");

    // 用户名后缀
    private static final List<String> USERNAME_SUFFIXES = Arrays.asList(
            "123", "2023", "2024", "01", "02", "03", "abc", "xyz");

    private final Random random;

    /**
     * 构造函数
     */
    public EmailGenerator() {
        this.random = new Random();
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("domain", String.class, "random",
                "指定邮箱域名，如gmail.com，或使用random随机选择", false));
        addParameter(new GeneratorParameter("usernameStyle", String.class, "random",
                "用户名风格：random（随机）、prefix（前缀+数字）、name（基于姓名）", false));
        addParameter(new GeneratorParameter("baseName", String.class, null,
                "当usernameStyle为name时，基于此姓名生成用户名", false));
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        String domain = context.getParameter("domain", "random");
        String usernameStyle = context.getParameter("usernameStyle", "random");
        String baseName = context.getParameter("baseName", null);

        // 生成用户名
        String username = generateUsername(usernameStyle, baseName);

        // 选择域名
        String emailDomain = selectDomain(domain);

        return username + "@" + emailDomain;
    }

    /**
     * 生成用户名
     * 
     * @param style    用户名风格
     * @param baseName 基础姓名
     * @return 生成的用户名
     */
    private String generateUsername(String style, String baseName) {
        switch (style) {
            case "name":
                return generateNameBasedUsername(baseName);
            case "prefix":
                return generatePrefixBasedUsername();
            case "random":
            default:
                return generateRandomUsername();
        }
    }

    /**
     * 基于姓名生成用户名
     * 
     * @param name 姓名
     * @return 用户名
     */
    private String generateNameBasedUsername(String name) {
        if (name == null || name.trim().isEmpty()) {
            return generateRandomUsername();
        }

        // 简化的拼音转换（实际项目中可使用专门的拼音库）
        String pinyin = convertToPinyin(name.trim());

        // 添加随机数字后缀
        int suffix = random.nextInt(1000);
        return pinyin + suffix;
    }

    /**
     * 生成前缀+数字风格的用户名
     * 
     * @return 用户名
     */
    private String generatePrefixBasedUsername() {
        String prefix = USERNAME_PREFIXES.get(random.nextInt(USERNAME_PREFIXES.size()));
        String suffix = USERNAME_SUFFIXES.get(random.nextInt(USERNAME_SUFFIXES.size()));
        return prefix + suffix;
    }

    /**
     * 生成随机用户名
     * 
     * @return 用户名
     */
    private String generateRandomUsername() {
        StringBuilder username = new StringBuilder();

        // 生成5-10位随机字符
        int length = 5 + random.nextInt(6);
        for (int i = 0; i < length; i++) {
            if (random.nextBoolean()) {
                // 添加字母
                username.append((char) ('a' + random.nextInt(26)));
            } else {
                // 添加数字
                username.append(random.nextInt(10));
            }
        }

        return username.toString();
    }

    /**
     * 选择域名
     * 
     * @param domain 指定的域名或"random"
     * @return 选择的域名
     */
    private String selectDomain(String domain) {
        if ("random".equals(domain) || domain == null) {
            return COMMON_DOMAINS.get(random.nextInt(COMMON_DOMAINS.size()));
        }
        return domain;
    }

    /**
     * 简化的中文转拼音方法
     * 实际项目中建议使用专门的拼音库如pinyin4j
     * 
     * @param chinese 中文字符串
     * @return 拼音字符串
     */
    private String convertToPinyin(String chinese) {
        // 这里只是一个简化的示例实现
        // 实际项目中应该使用专门的拼音转换库
        StringBuilder pinyin = new StringBuilder();

        for (char c : chinese.toCharArray()) {
            if (c >= 0x4e00 && c <= 0x9fa5) {
                // 中文字符，简单映射到字母
                pinyin.append((char) ('a' + (c % 26)));
            } else if (Character.isLetterOrDigit(c)) {
                pinyin.append(Character.toLowerCase(c));
            }
        }

        // 如果转换结果为空或太短，使用默认前缀
        if (pinyin.length() < 3) {
            pinyin.append("user");
        }

        return pinyin.toString();
    }

    @Override
    public ValidationResult validateWithDetails(String data) {
        if (data == null) {
            return ValidationResult.error("邮箱地址不能为空");
        }

        String trimmed = data.trim();
        if (trimmed.isEmpty()) {
            return ValidationResult.error("邮箱地址不能为空");
        }

        if (!EMAIL_PATTERN.matcher(trimmed).matches()) {
            return ValidationResult.error("邮箱地址格式不正确");
        }

        // 检查长度限制
        if (trimmed.length() > 254) {
            return ValidationResult.error("邮箱地址长度不能超过254个字符");
        }

        // 检查用户名部分长度
        String[] parts = trimmed.split("@");
        if (parts.length == 2 && parts[0].length() > 64) {
            return ValidationResult.error("邮箱用户名部分长度不能超过64个字符");
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }
}