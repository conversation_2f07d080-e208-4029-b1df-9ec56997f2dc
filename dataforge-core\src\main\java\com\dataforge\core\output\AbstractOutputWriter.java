package com.dataforge.core.output;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

/**
 * 抽象输出器基类
 * 
 * 提供输出器的通用实现，简化具体输出器的开发。
 * 处理状态管理、记录计数等通用功能。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public abstract class AbstractOutputWriter implements OutputWriter {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    protected OutputConfig config;
    protected boolean initialized = false;
    protected boolean closed = false;
    protected final AtomicLong recordCount = new AtomicLong(0);

    @Override
    public void initialize(OutputConfig config) throws IOException {
        if (config == null) {
            throw new IllegalArgumentException("输出配置不能为空");
        }

        if (initialized) {
            throw new IllegalStateException("输出器已经初始化");
        }

        this.config = config;

        try {
            doInitialize(config);
            this.initialized = true;
            logger.debug("输出器初始化成功: {}", getType());
        } catch (Exception e) {
            logger.error("输出器初始化失败: {}", getType(), e);
            throw new IOException("初始化失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void writeRecord(Map<String, Object> record) throws IOException {
        checkState();

        if (record == null || record.isEmpty()) {
            logger.debug("跳过空记录");
            return;
        }

        try {
            doWriteRecord(record);
            recordCount.incrementAndGet();

            if (logger.isTraceEnabled()) {
                logger.trace("写入记录: {}", record);
            }
        } catch (Exception e) {
            logger.error("写入记录失败", e);
            throw new IOException("写入记录失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void writeRecords(List<Map<String, Object>> records) throws IOException {
        checkState();

        if (records == null || records.isEmpty()) {
            logger.debug("跳过空记录列表");
            return;
        }

        try {
            doWriteRecords(records);
            recordCount.addAndGet(records.size());

            logger.debug("批量写入{}条记录", records.size());
        } catch (Exception e) {
            logger.error("批量写入记录失败", e);
            throw new IOException("批量写入记录失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void writeHeader(List<String> headers) throws IOException {
        checkState();

        if (!supportsHeader()) {
            logger.debug("输出器不支持表头，跳过写入");
            return;
        }

        if (headers == null || headers.isEmpty()) {
            logger.debug("跳过空表头");
            return;
        }

        try {
            doWriteHeader(headers);
            logger.debug("写入表头: {}", headers);
        } catch (Exception e) {
            logger.error("写入表头失败", e);
            throw new IOException("写入表头失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void flush() throws IOException {
        if (!initialized || closed) {
            return;
        }

        try {
            doFlush();
            logger.trace("刷新输出缓冲区");
        } catch (Exception e) {
            logger.error("刷新输出缓冲区失败", e);
            throw new IOException("刷新失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void close() throws IOException {
        if (closed) {
            return;
        }

        try {
            if (initialized) {
                flush();
                doClose();
            }

            this.closed = true;
            logger.debug("输出器关闭: {}, 共写入{}条记录", getType(), recordCount.get());
        } catch (Exception e) {
            logger.error("关闭输出器失败: {}", getType(), e);
            throw new IOException("关闭失败: " + e.getMessage(), e);
        }
    }

    @Override
    public boolean isInitialized() {
        return initialized;
    }

    @Override
    public boolean isClosed() {
        return closed;
    }

    @Override
    public long getWrittenRecordCount() {
        return recordCount.get();
    }

    /**
     * 检查输出器状态
     * 
     * @throws IOException 当状态不正确时抛出
     */
    protected void checkState() throws IOException {
        if (!initialized) {
            throw new IOException("输出器未初始化");
        }

        if (closed) {
            throw new IOException("输出器已关闭");
        }
    }

    /**
     * 执行初始化
     * 子类需要实现此方法来完成具体的初始化工作
     * 
     * @param config 输出配置
     * @throws Exception 当初始化失败时抛出
     */
    protected abstract void doInitialize(OutputConfig config) throws Exception;

    /**
     * 执行单条记录写入
     * 子类需要实现此方法来完成具体的记录写入工作
     * 
     * @param record 记录数据
     * @throws Exception 当写入失败时抛出
     */
    protected abstract void doWriteRecord(Map<String, Object> record) throws Exception;

    /**
     * 执行批量记录写入
     * 默认实现是逐条调用doWriteRecord，子类可以重写以提供更高效的批量写入
     * 
     * @param records 记录列表
     * @throws Exception 当写入失败时抛出
     */
    protected void doWriteRecords(List<Map<String, Object>> records) throws Exception {
        for (Map<String, Object> record : records) {
            doWriteRecord(record);
        }
    }

    /**
     * 执行表头写入
     * 默认实现为空，子类可以重写以支持表头写入
     * 
     * @param headers 表头字段列表
     * @throws Exception 当写入失败时抛出
     */
    protected void doWriteHeader(List<String> headers) throws Exception {
        // 默认实现为空
    }

    /**
     * 执行刷新操作
     * 默认实现为空，子类可以重写以支持缓冲区刷新
     * 
     * @throws Exception 当刷新失败时抛出
     */
    protected void doFlush() throws Exception {
        // 默认实现为空
    }

    /**
     * 执行关闭操作
     * 默认实现为空，子类可以重写以释放资源
     * 
     * @throws Exception 当关闭失败时抛出
     */
    protected void doClose() throws Exception {
        // 默认实现为空
    }

    @Override
    public String toString() {
        return String.format("%s{type=%s, initialized=%s, closed=%s, recordCount=%d}",
                getClass().getSimpleName(), getType(), initialized, closed, recordCount.get());
    }
}