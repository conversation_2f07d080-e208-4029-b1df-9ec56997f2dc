package com.dataforge.core.stream;

import com.dataforge.core.model.GenerationContext;

import java.util.stream.Stream;

/**
 * 流式数据生成器接口
 * 
 * 扩展基础数据生成器，提供流式数据生成能力。
 * 支持大数据量的流式生成，避免内存溢出问题。
 * 
 * @param <T> 生成的数据类型
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface StreamDataGenerator<T> {

    /**
     * 生成指定数量的数据流
     * 
     * @param context 生成上下文
     * @param count   生成数量
     * @return 数据流
     */
    Stream<T> generateStream(GenerationContext context, long count);

    /**
     * 生成无限数据流
     * 
     * @param context 生成上下文
     * @return 无限数据流
     */
    Stream<T> generateInfiniteStream(GenerationContext context);

    /**
     * 生成批次数据流
     * 每个批次包含指定数量的数据
     * 
     * @param context    生成上下文
     * @param batchSize  批次大小
     * @param batchCount 批次数量
     * @return 批次数据流
     */
    Stream<Batch<T>> generateBatchStream(GenerationContext context, int batchSize, long batchCount);

    /**
     * 获取生成器类型
     * 
     * @return 生成器类型
     */
    String getType();

    /**
     * 检查是否支持并行流
     * 
     * @return 是否支持并行流
     */
    default boolean supportsParallelStream() {
        return true;
    }

    /**
     * 获取建议的批次大小
     * 
     * @return 建议的批次大小
     */
    default int getRecommendedBatchSize() {
        return 1000;
    }

    /**
     * 数据批次
     * 
     * @param <T> 数据类型
     */
    record Batch<T>(java.util.List<T> data, long batchNumber, long totalBatches) {

        /**
         * 获取批次大小
         * 
         * @return 批次大小
         */
        public int size() {
            return data.size();
        }

        /**
         * 检查是否为最后一个批次
         * 
         * @return 是否为最后一个批次
         */
        public boolean isLastBatch() {
            return batchNumber == totalBatches - 1;
        }

        /**
         * 获取批次进度百分比
         * 
         * @return 进度百分比 (0-100)
         */
        public double getProgress() {
            return totalBatches > 0 ? (batchNumber + 1) * 100.0 / totalBatches : 0;
        }
    }
}