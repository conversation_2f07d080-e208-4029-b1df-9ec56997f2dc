package examples;

import com.dataforge.core.output.OutputConfig;
import com.dataforge.core.output.OutputWriter;
import com.dataforge.core.output.OutputWriterFactory;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 输出系统演示程序
 * 
 * 演示如何使用OutputWriterFactory和各种OutputWriter来输出数据。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class OutputSystemDemo {

    public static void main(String[] args) {
        System.out.println("=== DataForge 输出系统演示 ===\n");

        // 创建输出器工厂
        OutputWriterFactory factory = new OutputWriterFactory();

        // 准备测试数据
        List<Map<String, Object>> testData = createTestData();
        List<String> headers = Arrays.asList("id", "name", "age", "email", "city");

        // 演示1: 控制台输出
        demonstrateConsoleOutput(factory, testData, headers);

        // 演示2: CSV输出
        demonstrateCsvOutput(factory, testData, headers);

        // 演示3: JSON输出
        demonstrateJsonOutput(factory, testData, headers);

        // 演示4: 输出器工厂功能
        demonstrateOutputFactory(factory);

        System.out.println("\n=== 演示完成 ===");
    }

    /**
     * 创建测试数据
     */
    private static List<Map<String, Object>> createTestData() {
        return Arrays.asList(
            createRecord(1, "张三", 25, "<EMAIL>", "北京"),
            createRecord(2, "李四", 30, "<EMAIL>", "上海"),
            createRecord(3, "王五", 28, "<EMAIL>", "广州"),
            createRecord(4, "赵六", 35, "<EMAIL>", "深圳"),
            createRecord(5, "钱七", 22, "<EMAIL>", "杭州")
        );
    }

    /**
     * 创建单条记录
     */
    private static Map<String, Object> createRecord(int id, String name, int age, String email, String city) {
        Map<String, Object> record = new HashMap<>();
        record.put("id", id);
        record.put("name", name);
        record.put("age", age);
        record.put("email", email);
        record.put("city", city);
        return record;
    }

    /**
     * 演示控制台输出
     */
    private static void demonstrateConsoleOutput(OutputWriterFactory factory, 
                                                List<Map<String, Object>> testData, 
                                                List<String> headers) {
        System.out.println("1. 控制台输出演示");
        System.out.println("----------------------------------------");

        try {
            // 创建控制台输出配置
            OutputConfig config = new OutputConfig();
            config.setFormat("console");
            config.setTarget("stdout");
            config.setIncludeHeader(true);
            config.addFormatOption("maxRowsPerPage", 10);
            config.addFormatOption("enablePaging", false);

            // 创建并初始化输出器
            OutputWriter writer = factory.createAndInitialize(config);

            System.out.println("输出器类型: " + writer.getType());
            System.out.println("输出器描述: " + writer.getDescription());
            System.out.println();

            // 写入表头
            writer.writeHeader(headers);

            // 写入数据
            writer.writeRecords(testData);

            // 刷新和关闭
            writer.flush();
            writer.close();

            System.out.println("共写入 " + writer.getWrittenRecordCount() + " 条记录");

        } catch (Exception e) {
            System.err.println("控制台输出演示失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println();
    }

    /**
     * 演示CSV输出
     */
    private static void demonstrateCsvOutput(OutputWriterFactory factory, 
                                           List<Map<String, Object>> testData, 
                                           List<String> headers) {
        System.out.println("2. CSV输出演示");
        System.out.println("----------------------------------------");

        try {
            // 创建CSV输出配置
            OutputConfig config = new OutputConfig();
            config.setFormat("csv");
            config.setTarget("output_demo.csv");
            config.setIncludeHeader(true);
            config.setEncoding("UTF-8");
            config.addFormatOption("delimiter", ",");
            config.addFormatOption("quote", "\"");
            config.addFormatOption("alwaysQuote", false);

            // 创建并初始化输出器
            OutputWriter writer = factory.createAndInitialize(config);

            System.out.println("输出器类型: " + writer.getType());
            System.out.println("输出目标: " + config.getTarget());

            // 写入表头
            writer.writeHeader(headers);

            // 逐条写入数据
            for (Map<String, Object> record : testData) {
                writer.writeRecord(record);
            }

            // 刷新和关闭
            writer.flush();
            writer.close();

            System.out.println("CSV文件生成成功，共写入 " + writer.getWrittenRecordCount() + " 条记录");

        } catch (Exception e) {
            System.err.println("CSV输出演示失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println();
    }

    /**
     * 演示JSON输出
     */
    private static void demonstrateJsonOutput(OutputWriterFactory factory, 
                                            List<Map<String, Object>> testData, 
                                            List<String> headers) {
        System.out.println("3. JSON输出演示");
        System.out.println("----------------------------------------");

        try {
            // 创建JSON输出配置
            OutputConfig config = new OutputConfig();
            config.setFormat("json");
            config.setTarget("output_demo.json");
            config.setEncoding("UTF-8");
            config.addFormatOption("prettyPrint", true);
            config.addFormatOption("arrayFormat", true);

            // 创建并初始化输出器
            OutputWriter writer = factory.createAndInitialize(config);

            System.out.println("输出器类型: " + writer.getType());
            System.out.println("输出目标: " + config.getTarget());

            // 写入表头（JSON不需要单独的表头）
            writer.writeHeader(headers);

            // 批量写入数据
            writer.writeRecords(testData);

            // 刷新和关闭
            writer.flush();
            writer.close();

            System.out.println("JSON文件生成成功，共写入 " + writer.getWrittenRecordCount() + " 条记录");

        } catch (Exception e) {
            System.err.println("JSON输出演示失败: " + e.getMessage());
            e.printStackTrace();
        }

        System.out.println();
    }

    /**
     * 演示输出器工厂功能
     */
    private static void demonstrateOutputFactory(OutputWriterFactory factory) {
        System.out.println("4. 输出器工厂功能演示");
        System.out.println("----------------------------------------");

        // 显示已注册的输出器
        System.out.println("已注册的输出格式:");
        for (String format : factory.getRegisteredFormats()) {
            System.out.println("  - " + format);
        }

        System.out.println("\n输出器数量: " + factory.getWriterCount());

        // 测试格式检测
        System.out.println("\n格式检测测试:");
        testFormatDetection(factory, "test.csv");
        testFormatDetection(factory, "test.json");
        testFormatDetection(factory, "test.txt");
        testFormatDetection(factory, "stdout");

        // 测试输出器创建
        System.out.println("\n输出器创建测试:");
        testWriterCreation(factory, "console");
        testWriterCreation(factory, "csv");
        testWriterCreation(factory, "json");
        testWriterCreation(factory, "unknown");

        System.out.println();
    }

    /**
     * 测试格式检测
     */
    private static void testFormatDetection(OutputWriterFactory factory, String target) {
        try {
            OutputConfig config = new OutputConfig();
            config.setTarget(target);
            
            OutputWriter writer = factory.createAndInitialize(config);
            System.out.println("  " + target + " -> " + writer.getType());
            writer.close();
        } catch (Exception e) {
            System.out.println("  " + target + " -> 检测失败: " + e.getMessage());
        }
    }

    /**
     * 测试输出器创建
     */
    private static void testWriterCreation(OutputWriterFactory factory, String format) {
        OutputWriter writer = factory.createWriter(format);
        if (writer != null) {
            System.out.println("  " + format + " -> " + writer.getClass().getSimpleName() + " (成功)");
        } else {
            System.out.println("  " + format + " -> 创建失败");
        }
    }
}