package com.dataforge.cli;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;

/**
 * DataForge CLI应用程序入口点
 * 
 * 这是DataForge命令行工具的主入口类，负责启动Spring Boot应用程序
 * 并处理命令行参数。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@SpringBootApplication
@ComponentScan(basePackages = { "com.dataforge.cli", "com.dataforge.core", "com.dataforge.generators" })
public class DataForgeApplication implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataForgeApplication.class);

    private final CommandProcessor commandProcessor;

    /**
     * 构造函数
     * 
     * @param commandProcessor 命令处理器
     */
    public DataForgeApplication(CommandProcessor commandProcessor) {
        this.commandProcessor = commandProcessor;
    }

    /**
     * 应用程序主入口点
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        try {
            // 禁用Spring Boot的banner
            System.setProperty("spring.main.banner-mode", "off");

            // 设置日志级别
            if (!isDebugMode(args)) {
                System.setProperty("logging.level.root", "WARN");
                System.setProperty("logging.level.com.dataforge", "INFO");
            }

            // 启动Spring Boot应用程序
            SpringApplication app = new SpringApplication(DataForgeApplication.class);
            app.setLogStartupInfo(false);

            int exitCode = SpringApplication.exit(app.run(args));
            System.exit(exitCode);

        } catch (Exception e) {
            logger.error("应用程序启动失败", e);
            System.err.println("启动失败: " + e.getMessage());
            System.exit(1);
        }
    }

    /**
     * 命令行运行器实现
     * 
     * @param args 命令行参数
     */
    @Override
    public void run(String... args) {
        try {
            logger.debug("开始处理命令行参数: {}", String.join(" ", args));

            // 处理命令并获取退出代码
            int exitCode = commandProcessor.processCommand(args);

            logger.debug("命令处理完成，退出代码: {}", exitCode);

            // 设置退出代码
            System.exit(exitCode);

        } catch (Exception e) {
            logger.error("命令处理失败", e);
            System.err.println("执行失败: " + e.getMessage());
            System.exit(1);
        }
    }

    /**
     * 检查是否启用调试模式
     * 
     * @param args 命令行参数
     * @return 如果启用调试模式返回true
     */
    private static boolean isDebugMode(String[] args) {
        for (String arg : args) {
            if ("--debug".equals(arg) || "--verbose".equals(arg)) {
                return true;
            }
        }
        return false;
    }
}