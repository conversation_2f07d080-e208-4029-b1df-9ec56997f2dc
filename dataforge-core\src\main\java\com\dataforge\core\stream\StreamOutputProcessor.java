package com.dataforge.core.stream;

import com.dataforge.core.output.OutputConfig;
import com.dataforge.core.output.OutputWriter;
import com.dataforge.core.output.OutputWriterFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

/**
 * 流式输出处理器
 * 
 * 处理流式数据的输出，支持大数据量的流式写入，
 * 避免内存溢出问题。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class StreamOutputProcessor {

    private static final Logger logger = LoggerFactory.getLogger(StreamOutputProcessor.class);

    private final OutputWriterFactory outputWriterFactory;
    private final int defaultBatchSize;

    /**
     * 构造函数
     * 
     * @param outputWriterFactory 输出写入器工厂
     */
    public StreamOutputProcessor(OutputWriterFactory outputWriterFactory) {
        this.outputWriterFactory = outputWriterFactory;
        this.defaultBatchSize = 1000;
    }

    /**
     * 处理数据流输出
     * 
     * @param dataStream   数据流
     * @param fieldNames   字段名称列表
     * @param outputConfig 输出配置
     * @param <T>          数据类型
     * @return 处理结果
     * @throws IOException 如果输出过程中出现IO异常
     */
    public <T> StreamOutputResult processStream(Stream<T> dataStream,
            List<String> fieldNames,
            OutputConfig outputConfig) throws IOException {

        logger.info("Starting stream output processing: format={}, target={}",
                outputConfig.getFormat(), outputConfig.getTarget());

        OutputWriter writer = outputWriterFactory.createWriter(outputConfig.getFormat());
        if (writer == null) {
            throw new IllegalArgumentException("Unsupported output format: " + outputConfig.getFormat());
        }

        AtomicLong processedCount = new AtomicLong(0);
        long startTime = System.currentTimeMillis();

        try {
            // 初始化输出器
            writer.initialize(outputConfig);

            // 写入表头（如果支持）
            if (writer.supportsHeader() && fieldNames != null && !fieldNames.isEmpty()) {
                writer.writeHeader(fieldNames);
            }

            // 将数据流转换为Map流以适配OutputWriter接口
            Stream<Map<String, Object>> mapStream = dataStream.map(item -> convertToMap(item, fieldNames));

            // 使用批次处理来优化内存使用
            processBatchedStream(mapStream, writer, outputConfig, processedCount);

            // 刷新并关闭
            writer.flush();
            writer.close();

        } catch (Exception e) {
            logger.error("Error during stream output processing", e);
            throw new IOException("Stream output processing failed", e);
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        long count = processedCount.get();

        logger.info("Stream output processing completed: processed={}, duration={}ms, throughput={}/s",
                count, duration, duration > 0 ? count * 1000 / duration : 0);

        return new StreamOutputResult(count, duration, true, null);
    }

    /**
     * 处理批次数据流输出
     * 
     * @param batchStream  批次数据流
     * @param outputConfig 输出配置
     * @param <T>          数据类型
     * @return 处理结果
     * @throws IOException 如果输出过程中出现IO异常
     */
    public <T> StreamOutputResult processBatchStream(Stream<StreamDataGenerator.Batch<T>> batchStream,
            OutputConfig outputConfig) throws IOException {

        logger.info("Starting batch stream output processing: format={}, target={}",
                outputConfig.getFormat(), outputConfig.getTarget());

        OutputWriter writer = outputWriterFactory.createWriter(outputConfig.getFormat());
        if (writer == null) {
            throw new IllegalArgumentException("Unsupported output format: " + outputConfig.getFormat());
        }

        AtomicLong processedCount = new AtomicLong(0);
        AtomicLong batchCount = new AtomicLong(0);
        long startTime = System.currentTimeMillis();

        try {
            // 初始化输出器
            writer.initialize(outputConfig);

            // 写入表头（如果支持）
            if (writer.supportsHeader()) {
                writer.writeHeader(List.of("data"));
            }

            batchStream.forEach(batch -> {
                try {
                    // 将批次数据转换为Map列表
                    List<Map<String, Object>> records = batch.data().stream()
                            .map(item -> convertToMap(item, List.of("data")))
                            .toList();

                    // 批量写入数据
                    writer.writeRecords(records);

                    long currentBatch = batchCount.incrementAndGet();
                    long currentCount = processedCount.addAndGet(batch.size());

                    if (currentBatch % 10 == 0) {
                        logger.debug("Processed batch {}/{}, total items: {}, progress: {:.1f}%",
                                currentBatch, batch.totalBatches(), currentCount, batch.getProgress());
                    }

                } catch (IOException e) {
                    logger.error("Error processing batch {}", batch.batchNumber(), e);
                    throw new RuntimeException("Batch processing failed", e);
                }
            });

            // 刷新并关闭
            writer.flush();
            writer.close();

        } catch (Exception e) {
            logger.error("Error during batch stream output processing", e);
            throw new IOException("Batch stream output processing failed", e);
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;
        long count = processedCount.get();

        logger.info("Batch stream output processing completed: batches={}, items={}, duration={}ms",
                batchCount.get(), count, duration);

        return new StreamOutputResult(count, duration, true, null);
    }

    /**
     * 处理带进度回调的流式输出
     * 
     * @param dataStream       数据流
     * @param fieldNames       字段名称列表
     * @param outputConfig     输出配置
     * @param progressCallback 进度回调
     * @param <T>              数据类型
     * @return 处理结果
     * @throws IOException 如果输出过程中出现IO异常
     */
    public <T> StreamOutputResult processStreamWithProgress(Stream<T> dataStream,
            List<String> fieldNames,
            OutputConfig outputConfig,
            ProgressCallback progressCallback) throws IOException {

        logger.info("Starting stream output processing with progress tracking");

        AtomicLong processedCount = new AtomicLong(0);
        long startTime = System.currentTimeMillis();

        // 添加进度跟踪
        Stream<T> progressStream = dataStream.peek(item -> {
            long current = processedCount.incrementAndGet();
            if (progressCallback != null && current % 1000 == 0) {
                progressCallback.onProgress(current, System.currentTimeMillis() - startTime);
            }
        });

        return processStream(progressStream, fieldNames, outputConfig);
    }

    /**
     * 批次处理流数据
     */
    private void processBatchedStream(Stream<Map<String, Object>> mapStream,
            OutputWriter writer,
            OutputConfig outputConfig,
            AtomicLong processedCount) throws IOException {

        // 将流分批处理以优化内存使用
        mapStream
                .peek(item -> processedCount.incrementAndGet())
                .forEach(item -> {
                    try {
                        // 逐条写入记录
                        writer.writeRecord(item);
                    } catch (IOException e) {
                        throw new RuntimeException("Failed to write item", e);
                    }
                });
    }

    /**
     * 将数据项转换为Map
     */
    private <T> Map<String, Object> convertToMap(T item, List<String> fieldNames) {
        if (fieldNames.size() == 1) {
            return Map.of(fieldNames.get(0), item);
        }

        // 对于复杂对象，可以使用反射或其他方式转换
        // 这里简化处理
        return Map.of("data", item);
    }

    /**
     * 获取默认批次大小
     * 
     * @return 默认批次大小
     */
    public int getDefaultBatchSize() {
        return defaultBatchSize;
    }

    /**
     * 流式输出结果
     */
    public static class StreamOutputResult {
        private final long processedCount;
        private final long duration;
        private final boolean success;
        private final String errorMessage;

        public StreamOutputResult(long processedCount, long duration, boolean success, String errorMessage) {
            this.processedCount = processedCount;
            this.duration = duration;
            this.success = success;
            this.errorMessage = errorMessage;
        }

        public long getProcessedCount() {
            return processedCount;
        }

        public long getDuration() {
            return duration;
        }

        public boolean isSuccess() {
            return success;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        public double getThroughput() {
            return duration > 0 ? processedCount * 1000.0 / duration : 0;
        }

        @Override
        public String toString() {
            return String.format("StreamOutputResult{processed=%d, duration=%dms, success=%s, throughput=%.1f/s}",
                    processedCount, duration, success, getThroughput());
        }
    }

    /**
     * 进度回调接口
     */
    @FunctionalInterface
    public interface ProgressCallback {
        /**
         * 进度更新回调
         * 
         * @param processedCount 已处理数量
         * @param elapsedTime    已用时间（毫秒）
         */
        void onProgress(long processedCount, long elapsedTime);
    }
}