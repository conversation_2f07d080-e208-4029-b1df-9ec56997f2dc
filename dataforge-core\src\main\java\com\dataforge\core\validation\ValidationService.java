package com.dataforge.core.validation;

import com.dataforge.core.model.ValidationConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 数据校验服务
 * 
 * 统一管理数据校验规则和执行校验任务。
 * 支持单条和批量数据校验，提供并行校验能力。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class ValidationService {

    private final Logger logger;

    private final Map<String, ValidationRule> validationRules;
    private final ExecutorService executorService;
    private final ValidationRuleFactory ruleFactory;

    /**
     * 构造函数
     */
    public ValidationService() {
        this.validationRules = new ConcurrentHashMap<>();
        this.executorService = Executors.newCachedThreadPool(r -> {
            Thread thread = new Thread(r, "ValidationService-" + System.currentTimeMillis());
            thread.setDaemon(true);
            return thread;
        });
        this.ruleFactory = new ValidationRuleFactory();
        
        // 延迟初始化logger以避免Logback兼容性问题
        this.logger = null;
        
        initializeDefaultRules();
    }

    /**
     * 注册校验规则
     * 
     * @param rule 校验规则
     */
    public void registerValidationRule(ValidationRule rule) {
        if (rule == null) {
            if (logger != null) {
                logger.warn("尝试注册空的校验规则");
            }
            return;
        }

        validationRules.put(rule.getRuleName(), rule);

        if (logger != null && logger.isDebugEnabled()) {
            logger.debug("注册校验规则: {}", rule.getRuleName());
        }
    }

    /**
     * 移除校验规则
     * 
     * @param ruleName 规则名称
     * @return 被移除的规则，如果不存在返回null
     */
    public ValidationRule removeValidationRule(String ruleName) {
        ValidationRule removed = validationRules.remove(ruleName);

        if (removed != null && logger != null && logger.isDebugEnabled()) {
            logger.debug("移除校验规则: {}", ruleName);
        }

        return removed;
    }

    /**
     * 校验单条数据
     * 
     * @param dataType 数据类型
     * @param value    数据值
     * @return 校验结果
     */
    public ValidationResult validateSingle(String dataType, Object value) {
        if (dataType == null || dataType.trim().isEmpty()) {
            return ValidationResult.failure("数据类型不能为空");
        }

        ValidationRule rule = validationRules.get(dataType);
        if (rule == null) {
            return ValidationResult.success("未找到对应的校验规则，跳过校验");
        }

        try {
            return rule.validate(value);
        } catch (Exception e) {
            if (logger != null) {
                logger.error("校验数据时发生异常: dataType={}, value={}", dataType, value, e);
            }
            return ValidationResult.failure("校验过程中发生异常: " + e.getMessage());
        }
    }

    /**
     * 批量校验数据
     * 
     * @param dataType 数据类型
     * @param values   数据值列表
     * @return 批量校验结果
     */
    public BatchValidationResult validateBatch(String dataType, List<Object> values) {
        if (values == null || values.isEmpty()) {
            return BatchValidationResult.empty("没有数据需要校验");
        }

        ValidationRule rule = validationRules.get(dataType);
        if (rule == null) {
            return BatchValidationResult.empty("未找到对应的校验规则: " + dataType);
        }

        List<ValidationResult> results = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        for (int i = 0; i < values.size(); i++) {
            Object value = values.get(i);
            try {
                ValidationResult result = rule.validate(value);
                results.add(result);

                if (result.isValid()) {
                    successCount++;
                } else {
                    failureCount++;
                }
            } catch (Exception e) {
                if (logger != null) {
                    logger.error("校验第{}条数据时发生异常: value={}", i + 1, value, e);
                }
                ValidationResult errorResult = ValidationResult.failure("校验异常: " + e.getMessage());
                results.add(errorResult);
                failureCount++;
            }
        }

        return new BatchValidationResult(results, successCount, failureCount);
    }

    /**
     * 并行批量校验数据
     * 
     * @param dataType 数据类型
     * @param values   数据值列表
     * @return 批量校验结果的Future
     */
    public CompletableFuture<BatchValidationResult> validateBatchAsync(String dataType, List<Object> values) {
        return CompletableFuture.supplyAsync(() -> validateBatch(dataType, values), executorService);
    }

    /**
     * 校验记录数据
     * 
     * @param recordData 记录数据
     * @return 记录校验结果
     */
    public RecordValidationResult validateRecord(Map<String, Object> recordData) {
        if (recordData == null || recordData.isEmpty()) {
            return RecordValidationResult.empty("没有数据需要校验");
        }

        Map<String, ValidationResult> fieldResults = new HashMap<>();
        int validFieldCount = 0;
        int invalidFieldCount = 0;

        for (Map.Entry<String, Object> entry : recordData.entrySet()) {
            String fieldName = entry.getKey();
            Object fieldValue = entry.getValue();

            ValidationResult result = validateSingle(fieldName, fieldValue);
            fieldResults.put(fieldName, result);

            if (result.isValid()) {
                validFieldCount++;
            } else {
                invalidFieldCount++;
            }
        }

        return new RecordValidationResult(fieldResults, validFieldCount, invalidFieldCount);
    }

    /**
     * 并行校验多条记录
     * 
     * @param records 记录列表
     * @return 记录校验结果列表的Future
     */
    public CompletableFuture<List<RecordValidationResult>> validateRecordsAsync(List<Map<String, Object>> records) {
        if (records == null || records.isEmpty()) {
            return CompletableFuture.completedFuture(Collections.emptyList());
        }

        List<CompletableFuture<RecordValidationResult>> futures = new ArrayList<>();

        for (Map<String, Object> record : records) {
            CompletableFuture<RecordValidationResult> future = CompletableFuture
                    .supplyAsync(() -> validateRecord(record), executorService);
            futures.add(future);
        }

        return CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                .thenApply(v -> futures.stream()
                        .map(CompletableFuture::join)
                        .toList());
    }

    /**
     * 获取校验规则
     * 
     * @param ruleName 规则名称
     * @return 校验规则，如果不存在返回null
     */
    public ValidationRule getValidationRule(String ruleName) {
        return validationRules.get(ruleName);
    }

    /**
     * 获取所有校验规则
     * 
     * @return 校验规则映射的副本
     */
    public Map<String, ValidationRule> getAllValidationRules() {
        return new HashMap<>(validationRules);
    }

    /**
     * 检查是否存在校验规则
     * 
     * @param ruleName 规则名称
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasValidationRule(String ruleName) {
        return validationRules.containsKey(ruleName);
    }

    /**
     * 获取校验规则数量
     * 
     * @return 规则数量
     */
    public int getRuleCount() {
        return validationRules.size();
    }

    /**
     * 检查是否为空
     * 
     * @return 如果没有规则返回true，否则返回false
     */
    public boolean isEmpty() {
        return validationRules.isEmpty();
    }

    /**
     * 生成校验统计报告
     * 
     * @param results 校验结果列表
     * @return 统计报告
     */
    public ValidationStatistics generateStatistics(List<ValidationResult> results) {
        if (results == null || results.isEmpty()) {
            return ValidationStatistics.empty();
        }

        int totalCount = results.size();
        int validCount = 0;
        int invalidCount = 0;
        Map<String, Integer> errorTypeCount = new HashMap<>();

        for (ValidationResult result : results) {
            if (result.isValid()) {
                validCount++;
            } else {
                invalidCount++;

                // 统计错误类型
                String errorMessage = result.getErrorMessage();
                if (errorMessage != null) {
                    String errorType = extractErrorType(errorMessage);
                    errorTypeCount.put(errorType, errorTypeCount.getOrDefault(errorType, 0) + 1);
                }
            }
        }

        return new ValidationStatistics(totalCount, validCount, invalidCount, errorTypeCount);
    }

    /**
     * 关闭服务
     */
    public void shutdown() {
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            if (logger != null) {
                logger.info("ValidationService已关闭");
            }
        }
    }

    /**
     * 初始化默认校验规则
     */
    private void initializeDefaultRules() {
        // 注册身份证号校验规则
        registerValidationRule(ruleFactory.createIdCardValidationRule());

        // 注册银行卡号校验规则
        registerValidationRule(ruleFactory.createBankCardValidationRule());

        // 注册统一社会信用代码校验规则
        registerValidationRule(ruleFactory.createUSCCValidationRule());

        // 注册邮箱校验规则
        registerValidationRule(ruleFactory.createEmailValidationRule());

        // 注册手机号校验规则
        registerValidationRule(ruleFactory.createPhoneValidationRule());

        if (logger != null) {
            logger.info("已初始化 {} 个默认校验规则", validationRules.size());
        }
    }

    /**
     * 提取错误类型
     * 
     * @param errorMessage 错误消息
     * @return 错误类型
     */
    private String extractErrorType(String errorMessage) {
        if (errorMessage == null || errorMessage.isEmpty()) {
            return "未知错误";
        }

        // 简单的错误类型提取逻辑
        if (errorMessage.contains("格式")) {
            return "格式错误";
        } else if (errorMessage.contains("长度")) {
            return "长度错误";
        } else if (errorMessage.contains("校验")) {
            return "校验错误";
        } else if (errorMessage.contains("为空")) {
            return "空值错误";
        } else {
            return "其他错误";
        }
    }

    /**
     * 校验身份证号
     * 
     * @param idCard 身份证号
     * @return 校验结果
     */
    public ValidationResult validateIdCard(String idCard) {
        if (idCard == null || idCard.trim().isEmpty()) {
            return ValidationResult.failure("身份证号不能为空");
        }

        // 移除空格
        idCard = idCard.trim();

        // 检查是否包含非数字和X字符
        if (!idCard.matches("^[0-9Xx]+$")) {
            return ValidationResult.failure("身份证号格式不正确");
        }

        // 检查长度
        if (idCard.length() != 18) {
            return ValidationResult.failure("身份证号长度必须为18位");
        }

        // 检查格式
        if (!idCard.matches("^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$")) {
            return ValidationResult.failure("身份证号格式不正确");
        }

        // 校验位验证（简化实现）
        try {
            String checkCode = "10X98765432";
            int sum = 0;
            for (int i = 0; i < 17; i++) {
                sum += Integer.parseInt(idCard.substring(i, i + 1)) * (Math.pow(2, 17 - i) % 11);
            }
            char expectedCheck = checkCode.charAt(sum % 11);
            char actualCheck = Character.toUpperCase(idCard.charAt(17));
            
            if (expectedCheck != actualCheck) {
                return ValidationResult.failure("校验位错误");
            }
        } catch (NumberFormatException e) {
            return ValidationResult.failure("身份证号包含无效字符");
        }

        return ValidationResult.success("身份证号校验通过");
    }

    /**
     * 校验银行卡号
     * 
     * @param bankCard 银行卡号
     * @return 校验结果
     */
    public ValidationResult validateBankCard(String bankCard) {
        if (bankCard == null || bankCard.trim().isEmpty()) {
            return ValidationResult.failure("银行卡号不能为空");
        }

        // 移除空格和连字符
        bankCard = bankCard.replaceAll("[\\s-]", "");

        // 检查长度
        if (bankCard.length() < 13 || bankCard.length() > 19) {
            return ValidationResult.failure("银行卡号长度不正确");
        }

        // 检查是否只包含数字
        if (!bankCard.matches("^\\d+$")) {
            return ValidationResult.failure("银行卡号只能包含数字");
        }

        // Luhn算法校验
        int sum = 0;
        boolean alternate = false;
        
        for (int i = bankCard.length() - 1; i >= 0; i--) {
            int n = Integer.parseInt(bankCard.substring(i, i + 1));
            if (alternate) {
                n *= 2;
                if (n > 9) {
                    n = (n % 10) + 1;
                }
            }
            sum += n;
            alternate = !alternate;
        }
        
        if (sum % 10 != 0) {
            return ValidationResult.failure("Luhn算法校验失败");
        }

        return ValidationResult.success("银行卡号校验通过");
    }

    /**
     * 校验统一社会信用代码
     * 
     * @param uscc 统一社会信用代码
     * @return 校验结果
     */
    public ValidationResult validateUSCC(String uscc) {
        if (uscc == null || uscc.trim().isEmpty()) {
            return ValidationResult.failure("统一社会信用代码不能为空");
        }

        uscc = uscc.trim().toUpperCase();

        // 检查长度
        if (uscc.length() != 18) {
            return ValidationResult.failure("长度不正确");
        }

        // 检查是否包含无效字符
        if (uscc.contains("I") || uscc.contains("O") || uscc.contains("Z")) {
            return ValidationResult.failure("包含无效字符");
        }

        // 检查格式
        if (!uscc.matches("^[0-9A-HJ-NPQRTUWXY]{2}\\d{6}[0-9A-HJ-NPQRTUWXY]{10}$")) {
            return ValidationResult.failure("统一社会信用代码格式不正确");
        }

        // 校验位验证（简化实现）
        try {
            String checkCode = "0123456789ABCDEFGHJKLMNPQRTUWXY";
            int sum = 0;
            for (int i = 0; i < 17; i++) {
                int value = checkCode.indexOf(uscc.charAt(i));
                if (value == -1) {
                    return ValidationResult.failure("包含无效字符");
                }
                sum += value * (Math.pow(3, 17 - i) % 31);
            }
            char expectedCheck = checkCode.charAt((31 - sum % 31) % 31);
            char actualCheck = uscc.charAt(17);
            
            if (expectedCheck != actualCheck) {
                return ValidationResult.failure("校验位错误");
            }
        } catch (Exception e) {
            return ValidationResult.failure("校验过程中发生错误");
        }

        return ValidationResult.success("统一社会信用代码校验通过");
    }

    /**
     * 校验手机号
     * 
     * @param phone 手机号
     * @return 校验结果
     */
    public ValidationResult validatePhone(String phone) {
        if (phone == null || phone.trim().isEmpty()) {
            return ValidationResult.failure("手机号不能为空");
        }

        phone = phone.trim();

        // 检查格式
        if (!phone.matches("^1[3-9]\\d{9}$")) {
            return ValidationResult.failure("手机号格式不正确");
        }

        return ValidationResult.success("手机号校验通过");
    }

    /**
     * 校验邮箱地址
     * 
     * @param email 邮箱地址
     * @return 校验结果
     */
    public ValidationResult validateEmail(String email) {
        if (email == null || email.trim().isEmpty()) {
            return ValidationResult.failure("邮箱地址不能为空");
        }

        email = email.trim();

        // 检查格式
        if (!email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$")) {
            return ValidationResult.failure("邮箱格式不正确");
        }

        return ValidationResult.success("邮箱地址校验通过");
    }

    /**
     * 添加自定义校验规则
     * 
     * @param ruleName 规则名称
     * @param validator 校验函数
     */
    public void addCustomRule(String ruleName, java.util.function.Function<Object, ValidationResult> validator) {
        ValidationRule customRule = new ValidationRule() {
            @Override
            public ValidationResult validate(Object value) {
                return validator.apply(value);
            }

            @Override
            public String getRuleName() {
                return ruleName;
            }

            @Override
            public String getDescription() {
                return "自定义校验规则: " + ruleName;
            }

            @Override
            public String getSupportedDataType() {
                return ruleName;
            }
        };
        
        registerValidationRule(customRule);
    }

    /**
     * 批量校验数据（重载方法，支持ValidationConfig）
     * 
     * @param dataList 数据列表
     * @param config 校验配置
     * @return 批量校验结果
     */
    public BatchValidationResult validateBatch(List<Map<String, Object>> dataList, ValidationConfig config) {
        if (dataList == null || dataList.isEmpty()) {
            return BatchValidationResult.empty("没有数据需要校验");
        }

        if (config != null && !config.isEnabled()) {
            // 如果校验被禁用，返回所有数据都有效的结果
            List<ValidationResult> results = dataList.stream()
                    .map(record -> ValidationResult.success("校验已禁用"))
                    .toList();
            return new BatchValidationResult(results, dataList.size(), 0);
        }

        List<ValidationResult> results = new ArrayList<>();
        int successCount = 0;
        int failureCount = 0;

        for (Map<String, Object> record : dataList) {
            RecordValidationResult recordResult = validateRecord(record);
            results.add(recordResult.isRecordValid() ? 
                    ValidationResult.success("记录校验通过") : 
                    ValidationResult.failure("记录校验失败"));

            if (recordResult.isRecordValid()) {
                successCount++;
            } else {
                failureCount++;
            }
        }

        return new BatchValidationResult(results, successCount, failureCount);
    }

    @Override
    public String toString() {
        return String.format("ValidationService{rules=%d, executor=%s}",
                validationRules.size(), executorService.isShutdown() ? "shutdown" : "active");
    }
}