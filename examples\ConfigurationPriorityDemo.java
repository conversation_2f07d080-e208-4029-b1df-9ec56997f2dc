package examples;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.service.ConfigurationManager;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 配置优先级管理演示
 * 
 * 演示DataForge配置系统的优先级管理功能：
 * 1. CLI参数（最高优先级）
 * 2. 配置文件参数
 * 3. 默认参数（最低优先级）
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ConfigurationPriorityDemo {

    public static void main(String[] args) {
        ConfigurationManager configManager = new ConfigurationManager();
        
        System.out.println("=== DataForge 配置优先级管理演示 ===\n");
        
        // 1. 演示默认配置
        demonstrateDefaultConfiguration(configManager);
        
        // 2. 演示CLI参数优先级
        demonstrateCLIPriority(configManager);
        
        // 3. 演示配置文件与CLI参数的优先级
        demonstrateConfigFilePriority(configManager);
        
        // 4. 演示完整的优先级管理
        demonstrateFullPriorityManagement(configManager);
    }

    /**
     * 演示默认配置
     */
    private static void demonstrateDefaultConfiguration(ConfigurationManager configManager) {
        System.out.println("1. 默认配置演示");
        System.out.println("================");
        
        GenerationConfig defaultConfig = configManager.createDefaultConfiguration();
        
        System.out.println("默认配置内容：");
        System.out.println("  - 生成数量: " + defaultConfig.getCount());
        System.out.println("  - 输出格式: " + defaultConfig.getOutputConfig().getFormat());
        System.out.println("  - 编码格式: " + defaultConfig.getOutputConfig().getEncoding());
        System.out.println("  - 校验启用: " + defaultConfig.getValidationConfig().isEnabled());
        System.out.println("  - 严格模式: " + defaultConfig.getValidationConfig().isStrictMode());
        System.out.println("  - 并行处理: " + defaultConfig.getPerformanceConfig().isEnableParallel());
        System.out.println("  - 线程池大小: " + defaultConfig.getPerformanceConfig().getThreadPoolSize());
        System.out.println();
    }

    /**
     * 演示CLI参数优先级
     */
    private static void demonstrateCLIPriority(ConfigurationManager configManager) {
        System.out.println("2. CLI参数优先级演示");
        System.out.println("===================");
        
        // 模拟CLI参数
        Map<String, Object> cliParams = new HashMap<>();
        cliParams.put("dataType", "name");
        cliParams.put("count", 50);
        cliParams.put("output.format", "json");
        cliParams.put("output.file", "output.json");
        cliParams.put("validation.enabled", false);
        cliParams.put("performance.threads", 8);
        cliParams.put("name.type", "CN");
        cliParams.put("name.gender", "MALE");
        
        System.out.println("CLI参数：");
        cliParams.forEach((key, value) -> 
            System.out.println("  --" + key + "=" + value));
        
        GenerationConfig cliConfig = configManager.createConfigurationFromCLI(cliParams);
        
        System.out.println("\n从CLI参数创建的配置：");
        System.out.println("  - 数据类型: " + cliConfig.getDataType());
        System.out.println("  - 生成数量: " + cliConfig.getCount());
        System.out.println("  - 输出格式: " + cliConfig.getOutputConfig().getFormat());
        System.out.println("  - 输出文件: " + cliConfig.getOutputConfig().getFile());
        System.out.println("  - 校验启用: " + cliConfig.getValidationConfig().isEnabled());
        System.out.println("  - 线程池大小: " + cliConfig.getPerformanceConfig().getThreadPoolSize());
        System.out.println("  - 姓名类型: " + cliConfig.getParameters().get("name.type"));
        System.out.println("  - 性别: " + cliConfig.getParameters().get("name.gender"));
        System.out.println();
    }

    /**
     * 演示配置文件与CLI参数的优先级
     */
    private static void demonstrateConfigFilePriority(ConfigurationManager configManager) {
        System.out.println("3. 配置文件与CLI参数优先级演示");
        System.out.println("===============================");
        
        // 模拟配置文件内容
        GenerationConfig fileConfig = new GenerationConfig("phone", 20);
        fileConfig.addParameter("phone.prefix", "138,139");
        fileConfig.getOutputConfig().setFormat("csv");
        fileConfig.getOutputConfig().setFile("phones.csv");
        fileConfig.getOutputConfig().setEncoding("GBK");
        fileConfig.getValidationConfig().setEnabled(true);
        fileConfig.getValidationConfig().setStrictMode(true);
        fileConfig.getPerformanceConfig().setThreadPoolSize(4);
        fileConfig.getPerformanceConfig().setEnableParallel(false);
        
        System.out.println("配置文件内容：");
        System.out.println("  - 数据类型: " + fileConfig.getDataType());
        System.out.println("  - 生成数量: " + fileConfig.getCount());
        System.out.println("  - 输出格式: " + fileConfig.getOutputConfig().getFormat());
        System.out.println("  - 输出文件: " + fileConfig.getOutputConfig().getFile());
        System.out.println("  - 编码格式: " + fileConfig.getOutputConfig().getEncoding());
        System.out.println("  - 校验启用: " + fileConfig.getValidationConfig().isEnabled());
        System.out.println("  - 严格模式: " + fileConfig.getValidationConfig().isStrictMode());
        System.out.println("  - 线程池大小: " + fileConfig.getPerformanceConfig().getThreadPoolSize());
        System.out.println("  - 并行处理: " + fileConfig.getPerformanceConfig().isEnableParallel());
        System.out.println("  - 手机号前缀: " + fileConfig.getParameters().get("phone.prefix"));
        
        // CLI参数（部分覆盖）
        Map<String, Object> cliParams = new HashMap<>();
        cliParams.put("dataType", "name"); // 覆盖配置文件
        cliParams.put("count", 100); // 覆盖配置文件
        cliParams.put("output.format", "json"); // 覆盖配置文件
        cliParams.put("validation.enabled", false); // 覆盖配置文件
        cliParams.put("name.type", "CN"); // 新增参数
        
        System.out.println("\nCLI参数（部分覆盖）：");
        cliParams.forEach((key, value) -> 
            System.out.println("  --" + key + "=" + value));
        
        // 合并配置
        GenerationConfig defaultConfig = configManager.createDefaultConfiguration();
        GenerationConfig mergedConfig = configManager.mergeConfigurations(defaultConfig, fileConfig);
        GenerationConfig cliConfig = configManager.createConfigurationFromCLI(cliParams);
        GenerationConfig finalConfig = configManager.mergeConfigurations(mergedConfig, cliConfig);
        
        System.out.println("\n最终合并后的配置：");
        System.out.println("  - 数据类型: " + finalConfig.getDataType() + " (CLI覆盖)");
        System.out.println("  - 生成数量: " + finalConfig.getCount() + " (CLI覆盖)");
        System.out.println("  - 输出格式: " + finalConfig.getOutputConfig().getFormat() + " (CLI覆盖)");
        System.out.println("  - 输出文件: " + finalConfig.getOutputConfig().getFile() + " (配置文件)");
        System.out.println("  - 编码格式: " + finalConfig.getOutputConfig().getEncoding() + " (配置文件)");
        System.out.println("  - 校验启用: " + finalConfig.getValidationConfig().isEnabled() + " (CLI覆盖)");
        System.out.println("  - 严格模式: " + finalConfig.getValidationConfig().isStrictMode() + " (配置文件)");
        System.out.println("  - 线程池大小: " + finalConfig.getPerformanceConfig().getThreadPoolSize() + " (配置文件)");
        System.out.println("  - 并行处理: " + finalConfig.getPerformanceConfig().isEnableParallel() + " (配置文件)");
        System.out.println("  - 姓名类型: " + finalConfig.getParameters().get("name.type") + " (CLI新增)");
        System.out.println();
    }

    /**
     * 演示完整的优先级管理
     */
    private static void demonstrateFullPriorityManagement(ConfigurationManager configManager) {
        System.out.println("4. 完整优先级管理演示");
        System.out.println("=====================");
        
        // CLI参数
        Map<String, Object> cliParams = new HashMap<>();
        cliParams.put("dataType", "email");
        cliParams.put("count", 200);
        cliParams.put("output.format", "xml");
        cliParams.put("validation.strict", true);
        cliParams.put("performance.parallel", false);
        cliParams.put("email.domains", "qq.com,163.com,gmail.com");
        
        System.out.println("使用applyConfigurationPriorityIgnoreErrors方法：");
        System.out.println("CLI参数：");
        cliParams.forEach((key, value) -> 
            System.out.println("  --" + key + "=" + value));
        
        // 应用配置优先级（无配置文件）
        GenerationConfig finalConfig = configManager.applyConfigurationPriorityIgnoreErrors(
            cliParams, null);
        
        System.out.println("\n最终配置（默认配置 + CLI参数）：");
        System.out.println("  - 数据类型: " + finalConfig.getDataType() + " (CLI)");
        System.out.println("  - 生成数量: " + finalConfig.getCount() + " (CLI)");
        System.out.println("  - 输出格式: " + finalConfig.getOutputConfig().getFormat() + " (CLI)");
        System.out.println("  - 编码格式: " + finalConfig.getOutputConfig().getEncoding() + " (默认)");
        System.out.println("  - 校验启用: " + finalConfig.getValidationConfig().isEnabled() + " (默认)");
        System.out.println("  - 严格模式: " + finalConfig.getValidationConfig().isStrictMode() + " (CLI)");
        System.out.println("  - 线程池大小: " + finalConfig.getPerformanceConfig().getThreadPoolSize() + " (默认)");
        System.out.println("  - 并行处理: " + finalConfig.getPerformanceConfig().isEnableParallel() + " (CLI)");
        System.out.println("  - 邮箱域名: " + finalConfig.getParameters().get("email.domains") + " (CLI)");
        
        System.out.println("\n配置优先级总结：");
        System.out.println("1. CLI参数具有最高优先级，会覆盖配置文件和默认配置");
        System.out.println("2. 配置文件参数具有中等优先级，会覆盖默认配置");
        System.out.println("3. 默认配置具有最低优先级，作为基础配置");
        System.out.println("4. 配置合并是增量的，只有明确指定的值才会被覆盖");
        System.out.println("5. 支持部分配置覆盖，未指定的配置项保持原值");
        System.out.println();
    }
}