# DataForge应用程序配置文件

# Spring Boot配置
spring:
  application:
    name: dataforge
  profiles:
    active: default
  main:
    banner-mode: off
    web-application-type: none

# 日志配置
logging:
  level:
    com.dataforge: INFO
    org.springframework: WARN
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/dataforge.log
    max-size: 10MB
    max-history: 30

# DataForge特定配置
dataforge:
  # 默认配置
  default:
    output-format: json
    batch-size: 1000
    thread-count: 4
    validation-enabled: true
  
  # 性能配置
  performance:
    max-memory-usage: 512MB
    parallel-threshold: 10000
    batch-size-per-thread: 1000
  
  # 输出配置
  output:
    default-directory: ./output
    file-encoding: UTF-8
    create-directories: true
  
  # 生成器配置
  generators:
    # 文本生成器配置
    text:
      name:
        default-length: 2
        default-gender: random
      phone:
        default-carrier: random
        default-format: plain
      email:
        default-domain-type: common
        default-username-length: 8
      address:
        default-format: full
        include-postal-code: false
    
    # 数字生成器配置
    numeric:
      number:
        default-type: integer
        default-min: 0
        default-max: 100
        default-precision: 2
        default-distribution: uniform