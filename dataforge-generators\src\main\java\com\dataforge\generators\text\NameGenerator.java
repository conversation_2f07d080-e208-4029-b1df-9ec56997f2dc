package com.dataforge.generators.text;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 姓名生成器
 * 
 * 支持中英文姓名生成，基于真实姓氏库和名字库进行加权随机组合。
 * 支持性别相关的姓名生成逻辑，提供高质量的测试数据。
 * 
 * <AUTHOR> Team
 * @version 2.0.0
 */
public class NameGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "name";
    private static final String DESCRIPTION = "生成中英文姓名，支持性别倾向和频率加权";

    // 中文姓氏加权列表
    private final List<WeightedItem> chineseSurnames = new ArrayList<>();

    // 中文男性名字字符加权列表
    private final List<WeightedItem> chineseMaleNames = new ArrayList<>();

    // 中文女性名字字符加权列表
    private final List<WeightedItem> chineseFemaleNames = new ArrayList<>();

    // 英文名字加权列表
    private final List<WeightedItem> englishFirstNames = new ArrayList<>();
    private final List<WeightedItem> englishMaleFirstNames = new ArrayList<>();
    private final List<WeightedItem> englishFemaleFirstNames = new ArrayList<>();

    // 英文姓氏加权列表
    private final List<WeightedItem> englishLastNames = new ArrayList<>();

    // 总权重缓存
    private int chineseSurnamesWeight = 0;
    private int chineseMaleNamesWeight = 0;
    private int chineseFemaleNamesWeight = 0;
    private int englishFirstNamesWeight = 0;
    private int englishMaleFirstNamesWeight = 0;
    private int englishFemaleFirstNamesWeight = 0;
    private int englishLastNamesWeight = 0;

    /**
     * 加权项目类
     */
    private static class WeightedItem {
        final String value;
        final int weight;
        final String gender; // M, F, B (both)

        WeightedItem(String value, int weight) {
            this(value, weight, "B");
        }

        WeightedItem(String value, int weight, String gender) {
            this.value = value;
            this.weight = weight;
            this.gender = gender;
        }
    }

    /**
     * 构造函数
     */
    public NameGenerator() {
        loadNameLibraries();
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("gender", String.class, "random",
                "指定生成姓名的性别倾向，可选值：male（男性）、female（女性）、random（随机）", false));
        addParameter(new GeneratorParameter("nameLength", Integer.class, 2,
                "指定中文名字部分的字符长度，范围：1-3", false));
        addParameter(new GeneratorParameter("language", String.class, "chinese",
                "指定姓名语言，可选值：chinese（中文）、english（英文）", false));
        addParameter(new GeneratorParameter("format", String.class, "full",
                "指定姓名格式，可选值：full（全名）、first（名）、last（姓）", false));
    }

    /**
     * 加载姓名库
     */
    private void loadNameLibraries() {
        try {
            loadChineseSurnames();
            loadChineseMaleNames();
            loadChineseFemaleNames();
            loadEnglishFirstNames();
            loadEnglishLastNames();
        } catch (IOException e) {
            // 如果加载失败，使用默认数据
            loadDefaultData();
        }
    }

    /**
     * 加载中文姓氏库
     */
    private void loadChineseSurnames() throws IOException {
        loadWeightedItems("/names/chinese-surnames.txt", chineseSurnames);
        chineseSurnamesWeight = calculateTotalWeight(chineseSurnames);
    }

    /**
     * 加载中文男性名字库
     */
    private void loadChineseMaleNames() throws IOException {
        loadWeightedItems("/names/chinese-male-names.txt", chineseMaleNames);
        chineseMaleNamesWeight = calculateTotalWeight(chineseMaleNames);
    }

    /**
     * 加载中文女性名字库
     */
    private void loadChineseFemaleNames() throws IOException {
        loadWeightedItems("/names/chinese-female-names.txt", chineseFemaleNames);
        chineseFemaleNamesWeight = calculateTotalWeight(chineseFemaleNames);
    }

    /**
     * 加载英文名字库
     */
    private void loadEnglishFirstNames() throws IOException {
        try (InputStream is = getClass().getResourceAsStream("/names/english-first-names.txt");
                BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                String[] parts = line.split(":");
                if (parts.length >= 3) {
                    String name = parts[0];
                    int weight = Integer.parseInt(parts[1]);
                    String gender = parts[2];

                    WeightedItem item = new WeightedItem(name, weight, gender);
                    englishFirstNames.add(item);

                    if ("M".equals(gender)) {
                        englishMaleFirstNames.add(item);
                    } else if ("F".equals(gender)) {
                        englishFemaleFirstNames.add(item);
                    }
                }
            }
        }

        englishFirstNamesWeight = calculateTotalWeight(englishFirstNames);
        englishMaleFirstNamesWeight = calculateTotalWeight(englishMaleFirstNames);
        englishFemaleFirstNamesWeight = calculateTotalWeight(englishFemaleFirstNames);
    }

    /**
     * 加载英文姓氏库
     */
    private void loadEnglishLastNames() throws IOException {
        loadWeightedItems("/names/english-last-names.txt", englishLastNames);
        englishLastNamesWeight = calculateTotalWeight(englishLastNames);
    }

    /**
     * 加载加权项目
     */
    private void loadWeightedItems(String resourcePath, List<WeightedItem> items) throws IOException {
        try (InputStream is = getClass().getResourceAsStream(resourcePath);
                BufferedReader reader = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8))) {

            String line;
            while ((line = reader.readLine()) != null) {
                line = line.trim();
                if (line.isEmpty() || line.startsWith("#")) {
                    continue;
                }

                String[] parts = line.split(":");
                if (parts.length >= 2) {
                    String value = parts[0];
                    int weight = Integer.parseInt(parts[1]);
                    items.add(new WeightedItem(value, weight));
                }
            }
        }
    }

    /**
     * 计算总权重
     */
    private int calculateTotalWeight(List<WeightedItem> items) {
        return items.stream().mapToInt(item -> item.weight).sum();
    }

    /**
     * 加载默认数据（当资源文件加载失败时使用）
     */
    private void loadDefaultData() {
        // 默认中文姓氏
        String[] defaultSurnames = { "王", "李", "张", "刘", "陈", "杨", "赵", "黄", "周", "吴" };
        for (String surname : defaultSurnames) {
            chineseSurnames.add(new WeightedItem(surname, 10));
        }
        chineseSurnamesWeight = calculateTotalWeight(chineseSurnames);

        // 默认中文男性名字
        String[] defaultMaleNames = { "伟", "强", "磊", "军", "勇", "涛", "明", "超", "辉", "华" };
        for (String name : defaultMaleNames) {
            chineseMaleNames.add(new WeightedItem(name, 10));
        }
        chineseMaleNamesWeight = calculateTotalWeight(chineseMaleNames);

        // 默认中文女性名字
        String[] defaultFemaleNames = { "丽", "娜", "敏", "静", "秀", "美", "芳", "燕", "红", "霞" };
        for (String name : defaultFemaleNames) {
            chineseFemaleNames.add(new WeightedItem(name, 10));
        }
        chineseFemaleNamesWeight = calculateTotalWeight(chineseFemaleNames);

        // 默认英文名字
        englishFirstNames.add(new WeightedItem("John", 10, "M"));
        englishFirstNames.add(new WeightedItem("Mary", 10, "F"));
        englishMaleFirstNames.add(new WeightedItem("John", 10, "M"));
        englishFemaleFirstNames.add(new WeightedItem("Mary", 10, "F"));
        englishFirstNamesWeight = calculateTotalWeight(englishFirstNames);
        englishMaleFirstNamesWeight = calculateTotalWeight(englishMaleFirstNames);
        englishFemaleFirstNamesWeight = calculateTotalWeight(englishFemaleFirstNames);

        // 默认英文姓氏
        englishLastNames.add(new WeightedItem("Smith", 10));
        englishLastNames.add(new WeightedItem("Johnson", 10));
        englishLastNamesWeight = calculateTotalWeight(englishLastNames);
    }

    /**
     * 根据权重随机选择项目
     */
    private String selectWeightedItem(List<WeightedItem> items, int totalWeight) {
        if (items.isEmpty()) {
            return "";
        }

        int randomValue = ThreadLocalRandom.current().nextInt(totalWeight);
        int currentWeight = 0;

        for (WeightedItem item : items) {
            currentWeight += item.weight;
            if (randomValue < currentWeight) {
                return item.value;
            }
        }

        // 如果没有选中任何项目，返回第一个
        return items.get(0).value;
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        String gender = context.getParameter("gender", "random");
        Integer nameLength = context.getParameter("nameLength", 2);
        String language = context.getParameter("language", "chinese");
        String format = context.getParameter("format", "full");

        // 规范化性别参数
        if ("random".equals(gender)) {
            gender = ThreadLocalRandom.current().nextBoolean() ? "male" : "female";
        }

        String fullName;
        if ("chinese".equals(language)) {
            fullName = generateChineseName(gender, nameLength, format);
        } else if ("english".equals(language)) {
            fullName = generateEnglishName(gender, format);
        } else {
            throw new IllegalArgumentException("不支持的语言类型: " + language);
        }

        // 将生成的姓名设置到关联管理器中，触发关联字段的自动生成
        context.getRelationManager().setRelatedValue("name", fullName);
        context.getRelationManager().setRelatedValue("gender", gender);

        return fullName;
    }

    /**
     * 生成中文姓名
     */
    private String generateChineseName(String gender, int nameLength, String format) {
        String surname = "";
        String givenName = "";

        // 生成姓氏
        if (!"first".equals(format)) {
            surname = selectWeightedItem(chineseSurnames, chineseSurnamesWeight);
        }

        // 生成名字
        if (!"last".equals(format)) {
            StringBuilder givenNameBuilder = new StringBuilder();
            List<WeightedItem> nameChars = "male".equals(gender) ? chineseMaleNames : chineseFemaleNames;
            int totalWeight = "male".equals(gender) ? chineseMaleNamesWeight : chineseFemaleNamesWeight;

            for (int i = 0; i < nameLength; i++) {
                givenNameBuilder.append(selectWeightedItem(nameChars, totalWeight));
            }
            givenName = givenNameBuilder.toString();
        }

        return surname + givenName;
    }

    /**
     * 生成英文姓名
     */
    private String generateEnglishName(String gender, String format) {
        String firstName = "";
        String lastName = "";

        // 生成名字
        if (!"last".equals(format)) {
            List<WeightedItem> firstNames;
            int totalWeight;

            if ("male".equals(gender)) {
                firstNames = englishMaleFirstNames;
                totalWeight = englishMaleFirstNamesWeight;
            } else {
                firstNames = englishFemaleFirstNames;
                totalWeight = englishFemaleFirstNamesWeight;
            }

            firstName = selectWeightedItem(firstNames, totalWeight);
        }

        // 生成姓氏
        if (!"first".equals(format)) {
            lastName = selectWeightedItem(englishLastNames, englishLastNamesWeight);
        }

        if ("first".equals(format)) {
            return firstName;
        } else if ("last".equals(format)) {
            return lastName;
        } else {
            return firstName + " " + lastName;
        }
    }

    @Override
    public ValidationResult validateWithDetails(String data) {
        if (data == null) {
            return ValidationResult.error("姓名不能为空");
        }

        String trimmed = data.trim();
        if (trimmed.isEmpty()) {
            return ValidationResult.error("姓名不能为空");
        }

        // 检查是否为中文姓名
        if (trimmed.matches("[\\u4e00-\\u9fa5]+")) {
            return validateChineseName(trimmed);
        }

        // 检查是否为英文姓名
        if (trimmed.matches("[a-zA-Z\\s]+")) {
            return validateEnglishName(trimmed);
        }

        return ValidationResult.error("姓名格式不正确，应为中文或英文姓名");
    }

    /**
     * 验证中文姓名
     */
    private ValidationResult validateChineseName(String name) {
        if (name.length() < 2) {
            return ValidationResult.error("中文姓名长度不能少于2个字符");
        }

        if (name.length() > 4) {
            return ValidationResult.error("中文姓名长度不能超过4个字符");
        }

        return ValidationResult.success();
    }

    /**
     * 验证英文姓名
     */
    private ValidationResult validateEnglishName(String name) {
        String[] parts = name.split("\\s+");

        if (parts.length < 1 || parts.length > 3) {
            return ValidationResult.error("英文姓名应包含1-3个部分");
        }

        for (String part : parts) {
            if (part.length() < 1 || part.length() > 20) {
                return ValidationResult.error("英文姓名每个部分长度应在1-20个字符之间");
            }
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }

    /**
     * 获取支持的语言列表
     */
    public List<String> getSupportedLanguages() {
        return Arrays.asList("chinese", "english");
    }

    /**
     * 获取支持的格式列表
     */
    public List<String> getSupportedFormats() {
        return Arrays.asList("full", "first", "last");
    }

    /**
     * 获取支持的性别列表
     */
    public List<String> getSupportedGenders() {
        return Arrays.asList("male", "female", "random");
    }
}