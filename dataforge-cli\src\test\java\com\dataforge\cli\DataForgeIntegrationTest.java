package com.dataforge.cli;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.service.GeneratorFactory;
import com.dataforge.core.generator.DataGenerator;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.junit.jupiter.api.Assertions.*;

/**
 * DataForge集成测试
 * 
 * 测试各个组件的集成功能，确保整个系统能够正常工作。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
class DataForgeIntegrationTest {

    private GeneratorFactory generatorFactory;

    @BeforeEach
    void setUp() {
        generatorFactory = new GeneratorFactory();
    }

    @Test
    @DisplayName("测试生成器工厂初始化")
    void testGeneratorFactoryInitialization() {
        assertNotNull(generatorFactory);
        assertTrue(generatorFactory.getGeneratorCount() > 0);

        // 验证内置生成器是否正确注册
        assertTrue(generatorFactory.hasGenerator("name"));
        assertTrue(generatorFactory.hasGenerator("phone"));
        assertTrue(generatorFactory.hasGenerator("email"));
        assertTrue(generatorFactory.hasGenerator("address"));
        assertTrue(generatorFactory.hasGenerator("number"));
    }

    @Test
    @DisplayName("测试姓名生成器")
    void testNameGenerator() throws Exception {
        DataGenerator<String> generator = generatorFactory.getGenerator("name");
        assertNotNull(generator);

        GenerationContext context = new GenerationContext.Builder().build();
        String name = generator.generate(context);

        assertNotNull(name);
        assertFalse(name.trim().isEmpty());
        assertTrue(name.length() >= 2);
        assertTrue(name.length() <= 4);
        assertTrue(generator.validate(name));
    }

    @Test
    @DisplayName("测试手机号生成器")
    void testPhoneGenerator() throws Exception {
        DataGenerator<String> generator = generatorFactory.getGenerator("phone");
        assertNotNull(generator);

        GenerationContext context = new GenerationContext.Builder().build();
        String phone = generator.generate(context);

        assertNotNull(phone);
        assertEquals(11, phone.length());
        assertTrue(phone.startsWith("1"));
        assertTrue(phone.matches("\\d{11}"));
        assertTrue(generator.validate(phone));
    }

    @Test
    @DisplayName("测试邮箱生成器")
    void testEmailGenerator() throws Exception {
        DataGenerator<String> generator = generatorFactory.getGenerator("email");
        assertNotNull(generator);

        GenerationContext context = new GenerationContext.Builder().build();
        String email = generator.generate(context);

        assertNotNull(email);
        assertTrue(email.contains("@"));
        assertTrue(email.contains("."));
        assertTrue(generator.validate(email));
    }

    @Test
    @DisplayName("测试地址生成器")
    void testAddressGenerator() throws Exception {
        DataGenerator<String> generator = generatorFactory.getGenerator("address");
        assertNotNull(generator);

        GenerationContext context = new GenerationContext.Builder().build();
        String address = generator.generate(context);

        assertNotNull(address);
        assertFalse(address.trim().isEmpty());
        assertTrue(address.length() > 5);
        assertTrue(generator.validate(address));
    }

    @Test
    @DisplayName("测试数字生成器")
    void testNumberGenerator() throws Exception {
        DataGenerator<String> generator = generatorFactory.getGenerator("number");
        assertNotNull(generator);

        GenerationContext context = new GenerationContext.Builder().build();
        String number = generator.generate(context);

        assertNotNull(number);
        assertFalse(number.trim().isEmpty());
        assertTrue(generator.validate(number));

        // 验证是否为有效数字
        assertDoesNotThrow(() -> Double.parseDouble(number));
    }

    @Test
    @DisplayName("测试带参数的生成器")
    void testGeneratorWithParameters() throws Exception {
        DataGenerator<String> nameGenerator = generatorFactory.getGenerator("name");
        assertNotNull(nameGenerator);

        // 测试指定性别的姓名生成
        GenerationContext maleContext = new GenerationContext.Builder()
                .withParameter("gender", "male")
                .withParameter("nameLength", 2)
                .build();

        String maleName = nameGenerator.generate(maleContext);
        assertNotNull(maleName);
        assertTrue(nameGenerator.validate(maleName));

        // 测试指定运营商的手机号生成
        DataGenerator<String> phoneGenerator = generatorFactory.getGenerator("phone");
        GenerationContext mobileContext = new GenerationContext.Builder()
                .withParameter("carrier", "mobile")
                .withParameter("format", "dash")
                .build();

        String mobilePhone = phoneGenerator.generate(mobileContext);
        assertNotNull(mobilePhone);
        assertTrue(mobilePhone.contains("-"));
        assertTrue(phoneGenerator.validate(mobilePhone));
    }

    @Test
    @DisplayName("测试生成器参数验证")
    void testGeneratorParameterValidation() {
        DataGenerator<String> generator = generatorFactory.getGenerator("name");
        assertNotNull(generator);

        // 验证支持的参数
        assertTrue(generator.supportsParameter("gender"));
        assertTrue(generator.supportsParameter("nameLength"));
        assertFalse(generator.supportsParameter("invalidParameter"));

        // 验证参数信息
        assertNotNull(generator.getSupportedParameters());
        assertTrue(generator.getSupportedParameters().length > 0);
    }
}