package com.dataforge.core.performance;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.model.GenerationContext;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Callable;

/**
 * 数据生成任务
 * 
 * 封装单个数据生成任务，支持批量生成和异常处理。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class GenerationTask<T> implements Callable<List<T>> {

    private final DataGenerator<T> generator;
    private final GenerationContext context;
    private final int count;
    private final int taskId;

    /**
     * 构造函数
     * 
     * @param generator 数据生成器
     * @param context 生成上下文
     * @param count 生成数量
     * @param taskId 任务ID
     */
    public GenerationTask(DataGenerator<T> generator, GenerationContext context, int count, int taskId) {
        this.generator = generator;
        this.context = context;
        this.count = count;
        this.taskId = taskId;
    }

    @Override
    public List<T> call() throws Exception {
        List<T> results = new ArrayList<>(count);
        
        try {
            for (int i = 0; i < count; i++) {
                T data = generator.generate(context);
                results.add(data);
            }
        } catch (Exception e) {
            throw new RuntimeException("Task " + taskId + " failed to generate data", e);
        }
        
        return results;
    }

    public int getTaskId() {
        return taskId;
    }

    public int getCount() {
        return count;
    }
}