package com.dataforge.core.relation;

/**
 * 关联类型枚举
 * 
 * 定义数据字段之间的关联类型。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public enum RelationType {

    /**
     * 一对一关联
     * 一个源字段对应一个目标字段
     */
    ONE_TO_ONE("一对一", "一个源字段对应一个目标字段"),

    /**
     * 一对多关联
     * 一个源字段对应多个目标字段
     */
    ONE_TO_MANY("一对多", "一个源字段对应多个目标字段"),

    /**
     * 提取关联
     * 从源字段中提取信息到目标字段
     */
    EXTRACTION("提取", "从源字段中提取信息到目标字段"),

    /**
     * 计算关联
     * 基于源字段计算目标字段的值
     */
    CALCULATION("计算", "基于源字段计算目标字段的值"),

    /**
     * 格式化关联
     * 将源字段格式化为目标字段
     */
    FORMATTING("格式化", "将源字段格式化为目标字段"),

    /**
     * 映射关联
     * 将源字段值映射为目标字段值
     */
    MAPPING("映射", "将源字段值映射为目标字段值"),

    /**
     * 条件关联
     * 基于条件决定目标字段的值
     */
    CONDITIONAL("条件", "基于条件决定目标字段的值"),

    /**
     * 自定义关联
     * 用户自定义的关联逻辑
     */
    CUSTOM("自定义", "用户自定义的关联逻辑");

    private final String displayName;
    private final String description;

    /**
     * 构造函数
     * 
     * @param displayName 显示名称
     * @param description 描述
     */
    RelationType(String displayName, String description) {
        this.displayName = displayName;
        this.description = description;
    }

    /**
     * 获取显示名称
     * 
     * @return 显示名称
     */
    public String getDisplayName() {
        return displayName;
    }

    /**
     * 获取描述
     * 
     * @return 描述
     */
    public String getDescription() {
        return description;
    }

    @Override
    public String toString() {
        return displayName;
    }
}