package com.dataforge.core.generator;

/**
 * 数据生成异常
 * 
 * 在数据生成过程中发生错误时抛出的异常类。
 * 提供详细的错误信息和上下文，便于问题诊断和处理。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class GenerationException extends Exception {

    private static final long serialVersionUID = 1L;

    private final String generatorType;
    private final String parameterName;
    private final Object parameterValue;

    /**
     * 构造函数
     * 
     * @param message 错误消息
     */
    public GenerationException(String message) {
        super(message);
        this.generatorType = null;
        this.parameterName = null;
        this.parameterValue = null;
    }

    /**
     * 构造函数
     * 
     * @param message 错误消息
     * @param cause   原因异常
     */
    public GenerationException(String message, Throwable cause) {
        super(message, cause);
        this.generatorType = null;
        this.parameterName = null;
        this.parameterValue = null;
    }

    /**
     * 构造函数
     * 
     * @param message       错误消息
     * @param generatorType 生成器类型
     */
    public GenerationException(String message, String generatorType) {
        super(message);
        this.generatorType = generatorType;
        this.parameterName = null;
        this.parameterValue = null;
    }

    /**
     * 构造函数
     * 
     * @param message       错误消息
     * @param generatorType 生成器类型
     * @param cause         原因异常
     */
    public GenerationException(String message, String generatorType, Throwable cause) {
        super(message, cause);
        this.generatorType = generatorType;
        this.parameterName = null;
        this.parameterValue = null;
    }

    /**
     * 构造函数
     * 
     * @param message        错误消息
     * @param generatorType  生成器类型
     * @param parameterName  参数名称
     * @param parameterValue 参数值
     */
    public GenerationException(String message, String generatorType, String parameterName, Object parameterValue) {
        super(message);
        this.generatorType = generatorType;
        this.parameterName = parameterName;
        this.parameterValue = parameterValue;
    }

    /**
     * 构造函数
     * 
     * @param message        错误消息
     * @param generatorType  生成器类型
     * @param parameterName  参数名称
     * @param parameterValue 参数值
     * @param cause          原因异常
     */
    public GenerationException(String message, String generatorType, String parameterName, Object parameterValue,
            Throwable cause) {
        super(message, cause);
        this.generatorType = generatorType;
        this.parameterName = parameterName;
        this.parameterValue = parameterValue;
    }

    /**
     * 获取生成器类型
     * 
     * @return 生成器类型
     */
    public String getGeneratorType() {
        return generatorType;
    }

    /**
     * 获取参数名称
     * 
     * @return 参数名称
     */
    public String getParameterName() {
        return parameterName;
    }

    /**
     * 获取参数值
     * 
     * @return 参数值
     */
    public Object getParameterValue() {
        return parameterValue;
    }

    /**
     * 获取详细的错误消息
     * 
     * @return 详细错误消息
     */
    @Override
    public String getMessage() {
        StringBuilder sb = new StringBuilder(super.getMessage());

        if (generatorType != null) {
            sb.append(" [生成器类型: ").append(generatorType).append("]");
        }

        if (parameterName != null) {
            sb.append(" [参数: ").append(parameterName);
            if (parameterValue != null) {
                sb.append("=").append(parameterValue);
            }
            sb.append("]");
        }

        return sb.toString();
    }
}