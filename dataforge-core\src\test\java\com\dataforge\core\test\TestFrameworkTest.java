package com.dataforge.core.test;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.mockito.Mockito;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 测试框架验证测试
 * 验证JUnit 5、Mocki<PERSON>和AssertJ是否正常工作
 */
class TestFrameworkTest {

    @Test
    @DisplayName("JUnit 5基础功能测试")
    void testJUnit5BasicFunctionality() {
        // 基础断言测试
        assertTrue(true);
        assertFalse(false);
        assertEquals(1, 1);
        assertNotNull("test");

        // 异常测试
        assertThrows(IllegalArgumentException.class, () -> {
            throw new IllegalArgumentException("测试异常");
        });
    }

    @Test
    @DisplayName("AssertJ断言库测试")
    void testAssertJFunctionality() {
        // AssertJ流式断言测试
        assertThat("Hello World")
                .isNotNull()
                .isNotEmpty()
                .startsWith("Hello")
                .endsWith("World")
                .contains("o W");

        assertThat(42)
                .isEqualTo(42)
                .isGreaterThan(40)
                .isLessThan(50);
    }

    @Test
    @DisplayName("Mockito模拟框架测试")
    void testMockitoFunctionality() {
        // 创建模拟对象
        java.util.List<String> mockList = Mockito.mock(java.util.List.class);

        // 设置模拟行为
        Mockito.when(mockList.size()).thenReturn(10);
        Mockito.when(mockList.get(0)).thenReturn("first");

        // 验证模拟行为
        assertEquals(10, mockList.size());
        assertEquals("first", mockList.get(0));

        // 验证方法调用
        Mockito.verify(mockList).size();
        Mockito.verify(mockList).get(0);
    }

    @Test
    @DisplayName("测试工具类功能测试")
    void testTestUtilsFunctionality() {
        // 测试GenerationConfig创建
        var config = TestUtils.createTestGenerationConfig("name", 100);
        assertThat(config).isNotNull();
        assertThat(config.getDataType()).isEqualTo("name");
        assertThat(config.getCount()).isEqualTo(100);

        // 测试OutputConfig创建
        var outputConfig = TestUtils.createTestOutputConfig();
        assertThat(outputConfig).isNotNull();
        assertThat(outputConfig.getFormat()).isEqualTo("json");
        assertThat(outputConfig.getTarget()).isEqualTo("stdout");

        // 测试ValidationConfig创建
        var validationConfig = TestUtils.createTestValidationConfig();
        assertThat(validationConfig).isNotNull();
        assertThat(validationConfig.isEnabled()).isTrue();
        assertThat(validationConfig.isStrictMode()).isFalse();

        // 测试GenerationContext创建
        var context = TestUtils.createTestGenerationContext();
        assertThat(context).isNotNull();
        assertThat(context.hasParameter("test")).isTrue();
        assertThat(context.getParameter("test")).isEqualTo(true);
    }

    @Test
    @DisplayName("数据格式验证工具测试")
    void testDataValidationUtils() {
        // 身份证号格式验证
        assertTrue(TestUtils.isValidIdCardFormat("110101199001011234"));
        assertFalse(TestUtils.isValidIdCardFormat("123456789"));
        assertFalse(TestUtils.isValidIdCardFormat(null));

        // 手机号格式验证
        assertTrue(TestUtils.isValidPhoneFormat("13800138000"));
        assertFalse(TestUtils.isValidPhoneFormat("12345678901"));
        assertFalse(TestUtils.isValidPhoneFormat(null));

        // 邮箱格式验证
        assertTrue(TestUtils.isValidEmailFormat("<EMAIL>"));
        assertFalse(TestUtils.isValidEmailFormat("invalid-email"));
        assertFalse(TestUtils.isValidEmailFormat(null));
    }
}