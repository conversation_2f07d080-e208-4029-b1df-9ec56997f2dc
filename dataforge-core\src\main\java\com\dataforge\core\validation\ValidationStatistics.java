package com.dataforge.core.validation;

import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

/**
 * 校验统计信息
 * 
 * 封装校验过程的统计数据，包括成功率、失败率和错误类型分布。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ValidationStatistics {

    private final int totalCount;
    private final int validCount;
    private final int invalidCount;
    private final Map<String, Integer> errorTypeCount;

    /**
     * 构造函数
     * 
     * @param totalCount     总数量
     * @param validCount     有效数量
     * @param invalidCount   无效数量
     * @param errorTypeCount 错误类型统计
     */
    public ValidationStatistics(int totalCount, int validCount, int invalidCount, Map<String, Integer> errorTypeCount) {
        this.totalCount = totalCount;
        this.validCount = validCount;
        this.invalidCount = invalidCount;
        this.errorTypeCount = errorTypeCount != null ? Collections.unmodifiableMap(new HashMap<>(errorTypeCount))
                : Collections.emptyMap();
    }

    /**
     * 创建空的统计信息
     * 
     * @return 空的统计信息
     */
    public static ValidationStatistics empty() {
        return new ValidationStatistics(0, 0, 0, Collections.emptyMap());
    }

    /**
     * 获取总数量
     * 
     * @return 总数量
     */
    public int getTotalCount() {
        return totalCount;
    }

    /**
     * 获取有效数量
     * 
     * @return 有效数量
     */
    public int getValidCount() {
        return validCount;
    }

    /**
     * 获取无效数量
     * 
     * @return 无效数量
     */
    public int getInvalidCount() {
        return invalidCount;
    }

    /**
     * 获取成功率
     * 
     * @return 成功率（0.0-1.0）
     */
    public double getSuccessRate() {
        return totalCount > 0 ? (double) validCount / totalCount : 0.0;
    }

    /**
     * 获取失败率
     * 
     * @return 失败率（0.0-1.0）
     */
    public double getFailureRate() {
        return totalCount > 0 ? (double) invalidCount / totalCount : 0.0;
    }

    /**
     * 获取校验率（兼容方法）
     * 
     * @return 校验率（0.0-1.0）
     */
    public double getValidationRate() {
        return getSuccessRate();
    }

    /**
     * 获取错误类型统计
     * 
     * @return 错误类型到数量的映射
     */
    public Map<String, Integer> getErrorTypeCount() {
        return errorTypeCount;
    }

    /**
     * 获取指定错误类型的数量
     * 
     * @param errorType 错误类型
     * @return 错误数量
     */
    public int getErrorCount(String errorType) {
        return errorTypeCount.getOrDefault(errorType, 0);
    }

    /**
     * 获取最常见的错误类型
     * 
     * @return 最常见的错误类型，如果没有错误返回null
     */
    public String getMostCommonErrorType() {
        return errorTypeCount.entrySet().stream()
                .max(Map.Entry.comparingByValue())
                .map(Map.Entry::getKey)
                .orElse(null);
    }

    /**
     * 获取错误类型种类数量
     * 
     * @return 错误类型种类数量
     */
    public int getErrorTypeVarietyCount() {
        return errorTypeCount.size();
    }

    /**
     * 检查是否为空
     * 
     * @return 如果总数量为0返回true，否则返回false
     */
    public boolean isEmpty() {
        return totalCount == 0;
    }

    /**
     * 检查是否全部有效
     * 
     * @return 如果全部有效返回true，否则返回false
     */
    public boolean isAllValid() {
        return totalCount > 0 && invalidCount == 0;
    }

    /**
     * 检查是否全部无效
     * 
     * @return 如果全部无效返回true，否则返回false
     */
    public boolean isAllInvalid() {
        return totalCount > 0 && validCount == 0;
    }

    /**
     * 检查是否有错误
     * 
     * @return 如果有错误返回true，否则返回false
     */
    public boolean hasErrors() {
        return invalidCount > 0;
    }

    /**
     * 获取成功率百分比字符串
     * 
     * @return 成功率百分比字符串
     */
    public String getSuccessRatePercentage() {
        return String.format("%.1f%%", getSuccessRate() * 100);
    }

    /**
     * 获取失败率百分比字符串
     * 
     * @return 失败率百分比字符串
     */
    public String getFailureRatePercentage() {
        return String.format("%.1f%%", getFailureRate() * 100);
    }

    /**
     * 获取摘要信息
     * 
     * @return 摘要信息
     */
    public String getSummary() {
        if (isEmpty()) {
            return "无校验数据";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("校验统计: ");
        sb.append("总计 ").append(totalCount).append(" 条");
        sb.append(", 成功 ").append(validCount).append(" 条");
        sb.append(", 失败 ").append(invalidCount).append(" 条");
        sb.append(", 成功率 ").append(getSuccessRatePercentage());

        if (hasErrors()) {
            sb.append(", 错误类型 ").append(getErrorTypeVarietyCount()).append(" 种");
        }

        return sb.toString();
    }

    /**
     * 获取详细报告
     * 
     * @return 详细报告
     */
    public String getDetailedReport() {
        StringBuilder sb = new StringBuilder();
        sb.append("=== 校验统计报告 ===\n");

        if (isEmpty()) {
            sb.append("无校验数据\n");
            return sb.toString();
        }

        sb.append("总体统计:\n");
        sb.append("  总数量: ").append(totalCount).append("\n");
        sb.append("  成功数量: ").append(validCount).append("\n");
        sb.append("  失败数量: ").append(invalidCount).append("\n");
        sb.append("  成功率: ").append(getSuccessRatePercentage()).append("\n");
        sb.append("  失败率: ").append(getFailureRatePercentage()).append("\n");

        if (hasErrors()) {
            sb.append("\n错误类型分布:\n");
            errorTypeCount.entrySet().stream()
                    .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                    .forEach(entry -> {
                        double percentage = (double) entry.getValue() / invalidCount * 100;
                        sb.append("  ").append(entry.getKey())
                                .append(": ").append(entry.getValue()).append(" 次")
                                .append(" (").append(String.format("%.1f%%", percentage)).append(")\n");
                    });

            String mostCommon = getMostCommonErrorType();
            if (mostCommon != null) {
                sb.append("\n最常见错误类型: ").append(mostCommon)
                        .append(" (").append(getErrorCount(mostCommon)).append(" 次)\n");
            }
        }

        return sb.toString();
    }

    @Override
    public String toString() {
        return String.format("ValidationStatistics{total=%d, valid=%d, invalid=%d, rate=%.1f%%, errorTypes=%d}",
                totalCount, validCount, invalidCount, getSuccessRate() * 100, getErrorTypeVarietyCount());
    }
}