package com.dataforge.core.relation;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 一致性检查结果
 * 
 * 封装数据一致性检查的结果，包括违规信息和警告信息。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ConsistencyResult {

    private final boolean consistent;
    private final List<String> violations;
    private final List<String> warnings;
    private final String message;

    /**
     * 私有构造函数
     * 
     * @param consistent 是否一致
     * @param violations 违规信息列表
     * @param warnings   警告信息列表
     * @param message    结果消息
     */
    private ConsistencyResult(boolean consistent, List<String> violations,
            List<String> warnings, String message) {
        this.consistent = consistent;
        this.violations = violations != null ? List.copyOf(violations) : Collections.emptyList();
        this.warnings = warnings != null ? List.copyOf(warnings) : Collections.emptyList();
        this.message = message;
    }

    /**
     * 创建成功结果
     * 
     * @param message 成功消息
     * @return 成功结果
     */
    public static ConsistencyResult success(String message) {
        return new ConsistencyResult(true, Collections.emptyList(),
                Collections.emptyList(), message);
    }

    /**
     * 创建带警告的成功结果
     * 
     * @param warnings 警告信息列表
     * @return 带警告的成功结果
     */
    public static ConsistencyResult successWithWarnings(List<String> warnings) {
        return new ConsistencyResult(true, Collections.emptyList(), warnings,
                "检查通过，但有警告");
    }

    /**
     * 创建失败结果
     * 
     * @param violations 违规信息列表
     * @return 失败结果
     */
    public static ConsistencyResult failure(List<String> violations) {
        return new ConsistencyResult(false, violations, Collections.emptyList(),
                "一致性检查失败");
    }

    /**
     * 创建带警告的失败结果
     * 
     * @param violations 违规信息列表
     * @param warnings   警告信息列表
     * @return 带警告的失败结果
     */
    public static ConsistencyResult failure(List<String> violations, List<String> warnings) {
        return new ConsistencyResult(false, violations, warnings,
                "一致性检查失败");
    }

    /**
     * 检查是否一致
     * 
     * @return 如果一致返回true，否则返回false
     */
    public boolean isConsistent() {
        return consistent;
    }

    /**
     * 检查是否有违规
     * 
     * @return 如果有违规返回true，否则返回false
     */
    public boolean hasViolations() {
        return !violations.isEmpty();
    }

    /**
     * 检查是否有警告
     * 
     * @return 如果有警告返回true，否则返回false
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }

    /**
     * 获取违规信息列表
     * 
     * @return 违规信息列表
     */
    public List<String> getViolations() {
        return violations;
    }

    /**
     * 获取警告信息列表
     * 
     * @return 警告信息列表
     */
    public List<String> getWarnings() {
        return warnings;
    }

    /**
     * 获取结果消息
     * 
     * @return 结果消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取违规数量
     * 
     * @return 违规数量
     */
    public int getViolationCount() {
        return violations.size();
    }

    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public int getWarningCount() {
        return warnings.size();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        ConsistencyResult that = (ConsistencyResult) o;
        return consistent == that.consistent &&
                Objects.equals(violations, that.violations) &&
                Objects.equals(warnings, that.warnings) &&
                Objects.equals(message, that.message);
    }

    @Override
    public int hashCode() {
        return Objects.hash(consistent, violations, warnings, message);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ConsistencyResult{");
        sb.append("consistent=").append(consistent);

        if (hasViolations()) {
            sb.append(", violations=").append(violations.size());
        }

        if (hasWarnings()) {
            sb.append(", warnings=").append(warnings.size());
        }

        if (message != null) {
            sb.append(", message='").append(message).append("'");
        }

        sb.append("}");
        return sb.toString();
    }
}