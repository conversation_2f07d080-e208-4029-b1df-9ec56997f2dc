package com.dataforge.cli;

import com.dataforge.cli.config.ParameterParser;
import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.ValidationResult;
import com.dataforge.core.service.ConfigurationManager;
import com.dataforge.core.service.DataForgeService;
import com.dataforge.core.service.DataForgeService;
import org.apache.commons.cli.ParseException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 命令处理器
 * 
 * 负责处理解析后的命令行参数，协调各个服务组件执行数据生成任务。
 * 提供统一的命令执行入口和结果处理。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class CommandProcessor {

    private static final Logger logger = LoggerFactory.getLogger(CommandProcessor.class);

    private final CommandLineInterface cli;
    private final ParameterParser parameterParser;
    private final ConfigurationManager configurationManager;
    private final DataForgeService dataForgeService;
    private final HelpFormatter helpFormatter;

    /**
     * 构造函数
     */
    @Autowired
    public CommandProcessor(CommandLineInterface cli,
            ParameterParser parameterParser,
            ConfigurationManager configurationManager,
            DataForgeService dataForgeService,
            HelpFormatter helpFormatter) {
        this.cli = cli;
        this.parameterParser = parameterParser;
        this.configurationManager = configurationManager;
        this.dataForgeService = dataForgeService;
        this.helpFormatter = helpFormatter;
    }

    /**
     * 处理命令行参数并执行相应操作
     * 
     * @param args 命令行参数数组
     * @return 执行结果代码 (0: 成功, 1: 错误, 2: 帮助信息)
     */
    public int processCommand(String[] args) {
        logger.info("开始处理命令: {}", String.join(" ", args));

        try {
            // 检查特殊命令
            if (args.length == 0 || cli.isHelpRequested(args)) {
                helpFormatter.printHelp();
                return 2; // 帮助信息
            }

            if (cli.isVersionRequested(args)) {
                helpFormatter.printVersion();
                return 2; // 版本信息
            }

            // 解析命令行参数
            GenerationConfig config = cli.parseArguments(args);

            // 处理配置文件
            config = processConfigFile(config, args);

            // 验证配置
            ValidationResult validationResult = validateConfiguration(config);
            if (!validationResult.isValid()) {
                printValidationErrors(validationResult);
                return 1; // 配置错误
            }

            // 执行数据生成
            return executeDataGeneration(config);

        } catch (ParseException e) {
            logger.error("命令行参数解析失败: {}", e.getMessage());
            System.err.println("参数解析错误: " + e.getMessage());
            System.err.println("使用 --help 查看帮助信息");
            return 1;
        } catch (Exception e) {
            logger.error("命令处理失败", e);
            System.err.println("执行失败: " + e.getMessage());
            return 1;
        }
    }

    /**
     * 处理配置文件
     * 
     * @param config 当前配置
     * @param args   命令行参数
     * @return 合并后的配置
     */
    private GenerationConfig processConfigFile(GenerationConfig config, String[] args) {
        try {
            // 检查是否指定了配置文件
            String configFile = extractConfigFile(args);
            if (configFile != null) {
                logger.info("加载配置文件: {}", configFile);
                GenerationConfig fileConfig = configurationManager.loadFromFile(configFile);

                // 合并配置 (命令行参数优先级更高)
                config = configurationManager.mergeConfigurations(fileConfig, config);
                logger.debug("配置文件加载并合并完成");
            }
        } catch (Exception e) {
            logger.error("配置文件处理失败: {}", e.getMessage());
            System.err.println("配置文件加载失败: " + e.getMessage());
            throw new RuntimeException("配置文件处理失败", e);
        }

        return config;
    }

    /**
     * 验证配置
     * 
     * @param config 生成配置
     * @return 验证结果
     */
    private ValidationResult validateConfiguration(GenerationConfig config) {
        logger.debug("开始验证配置");

        // 基本配置验证
        ValidationResult result = config.validate();

        // 参数验证
        ValidationResult paramResult = parameterParser.validateGenerationConfig(config);
        result.merge(paramResult);

        if (result.isValid()) {
            logger.info("配置验证通过");
        } else {
            logger.warn("配置验证失败: {}", result.getErrorsAsString());
        }

        return result;
    }

    /**
     * 执行数据生成
     * 
     * @param config 生成配置
     * @return 执行结果代码
     */
    private int executeDataGeneration(GenerationConfig config) {
        try {
            logger.info("开始执行数据生成: {}", config);

            // 显示生成信息
            printGenerationInfo(config);

            // 执行生成
            long startTime = System.currentTimeMillis();
            DataForgeService.GenerationResult result = dataForgeService.generateData(config);
            long endTime = System.currentTimeMillis();

            // 显示结果
            printGenerationResult(result, endTime - startTime);

            logger.info("数据生成完成，耗时: {}ms", endTime - startTime);
            return 0; // 成功

        } catch (Exception e) {
            logger.error("数据生成失败", e);
            System.err.println("数据生成失败: " + e.getMessage());
            return 1; // 失败
        }
    }

    /**
     * 从命令行参数中提取配置文件路径
     * 
     * @param args 命令行参数
     * @return 配置文件路径，如果未指定返回null
     */
    private String extractConfigFile(String[] args) {
        for (int i = 0; i < args.length - 1; i++) {
            if ("-c".equals(args[i]) || "--config".equals(args[i])) {
                return args[i + 1];
            }
        }
        return null;
    }

    /**
     * 打印验证错误信息
     * 
     * @param validationResult 验证结果
     */
    private void printValidationErrors(ValidationResult validationResult) {
        System.err.println("配置验证失败:");

        List<String> errors = validationResult.getErrors();
        for (int i = 0; i < errors.size(); i++) {
            System.err.println("  " + (i + 1) + ". " + errors.get(i));
        }

        List<String> warnings = validationResult.getWarnings();
        if (!warnings.isEmpty()) {
            System.err.println("\n警告:");
            for (int i = 0; i < warnings.size(); i++) {
                System.err.println("  " + (i + 1) + ". " + warnings.get(i));
            }
        }

        System.err.println("\n使用 --help 查看帮助信息");
    }

    /**
     * 打印生成信息
     * 
     * @param config 生成配置
     */
    private void printGenerationInfo(GenerationConfig config) {
        System.out.println("DataForge 数据生成");
        System.out.println("==================");

        if (config.isMultiFieldMode()) {
            System.out.println("模式: 多字段生成");
            System.out.println("字段数量: " + config.getFields().size());
        } else {
            System.out.println("数据类型: " + config.getDataType());
        }

        System.out.println("生成数量: " + config.getCount());
        System.out.println("输出格式: " + config.getOutputConfig().getFormat());

        if (config.getOutputConfig().isFileOutput()) {
            System.out.println("输出文件: " + config.getOutputConfig().getActualFilePath());
        } else {
            System.out.println("输出目标: 控制台");
        }

        if (config.getSeed() != null) {
            System.out.println("随机种子: " + config.getSeed());
        }

        System.out.println("校验模式: " + (config.getValidationConfig().isEnabled() ? "启用" : "禁用"));

        if (config.getPerformanceConfig().isEnableParallel()) {
            System.out.println("并行处理: 启用 (线程数: " +
                    config.getPerformanceConfig().getActualThreadCount(config.getCount()) + ")");
        } else {
            System.out.println("并行处理: 禁用");
        }

        System.out.println();
        System.out.println("正在生成数据...");
    }

    /**
     * 打印生成结果
     * 
     * @param result   生成结果
     * @param duration 耗时（毫秒）
     */
    private void printGenerationResult(DataForgeService.GenerationResult result, long duration) {
        System.out.println();
        System.out.println("生成完成");
        System.out.println("========");
        System.out.println("成功生成: " + result.getSuccessCount() + " 条数据");

        if (result.getFailureCount() > 0) {
            System.out.println("生成失败: " + result.getFailureCount() + " 条数据");
        }

        if (result.getValidationErrors() > 0) {
            System.out.println("校验错误: " + result.getValidationErrors() + " 条数据");
        }

        System.out.println("总耗时: " + duration + " ms");

        if (duration > 0) {
            double rate = (double) result.getSuccessCount() * 1000 / duration;
            System.out.println("生成速率: " + String.format("%.2f", rate) + " 条/秒");
        }

        if (result.getOutputFile() != null) {
            System.out.println("输出文件: " + result.getOutputFile());
        }

        // 显示警告信息
        if (result.hasWarnings()) {
            System.out.println();
            System.out.println("警告信息:");
            for (String warning : result.getWarnings()) {
                System.out.println("  - " + warning);
            }
        }
    }

}