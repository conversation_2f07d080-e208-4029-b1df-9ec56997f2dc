package com.dataforge.performance.benchmark;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.PerformanceConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.service.DataForgeService;
import com.dataforge.core.service.GeneratorFactory;
import com.dataforge.core.service.DataRelationManager;
import com.dataforge.core.relation.ConsistencyManager;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;

import java.util.concurrent.TimeUnit;

/**
 * 大数据量生成性能基准测试
 * 
 * 专门测试大规模数据生成的性能表现和扩展性
 */
@BenchmarkMode({Mode.Throughput, Mode.AverageTime})
@OutputTimeUnit(TimeUnit.SECONDS)
@State(Scope.Benchmark)
@Fork(1)
@Warmup(iterations = 2, time = 2, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 3, time = 5, timeUnit = TimeUnit.SECONDS)
@Timeout(time = 300, timeUnit = TimeUnit.SECONDS) // 5分钟超时
public class LargeDataBenchmark {

    private DataForgeService dataForgeService;

    @Setup(Level.Trial)
    public void setupTrial() {
        GeneratorFactory generatorFactory = new GeneratorFactory();
        DataRelationManager relationManager = new DataRelationManager();
        ConsistencyManager consistencyManager = new ConsistencyManager();
        dataForgeService = new DataForgeService(generatorFactory, relationManager, consistencyManager);
    }

    /**
     * 10万条数据生成测试
     */
    @Benchmark
    public void benchmark100KRecords(Blackhole bh) {
        GenerationConfig config = createLargeDataConfig("uuid", 100000);
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 50万条数据生成测试
     */
    @Benchmark
    public void benchmark500KRecords(Blackhole bh) {
        GenerationConfig config = createLargeDataConfig("email", 500000);
        
        // 启用并行处理以提高性能
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(4);
        perfConfig.setBatchSize(5000);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 100万条数据生成测试
     */
    @Benchmark
    public void benchmark1MRecords(Blackhole bh) {
        GenerationConfig config = createLargeDataConfig("name", 1000000);
        
        // 优化并行配置
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(6);
        perfConfig.setBatchSize(10000);
        perfConfig.setCacheEnabled(true);
        perfConfig.setCacheMaxSize(50000);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 复杂数据类型大量生成测试
     */
    @Benchmark
    public void benchmarkComplexLargeData(Blackhole bh) {
        // 身份证号生成计算复杂度较高
        GenerationConfig config = createLargeDataConfig("idcard", 200000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(4);
        perfConfig.setBatchSize(2000);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 不同线程数对大数据量生成的影响
     */
    @Param({"1", "2", "4", "6", "8"})
    public int threadCount;

    @Benchmark
    public void benchmarkThreadScaling(Blackhole bh) {
        GenerationConfig config = createLargeDataConfig("phone", 100000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(threadCount > 1);
        perfConfig.setThreadPoolSize(threadCount);
        perfConfig.setBatchSize(5000);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 不同批处理大小对大数据量生成的影响
     */
    @Param({"1000", "5000", "10000", "20000", "50000"})
    public int batchSize;

    @Benchmark
    public void benchmarkBatchSizeScaling(Blackhole bh) {
        GenerationConfig config = createLargeDataConfig("uuid", 200000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(4);
        perfConfig.setBatchSize(batchSize);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 缓存对大数据量生成的影响
     */
    @Benchmark
    public void benchmarkCacheImpactOnLargeData(Blackhole bh) {
        GenerationConfig configWithCache = createLargeDataConfig("email", 150000);
        
        PerformanceConfig perfConfigWithCache = new PerformanceConfig();
        perfConfigWithCache.setEnableParallel(true);
        perfConfigWithCache.setThreadPoolSize(4);
        perfConfigWithCache.setBatchSize(5000);
        perfConfigWithCache.setCacheEnabled(true);
        perfConfigWithCache.setCacheMaxSize(20000);
        configWithCache.setPerformanceConfig(perfConfigWithCache);
        
        DataForgeService.GenerationResult resultWithCache = dataForgeService.generateData(configWithCache);
        bh.consume(resultWithCache);
    }

    /**
     * 无缓存大数据量生成测试
     */
    @Benchmark
    public void benchmarkNoCacheOnLargeData(Blackhole bh) {
        GenerationConfig configNoCache = createLargeDataConfig("email", 150000);
        
        PerformanceConfig perfConfigNoCache = new PerformanceConfig();
        perfConfigNoCache.setEnableParallel(true);
        perfConfigNoCache.setThreadPoolSize(4);
        perfConfigNoCache.setBatchSize(5000);
        perfConfigNoCache.setCacheEnabled(false);
        configNoCache.setPerformanceConfig(perfConfigNoCache);
        
        DataForgeService.GenerationResult resultNoCache = dataForgeService.generateData(configNoCache);
        bh.consume(resultNoCache);
    }

    /**
     * 极限数据量测试 - 500万条记录
     */
    @Benchmark
    public void benchmarkExtremeDataVolume(Blackhole bh) {
        GenerationConfig config = createLargeDataConfig("uuid", 5000000);
        
        // 极限优化配置
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(8);
        perfConfig.setBatchSize(50000);
        perfConfig.setCacheEnabled(true);
        perfConfig.setCacheMaxSize(100000);
        perfConfig.setBufferSize(16384); // 16KB缓冲区
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 内存限制下的大数据量生成测试
     */
    @Benchmark
    public void benchmarkMemoryConstrainedLargeData(Blackhole bh) {
        GenerationConfig config = createLargeDataConfig("idcard", 300000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(4);
        perfConfig.setBatchSize(2000); // 较小的批处理以减少内存使用
        perfConfig.setCacheEnabled(false); // 禁用缓存以节省内存
        perfConfig.setMaxMemoryUsage(256 * 1024 * 1024); // 256MB内存限制
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        bh.consume(result);
    }

    /**
     * 创建大数据量测试配置
     */
    private GenerationConfig createLargeDataConfig(String dataType, int count) {
        GenerationConfig config = new GenerationConfig();
        config.setDataType(dataType);
        config.setCount(count);
        config.setSeed(12345L);
        
        // 禁用验证以专注于生成性能
        ValidationConfig validationConfig = new ValidationConfig();
        validationConfig.setEnabled(false);
        config.setValidationConfig(validationConfig);
        
        // 默认性能配置
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(false);
        perfConfig.setBatchSize(10000);
        perfConfig.setCacheEnabled(true);
        perfConfig.setCacheMaxSize(10000);
        config.setPerformanceConfig(perfConfig);
        
        return config;
    }
}