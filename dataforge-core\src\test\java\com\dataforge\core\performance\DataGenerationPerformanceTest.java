package com.dataforge.core.performance;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.FieldConfig;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.model.PerformanceConfig;
import com.dataforge.core.service.DataForgeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;
import org.junit.jupiter.api.Timeout;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.assertj.core.api.Assertions.*;

/**
 * 数据生成性能测试套件
 */
@DisplayName("数据生成性能测试套件")
class DataGenerationPerformanceTest {

    @TempDir
    Path tempDir;

    private DataForgeService dataForgeService;

    @BeforeEach
    void setUp() {
        dataForgeService = new DataForgeService();
    }

    @Test
    @DisplayName("小数据量生成性能测试")
    @Timeout(value = 5, unit = TimeUnit.SECONDS)
    void testSmallDataGenerationPerformance() throws IOException {
        // 创建小数据量配置
        GenerationConfig config = createPerformanceTestConfig(1000);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行数据生成
        dataForgeService.generateData(config);

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证性能要求
        assertThat(duration).isLessThan(2000); // 2秒内完成

        // 验证输出正确性
        Path outputFile = tempDir.resolve("small_perf_test.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(1001); // 1000条数据 + 1行头部

        // 计算生成速度
        double recordsPerSecond = 1000.0 / (duration / 1000.0);
        System.out.printf("小数据量生成速度: %.2f records/second%n", recordsPerSecond);

        // 验证最低性能要求
        assertThat(recordsPerSecond).isGreaterThan(500); // 至少500条/秒
    }

    @Test
    @DisplayName("中等数据量生成性能测试")
    @Timeout(value = 15, unit = TimeUnit.SECONDS)
    void testMediumDataGenerationPerformance() throws IOException {
        // 创建中等数据量配置
        GenerationConfig config = createPerformanceTestConfig(50000);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行数据生成
        dataForgeService.generateData(config);

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证性能要求
        assertThat(duration).isLessThan(12000); // 12秒内完成

        // 验证输出正确性
        Path outputFile = tempDir.resolve("medium_perf_test.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(50001); // 50000条数据 + 1行头部

        // 计算生成速度
        double recordsPerSecond = 50000.0 / (duration / 1000.0);
        System.out.printf("中等数据量生成速度: %.2f records/second%n", recordsPerSecond);

        // 验证最低性能要求
        assertThat(recordsPerSecond).isGreaterThan(4000); // 至少4000条/秒
    }

    @Test
    @DisplayName("大数据量生成性能测试")
    @Timeout(value = 60, unit = TimeUnit.SECONDS)
    void testLargeDataGenerationPerformance() throws IOException {
        // 创建大数据量配置
        GenerationConfig config = createPerformanceTestConfig(200000);

        // 启用并行处理
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setParallelEnabled(true);
        perfConfig.setThreadCount(4);
        perfConfig.setBatchSize(1000);
        config.setPerformanceConfig(perfConfig);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行数据生成
        dataForgeService.generateData(config);

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证性能要求
        assertThat(duration).isLessThan(50000); // 50秒内完成

        // 验证输出正确性
        Path outputFile = tempDir.resolve("large_perf_test.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(200001); // 200000条数据 + 1行头部

        // 计算生成速度
        double recordsPerSecond = 200000.0 / (duration / 1000.0);
        System.out.printf("大数据量生成速度: %.2f records/second%n", recordsPerSecond);

        // 验证最低性能要求
        assertThat(recordsPerSecond).isGreaterThan(5000); // 至少5000条/秒
    }

    @Test
    @DisplayName("并行处理性能对比测试")
    void testParallelProcessingPerformanceComparison() throws IOException {
        int dataCount = 100000;

        // 串行处理测试
        GenerationConfig serialConfig = createPerformanceTestConfig(dataCount);
        serialConfig.getOutputConfig().setFilePath(tempDir.resolve("serial_perf_test.csv").toString());

        PerformanceConfig serialPerfConfig = new PerformanceConfig();
        serialPerfConfig.setParallelEnabled(false);
        serialConfig.setPerformanceConfig(serialPerfConfig);

        long serialStartTime = System.currentTimeMillis();
        dataForgeService.generateData(serialConfig);
        long serialEndTime = System.currentTimeMillis();
        long serialDuration = serialEndTime - serialStartTime;

        // 并行处理测试
        GenerationConfig parallelConfig = createPerformanceTestConfig(dataCount);
        parallelConfig.getOutputConfig().setFilePath(tempDir.resolve("parallel_perf_test.csv").toString());

        PerformanceConfig parallelPerfConfig = new PerformanceConfig();
        parallelPerfConfig.setParallelEnabled(true);
        parallelPerfConfig.setThreadCount(4);
        parallelPerfConfig.setBatchSize(1000);
        parallelConfig.setPerformanceConfig(parallelPerfConfig);

        long parallelStartTime = System.currentTimeMillis();
        dataForgeService.generateData(parallelConfig);
        long parallelEndTime = System.currentTimeMillis();
        long parallelDuration = parallelEndTime - parallelStartTime;

        // 计算性能提升
        double speedupRatio = (double) serialDuration / parallelDuration;
        System.out.printf("串行处理时间: %d ms%n", serialDuration);
        System.out.printf("并行处理时间: %d ms%n", parallelDuration);
        System.out.printf("性能提升倍数: %.2fx%n", speedupRatio);

        // 验证并行处理确实有性能提升
        assertThat(speedupRatio).isGreaterThan(1.5); // 至少1.5倍提升

        // 验证输出正确性
        List<String> serialLines = Files.readAllLines(tempDir.resolve("serial_perf_test.csv"));
        List<String> parallelLines = Files.readAllLines(tempDir.resolve("parallel_perf_test.csv"));

        assertThat(serialLines).hasSize(dataCount + 1);
        assertThat(parallelLines).hasSize(dataCount + 1);
    }

    @Test
    @DisplayName("内存使用性能测试")
    void testMemoryUsagePerformance() throws IOException {
        // 记录初始内存使用
        Runtime runtime = Runtime.getRuntime();
        runtime.gc(); // 强制垃圾回收
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();

        // 创建大数据量配置
        GenerationConfig config = createPerformanceTestConfig(100000);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 记录峰值内存使用
        runtime.gc(); // 强制垃圾回收
        long peakMemory = runtime.totalMemory() - runtime.freeMemory();

        // 计算内存使用量
        long memoryUsed = peakMemory - initialMemory;
        double memoryPerRecord = (double) memoryUsed / 100000;

        System.out.printf("初始内存使用: %d bytes%n", initialMemory);
        System.out.printf("峰值内存使用: %d bytes%n", peakMemory);
        System.out.printf("内存使用量: %d bytes (%.2f MB)%n", memoryUsed, memoryUsed / 1024.0 / 1024.0);
        System.out.printf("每条记录内存使用: %.2f bytes%n", memoryPerRecord);

        // 验证内存使用合理
        assertThat(memoryPerRecord).isLessThan(1000); // 每条记录不超过1KB内存
        assertThat(memoryUsed).isLessThan(500 * 1024 * 1024); // 总内存使用不超过500MB
    }

    @Test
    @DisplayName("不同输出格式性能对比测试")
    void testOutputFormatPerformanceComparison() throws IOException {
        int dataCount = 50000;
        String[] formats = { "csv", "json", "xml" };
        long[] durations = new long[formats.length];

        for (int i = 0; i < formats.length; i++) {
            String format = formats[i];

            // 创建配置
            GenerationConfig config = createPerformanceTestConfig(dataCount);
            config.getOutputConfig().setFormat(format);
            config.getOutputConfig().setFilePath(tempDir.resolve("format_perf_test." + format).toString());

            // 记录时间
            long startTime = System.currentTimeMillis();
            dataForgeService.generateData(config);
            long endTime = System.currentTimeMillis();

            durations[i] = endTime - startTime;

            System.out.printf("%s格式生成时间: %d ms%n", format.toUpperCase(), durations[i]);

            // 验证输出文件存在
            Path outputFile = tempDir.resolve("format_perf_test." + format);
            assertThat(outputFile).exists();
        }

        // 验证所有格式都在合理时间内完成
        for (long duration : durations) {
            assertThat(duration).isLessThan(30000); // 30秒内完成
        }

        // CSV通常应该是最快的
        assertThat(durations[0]).isLessThanOrEqualTo(durations[1]); // CSV <= JSON
        assertThat(durations[0]).isLessThanOrEqualTo(durations[2]); // CSV <= XML
    }

    @Test
    @DisplayName("复杂字段类型性能测试")
    void testComplexFieldTypePerformance() throws IOException {
        // 创建包含复杂字段类型的配置
        GenerationConfig config = new GenerationConfig();
        config.setCount(20000);

        // 添加各种复杂字段类型
        FieldConfig idCardField = new FieldConfig("idcard", "idcard");
        FieldConfig bankCardField = new FieldConfig("bankcard", "bankcard");
        FieldConfig usccField = new FieldConfig("uscc", "uscc");
        FieldConfig uuidField = new FieldConfig("uuid", "uuid");
        FieldConfig emailField = new FieldConfig("email", "email");
        FieldConfig phoneField = new FieldConfig("phone", "phone");

        config.setFields(Arrays.asList(idCardField, bankCardField, usccField,
                uuidField, emailField, phoneField));

        // 设置输出配置
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve("complex_field_perf_test.csv").toString());
        config.setOutputConfig(outputConfig);

        // 记录时间
        long startTime = System.currentTimeMillis();
        dataForgeService.generateData(config);
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证性能
        assertThat(duration).isLessThan(20000); // 20秒内完成

        // 计算生成速度
        double recordsPerSecond = 20000.0 / (duration / 1000.0);
        System.out.printf("复杂字段类型生成速度: %.2f records/second%n", recordsPerSecond);

        // 验证最低性能要求
        assertThat(recordsPerSecond).isGreaterThan(1000); // 至少1000条/秒

        // 验证输出正确性
        Path outputFile = tempDir.resolve("complex_field_perf_test.csv");
        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(20001); // 20000条数据 + 1行头部

        // 验证数据格式正确
        String[] firstDataFields = lines.get(1).split(",");
        assertThat(firstDataFields).hasSize(6);
        assertThat(firstDataFields[0]).matches("\\d{18}"); // idcard
        assertThat(firstDataFields[3]).matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"); // uuid
    }

    @Test
    @DisplayName("批处理大小性能优化测试")
    void testBatchSizePerformanceOptimization() throws IOException {
        int dataCount = 50000;
        int[] batchSizes = { 100, 500, 1000, 2000, 5000 };
        long[] durations = new long[batchSizes.length];

        for (int i = 0; i < batchSizes.length; i++) {
            int batchSize = batchSizes[i];

            // 创建配置
            GenerationConfig config = createPerformanceTestConfig(dataCount);
            config.getOutputConfig().setFilePath(tempDir.resolve("batch_perf_test_" + batchSize + ".csv").toString());

            PerformanceConfig perfConfig = new PerformanceConfig();
            perfConfig.setParallelEnabled(true);
            perfConfig.setThreadCount(4);
            perfConfig.setBatchSize(batchSize);
            config.setPerformanceConfig(perfConfig);

            // 记录时间
            long startTime = System.currentTimeMillis();
            dataForgeService.generateData(config);
            long endTime = System.currentTimeMillis();

            durations[i] = endTime - startTime;

            System.out.printf("批处理大小 %d: %d ms%n", batchSize, durations[i]);

            // 验证输出正确性
            Path outputFile = tempDir.resolve("batch_perf_test_" + batchSize + ".csv");
            List<String> lines = Files.readAllLines(outputFile);
            assertThat(lines).hasSize(dataCount + 1);
        }

        // 验证所有批处理大小都在合理时间内完成
        for (long duration : durations) {
            assertThat(duration).isLessThan(30000); // 30秒内完成
        }

        // 找到最优批处理大小
        int optimalIndex = 0;
        for (int i = 1; i < durations.length; i++) {
            if (durations[i] < durations[optimalIndex]) {
                optimalIndex = i;
            }
        }

        System.out.printf("最优批处理大小: %d (耗时: %d ms)%n",
                batchSizes[optimalIndex], durations[optimalIndex]);
    }

    @Test
    @DisplayName("流式输出性能测试")
    void testStreamingOutputPerformance() throws IOException {
        int dataCount = 100000;

        // 非流式输出测试
        GenerationConfig nonStreamingConfig = createPerformanceTestConfig(dataCount);
        nonStreamingConfig.getOutputConfig().setFilePath(tempDir.resolve("non_streaming_perf_test.csv").toString());
        nonStreamingConfig.getOutputConfig().setStreamingEnabled(false);

        long nonStreamingStartTime = System.currentTimeMillis();
        dataForgeService.generateData(nonStreamingConfig);
        long nonStreamingEndTime = System.currentTimeMillis();
        long nonStreamingDuration = nonStreamingEndTime - nonStreamingStartTime;

        // 流式输出测试
        GenerationConfig streamingConfig = createPerformanceTestConfig(dataCount);
        streamingConfig.getOutputConfig().setFilePath(tempDir.resolve("streaming_perf_test.csv").toString());
        streamingConfig.getOutputConfig().setStreamingEnabled(true);
        streamingConfig.getOutputConfig().setBatchSize(1000);

        long streamingStartTime = System.currentTimeMillis();
        dataForgeService.generateDataStreaming(streamingConfig);
        long streamingEndTime = System.currentTimeMillis();
        long streamingDuration = streamingEndTime - streamingStartTime;

        System.out.printf("非流式输出时间: %d ms%n", nonStreamingDuration);
        System.out.printf("流式输出时间: %d ms%n", streamingDuration);

        // 验证两种方式都在合理时间内完成
        assertThat(nonStreamingDuration).isLessThan(60000); // 60秒内完成
        assertThat(streamingDuration).isLessThan(60000); // 60秒内完成

        // 验证输出正确性
        List<String> nonStreamingLines = Files.readAllLines(tempDir.resolve("non_streaming_perf_test.csv"));
        List<String> streamingLines = Files.readAllLines(tempDir.resolve("streaming_perf_test.csv"));

        assertThat(nonStreamingLines).hasSize(dataCount + 1);
        assertThat(streamingLines).hasSize(dataCount + 1);
    }

    /**
     * 创建性能测试配置
     */
    private GenerationConfig createPerformanceTestConfig(int count) {
        GenerationConfig config = new GenerationConfig();
        config.setCount(count);

        // 添加基本字段
        FieldConfig nameField = new FieldConfig("name", "name");
        FieldConfig ageField = new FieldConfig("age", "age");
        FieldConfig emailField = new FieldConfig("email", "email");
        FieldConfig phoneField = new FieldConfig("phone", "phone");

        config.setFields(Arrays.asList(nameField, ageField, emailField, phoneField));

        // 设置输出配置
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve(
                count <= 1000 ? "small_perf_test.csv" : count <= 50000 ? "medium_perf_test.csv" : "large_perf_test.csv")
                .toString());
        outputConfig.setIncludeHeader(true);
        config.setOutputConfig(outputConfig);

        return config;
    }
}