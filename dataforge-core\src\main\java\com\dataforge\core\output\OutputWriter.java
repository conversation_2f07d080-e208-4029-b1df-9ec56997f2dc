package com.dataforge.core.output;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * 输出器接口
 * 
 * 定义数据输出的标准接口，支持多种输出格式和流式输出。
 * 所有输出器实现都必须实现此接口。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface OutputWriter {

    /**
     * 获取输出器类型
     * 
     * @return 输出器类型标识
     */
    String getType();

    /**
     * 初始化输出器
     * 
     * @param config 输出配置
     * @throws IOException 当初始化失败时抛出
     */
    void initialize(OutputConfig config) throws IOException;

    /**
     * 写入单条记录
     * 
     * @param record 记录数据
     * @throws IOException 当写入失败时抛出
     */
    void writeRecord(Map<String, Object> record) throws IOException;

    /**
     * 批量写入记录
     * 
     * @param records 记录列表
     * @throws IOException 当写入失败时抛出
     */
    void writeRecords(List<Map<String, Object>> records) throws IOException;

    /**
     * 写入表头
     * 
     * @param headers 表头字段列表
     * @throws IOException 当写入失败时抛出
     */
    void writeHeader(List<String> headers) throws IOException;

    /**
     * 刷新输出缓冲区
     * 
     * @throws IOException 当刷新失败时抛出
     */
    void flush() throws IOException;

    /**
     * 关闭输出器并释放资源
     * 
     * @throws IOException 当关闭失败时抛出
     */
    void close() throws IOException;

    /**
     * 检查输出器是否已初始化
     * 
     * @return 如果已初始化返回true，否则返回false
     */
    boolean isInitialized();

    /**
     * 检查输出器是否已关闭
     * 
     * @return 如果已关闭返回true，否则返回false
     */
    boolean isClosed();

    /**
     * 获取已写入的记录数量
     * 
     * @return 已写入的记录数量
     */
    long getWrittenRecordCount();

    /**
     * 检查是否支持流式输出
     * 
     * @return 如果支持流式输出返回true，否则返回false
     */
    default boolean supportsStreaming() {
        return true;
    }

    /**
     * 检查是否支持表头
     * 
     * @return 如果支持表头返回true，否则返回false
     */
    default boolean supportsHeader() {
        return true;
    }

    /**
     * 获取输出器描述
     * 
     * @return 输出器描述信息
     */
    default String getDescription() {
        return getType() + " output writer";
    }
}