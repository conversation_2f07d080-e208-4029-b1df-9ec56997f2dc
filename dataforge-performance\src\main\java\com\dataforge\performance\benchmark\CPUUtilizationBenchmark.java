package com.dataforge.performance.benchmark;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.PerformanceConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.service.DataForgeService;
import com.dataforge.core.service.GeneratorFactory;
import com.dataforge.core.service.DataRelationManager;
import com.dataforge.core.relation.ConsistencyManager;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.infra.Blackhole;

import java.lang.management.ManagementFactory;
import com.sun.management.OperatingSystemMXBean;
import java.util.concurrent.TimeUnit;

/**
 * CPU利用率性能基准测试
 * 
 * 测试数据生成过程中的CPU使用情况和多核利用率
 */
@BenchmarkMode({Mode.Throughput, Mode.AverageTime})
@OutputTimeUnit(TimeUnit.SECONDS)
@State(Scope.Benchmark)
@Fork(1)
@Warmup(iterations = 2, time = 1, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 3, time = 3, timeUnit = TimeUnit.SECONDS)
public class CPUUtilizationBenchmark {

    private DataForgeService dataForgeService;
    private OperatingSystemMXBean osBean;

    @Setup(Level.Trial)
    public void setupTrial() {
        GeneratorFactory generatorFactory = new GeneratorFactory();
        DataRelationManager relationManager = new DataRelationManager();
        ConsistencyManager consistencyManager = new ConsistencyManager();
        dataForgeService = new DataForgeService(generatorFactory, relationManager, consistencyManager);
        
        osBean = (OperatingSystemMXBean) ManagementFactory.getOperatingSystemMXBean();
    }

    /**
     * 单线程CPU利用率测试
     */
    @Benchmark
    public void benchmarkSingleThreadCPUUsage(Blackhole bh) {
        double cpuBefore = osBean.getProcessCpuLoad();
        
        GenerationConfig config = createCPUTestConfig("idcard", 50000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(false);
        perfConfig.setBatchSize(5000);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        double cpuAfter = osBean.getProcessCpuLoad();
        double cpuUsage = cpuAfter - cpuBefore;
        
        bh.consume(result);
        bh.consume(cpuUsage);
    }

    /**
     * 多线程CPU利用率测试
     */
    @Benchmark
    public void benchmarkMultiThreadCPUUsage(Blackhole bh) {
        double cpuBefore = osBean.getProcessCpuLoad();
        
        GenerationConfig config = createCPUTestConfig("email", 50000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(4);
        perfConfig.setBatchSize(2500);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        double cpuAfter = osBean.getProcessCpuLoad();
        double cpuUsage = cpuAfter - cpuBefore;
        
        bh.consume(result);
        bh.consume(cpuUsage);
    }

    /**
     * 不同线程数的CPU利用率对比
     */
    @Param({"1", "2", "4", "6", "8"})
    public int threadCount;

    @Benchmark
    public void benchmarkThreadCountCPUUsage(Blackhole bh) {
        double cpuBefore = osBean.getProcessCpuLoad();
        
        GenerationConfig config = createCPUTestConfig("uuid", 30000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(threadCount > 1);
        perfConfig.setThreadPoolSize(threadCount);
        perfConfig.setBatchSize(5000);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        double cpuAfter = osBean.getProcessCpuLoad();
        double cpuUsage = cpuAfter - cpuBefore;
        
        bh.consume(result);
        bh.consume(cpuUsage);
    }

    /**
     * CPU密集型数据生成测试
     */
    @Benchmark
    public void benchmarkCPUIntensiveGeneration(Blackhole bh) {
        double cpuBefore = osBean.getProcessCpuLoad();
        
        // 身份证号生成是CPU密集型操作
        GenerationConfig config = createCPUTestConfig("idcard", 20000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(4);
        perfConfig.setBatchSize(1000);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        double cpuAfter = osBean.getProcessCpuLoad();
        double cpuUsage = cpuAfter - cpuBefore;
        
        bh.consume(result);
        bh.consume(cpuUsage);
    }

    /**
     * 轻量级数据生成CPU使用测试
     */
    @Benchmark
    public void benchmarkLightweightCPUUsage(Blackhole bh) {
        double cpuBefore = osBean.getProcessCpuLoad();
        
        // UUID生成相对轻量
        GenerationConfig config = createCPUTestConfig("uuid", 100000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(4);
        perfConfig.setBatchSize(10000);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        double cpuAfter = osBean.getProcessCpuLoad();
        double cpuUsage = cpuAfter - cpuBefore;
        
        bh.consume(result);
        bh.consume(cpuUsage);
    }

    /**
     * 系统总体CPU利用率测试
     */
    @Benchmark
    public void benchmarkSystemCPUUsage(Blackhole bh) {
        double systemCpuBefore = osBean.getSystemCpuLoad();
        
        GenerationConfig config = createCPUTestConfig("name", 40000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(6);
        perfConfig.setBatchSize(2000);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        double systemCpuAfter = osBean.getSystemCpuLoad();
        double systemCpuUsage = systemCpuAfter - systemCpuBefore;
        
        bh.consume(result);
        bh.consume(systemCpuUsage);
    }

    /**
     * CPU效率测试 - 吞吐量与CPU使用率的比值
     */
    @Benchmark
    public void benchmarkCPUEfficiency(Blackhole bh) {
        double cpuBefore = osBean.getProcessCpuLoad();
        long startTime = System.currentTimeMillis();
        
        GenerationConfig config = createCPUTestConfig("phone", 25000);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(true);
        perfConfig.setThreadPoolSize(4);
        perfConfig.setBatchSize(2500);
        config.setPerformanceConfig(perfConfig);
        
        DataForgeService.GenerationResult result = dataForgeService.generateData(config);
        
        long endTime = System.currentTimeMillis();
        double cpuAfter = osBean.getProcessCpuLoad();
        
        double duration = (endTime - startTime) / 1000.0; // 秒
        double cpuUsage = cpuAfter - cpuBefore;
        double throughput = 25000.0 / duration; // 记录/秒
        double efficiency = cpuUsage > 0 ? throughput / cpuUsage : 0; // 效率指标
        
        bh.consume(result);
        bh.consume(efficiency);
    }

    /**
     * 长时间运行CPU稳定性测试
     */
    @Benchmark
    public void benchmarkLongRunningCPUStability(Blackhole bh) {
        double[] cpuReadings = new double[5];
        
        for (int i = 0; i < 5; i++) {
            double cpuBefore = osBean.getProcessCpuLoad();
            
            GenerationConfig config = createCPUTestConfig("email", 10000);
            
            PerformanceConfig perfConfig = new PerformanceConfig();
            perfConfig.setEnableParallel(true);
            perfConfig.setThreadPoolSize(4);
            perfConfig.setBatchSize(2000);
            config.setPerformanceConfig(perfConfig);
            
            DataForgeService.GenerationResult result = dataForgeService.generateData(config);
            
            double cpuAfter = osBean.getProcessCpuLoad();
            cpuReadings[i] = cpuAfter - cpuBefore;
            
            bh.consume(result);
            
            // 短暂休息以模拟长时间运行
            try {
                Thread.sleep(100);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        // 计算CPU使用的标准差以评估稳定性
        double mean = 0;
        for (double reading : cpuReadings) {
            mean += reading;
        }
        mean /= cpuReadings.length;
        
        double variance = 0;
        for (double reading : cpuReadings) {
            variance += Math.pow(reading - mean, 2);
        }
        variance /= cpuReadings.length;
        double stdDev = Math.sqrt(variance);
        
        bh.consume(stdDev);
    }

    /**
     * 创建CPU测试配置
     */
    private GenerationConfig createCPUTestConfig(String dataType, int count) {
        GenerationConfig config = new GenerationConfig();
        config.setDataType(dataType);
        config.setCount(count);
        config.setSeed(12345L);
        
        // 禁用验证以专注于CPU使用
        ValidationConfig validationConfig = new ValidationConfig();
        validationConfig.setEnabled(false);
        config.setValidationConfig(validationConfig);
        
        // 默认性能配置
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(false);
        perfConfig.setBatchSize(5000);
        perfConfig.setCacheEnabled(false); // 禁用缓存以增加CPU负载
        config.setPerformanceConfig(perfConfig);
        
        return config;
    }
}