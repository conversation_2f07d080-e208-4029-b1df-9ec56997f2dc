package com.dataforge.core.model;

import com.dataforge.core.service.DataRelationManager;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 数据生成上下文
 * 
 * 封装数据生成过程中需要的所有上下文信息，包括生成参数、随机数生成器、
 * 数据关联管理器等。提供线程安全的参数访问和状态管理。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class GenerationContext {

    private final Map<String, Object> parameters;
    private final Random random;
    private final DataRelationManager relationManager;
    private final long seed;
    private final String requestId;
    private final long createdTime;

    /**
     * 私有构造函数，使用Builder模式创建实例
     */
    private GenerationContext(Builder builder) {
        this.parameters = new HashMap<>(builder.parameters);
        this.seed = builder.seed;
        this.random = builder.seed != 0 ? new Random(builder.seed) : ThreadLocalRandom.current();
        this.relationManager = builder.relationManager != null ? builder.relationManager : new DataRelationManager();
        this.requestId = builder.requestId != null ? builder.requestId : generateRequestId();
        this.createdTime = System.currentTimeMillis();
    }

    /**
     * 获取参数值
     * 
     * @param key 参数键
     * @return 参数值，如果不存在返回null
     */
    public Object getParameter(String key) {
        return parameters.get(key);
    }

    /**
     * 获取参数值并转换为指定类型
     * 
     * @param key  参数键
     * @param type 目标类型
     * @param <T>  类型参数
     * @return 转换后的参数值，如果不存在或转换失败返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, Class<T> type) {
        Object value = parameters.get(key);
        if (value == null) {
            return null;
        }

        if (type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }

        // 尝试基本类型转换
        return convertValue(value, type);
    }

    /**
     * 获取参数值，如果不存在则返回默认值
     * 
     * @param key          参数键
     * @param defaultValue 默认值
     * @param <T>          类型参数
     * @return 参数值或默认值
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, T defaultValue) {
        Object value = parameters.get(key);
        if (value == null) {
            return defaultValue;
        }

        try {
            return (T) value;
        } catch (ClassCastException e) {
            return defaultValue;
        }
    }

    /**
     * 检查是否包含指定参数
     * 
     * @param key 参数键
     * @return 如果包含返回true，否则返回false
     */
    public boolean hasParameter(String key) {
        return parameters.containsKey(key);
    }

    /**
     * 获取所有参数的副本
     * 
     * @return 参数映射的副本
     */
    public Map<String, Object> getAllParameters() {
        return new HashMap<>(parameters);
    }

    /**
     * 获取随机数生成器
     * 
     * @return Random实例
     */
    public Random getRandom() {
        return random;
    }

    /**
     * 获取数据关联管理器
     * 
     * @return DataRelationManager实例
     */
    public DataRelationManager getRelationManager() {
        return relationManager;
    }

    /**
     * 获取随机种子
     * 
     * @return 随机种子，如果未设置返回0
     */
    public long getSeed() {
        return seed;
    }

    /**
     * 获取请求ID
     * 
     * @return 请求ID
     */
    public String getRequestId() {
        return requestId;
    }

    /**
     * 获取创建时间
     * 
     * @return 创建时间戳
     */
    public long getCreatedTime() {
        return createdTime;
    }

    /**
     * 创建子上下文
     * 子上下文继承父上下文的参数和关联管理器，但可以添加额外参数
     * 
     * @param additionalParameters 额外参数
     * @return 子上下文
     */
    public GenerationContext createChildContext(Map<String, Object> additionalParameters) {
        Builder builder = new Builder()
                .withParameters(this.parameters)
                .withSeed(this.seed)
                .withRelationManager(this.relationManager)
                .withRequestId(this.requestId);

        if (additionalParameters != null) {
            additionalParameters.forEach(builder::withParameter);
        }

        return builder.build();
    }

    /**
     * 值类型转换
     */
    @SuppressWarnings("unchecked")
    private <T> T convertValue(Object value, Class<T> type) {
        if (value == null) {
            return null;
        }

        String stringValue = value.toString();

        try {
            if (type == String.class) {
                return (T) stringValue;
            } else if (type == Integer.class || type == int.class) {
                return (T) Integer.valueOf(stringValue);
            } else if (type == Long.class || type == long.class) {
                return (T) Long.valueOf(stringValue);
            } else if (type == Double.class || type == double.class) {
                return (T) Double.valueOf(stringValue);
            } else if (type == Float.class || type == float.class) {
                return (T) Float.valueOf(stringValue);
            } else if (type == Boolean.class || type == boolean.class) {
                return (T) Boolean.valueOf(stringValue);
            }
        } catch (IllegalArgumentException e) {
            // 转换失败，返回null
        }

        return null;
    }

    /**
     * 生成请求ID
     */
    private String generateRequestId() {
        return "req_" + System.currentTimeMillis() + "_" + ThreadLocalRandom.current().nextInt(10000);
    }

    @Override
    public String toString() {
        return String.format("GenerationContext{requestId='%s', parametersCount=%d, seed=%d, createdTime=%d}",
                requestId, parameters.size(), seed, createdTime);
    }

    /**
     * Builder模式构建器
     */
    public static class Builder {
        private final Map<String, Object> parameters = new HashMap<>();
        private long seed = 0;
        private DataRelationManager relationManager;
        private String requestId;

        /**
         * 添加参数
         * 
         * @param key   参数键
         * @param value 参数值
         * @return Builder实例
         */
        public Builder withParameter(String key, Object value) {
            if (key != null && value != null) {
                this.parameters.put(key, value);
            }
            return this;
        }

        /**
         * 批量添加参数
         * 
         * @param parameters 参数映射
         * @return Builder实例
         */
        public Builder withParameters(Map<String, Object> parameters) {
            if (parameters != null) {
                this.parameters.putAll(parameters);
            }
            return this;
        }

        /**
         * 设置随机种子
         * 
         * @param seed 随机种子
         * @return Builder实例
         */
        public Builder withSeed(long seed) {
            this.seed = seed;
            return this;
        }

        /**
         * 设置数据关联管理器
         * 
         * @param relationManager 数据关联管理器
         * @return Builder实例
         */
        public Builder withRelationManager(DataRelationManager relationManager) {
            this.relationManager = relationManager;
            return this;
        }

        /**
         * 设置请求ID
         * 
         * @param requestId 请求ID
         * @return Builder实例
         */
        public Builder withRequestId(String requestId) {
            this.requestId = requestId;
            return this;
        }

        /**
         * 从现有上下文复制配置
         * 
         * @param context 源上下文
         * @return Builder实例
         */
        public Builder copyFrom(GenerationContext context) {
            if (context != null) {
                this.parameters.putAll(context.parameters);
                this.seed = context.seed;
                this.relationManager = context.relationManager;
                this.requestId = context.requestId;
            }
            return this;
        }

        /**
         * 使用新的随机种子（用于线程安全）
         * 
         * @return Builder实例
         */
        public Builder withNewRandomSeed() {
            this.seed = ThreadLocalRandom.current().nextLong();
            return this;
        }

        /**
         * 构建GenerationContext实例
         * 
         * @return GenerationContext实例
         */
        public GenerationContext build() {
            return new GenerationContext(this);
        }
    }
}