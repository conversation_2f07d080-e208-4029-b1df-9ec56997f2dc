package com.dataforge.core.relation;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 一致性修复结果
 * 
 * 封装数据一致性修复的结果，包括修复后的值和修复信息。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ConsistencyFixResult {

    private final boolean fixed;
    private final Map<String, Object> fixedValues;
    private final List<String> appliedFixes;
    private final List<String> failedFixes;
    private final String message;

    /**
     * 私有构造函数
     * 
     * @param fixed        是否已修复
     * @param fixedValues  修复后的值
     * @param appliedFixes 应用的修复列表
     * @param failedFixes  失败的修复列表
     * @param message      结果消息
     */
    private ConsistencyFixResult(boolean fixed, Map<String, Object> fixedValues,
            List<String> appliedFixes, List<String> failedFixes,
            String message) {
        this.fixed = fixed;
        this.fixedValues = fixedValues != null ? Collections.unmodifiableMap(new HashMap<>(fixedValues))
                : Collections.emptyMap();
        this.appliedFixes = appliedFixes != null ? List.copyOf(appliedFixes) : Collections.emptyList();
        this.failedFixes = failedFixes != null ? List.copyOf(failedFixes) : Collections.emptyList();
        this.message = message;
    }

    /**
     * 创建成功修复结果
     * 
     * @param fixedValues  修复后的值
     * @param appliedFixes 应用的修复列表
     * @return 成功修复结果
     */
    public static ConsistencyFixResult success(Map<String, Object> fixedValues,
            List<String> appliedFixes) {
        return new ConsistencyFixResult(true, fixedValues, appliedFixes,
                Collections.emptyList(), "修复成功");
    }

    /**
     * 创建部分成功修复结果
     * 
     * @param fixedValues  修复后的值
     * @param appliedFixes 应用的修复列表
     * @param failedFixes  失败的修复列表
     * @return 部分成功修复结果
     */
    public static ConsistencyFixResult partialSuccess(Map<String, Object> fixedValues,
            List<String> appliedFixes,
            List<String> failedFixes) {
        return new ConsistencyFixResult(true, fixedValues, appliedFixes,
                failedFixes, "部分修复成功");
    }

    /**
     * 创建修复失败结果
     * 
     * @param failedFixes 失败的修复列表
     * @return 修复失败结果
     */
    public static ConsistencyFixResult failure(List<String> failedFixes) {
        return new ConsistencyFixResult(false, Collections.emptyMap(),
                Collections.emptyList(), failedFixes, "修复失败");
    }

    /**
     * 创建无需修复结果
     * 
     * @param message 消息
     * @return 无需修复结果
     */
    public static ConsistencyFixResult noActionNeeded(String message) {
        return new ConsistencyFixResult(false, Collections.emptyMap(),
                Collections.emptyList(), Collections.emptyList(), message);
    }

    /**
     * 检查是否已修复
     * 
     * @return 如果已修复返回true，否则返回false
     */
    public boolean isFixed() {
        return fixed;
    }

    /**
     * 检查是否有失败的修复
     * 
     * @return 如果有失败的修复返回true，否则返回false
     */
    public boolean hasFailed() {
        return !failedFixes.isEmpty();
    }

    /**
     * 检查是否为部分成功
     * 
     * @return 如果为部分成功返回true，否则返回false
     */
    public boolean isPartialSuccess() {
        return fixed && hasFailed();
    }

    /**
     * 获取修复后的值
     * 
     * @return 修复后的值映射
     */
    public Map<String, Object> getFixedValues() {
        return fixedValues;
    }

    /**
     * 获取应用的修复列表
     * 
     * @return 应用的修复列表
     */
    public List<String> getAppliedFixes() {
        return appliedFixes;
    }

    /**
     * 获取失败的修复列表
     * 
     * @return 失败的修复列表
     */
    public List<String> getFailedFixes() {
        return failedFixes;
    }

    /**
     * 获取结果消息
     * 
     * @return 结果消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 获取应用的修复数量
     * 
     * @return 应用的修复数量
     */
    public int getAppliedFixCount() {
        return appliedFixes.size();
    }

    /**
     * 获取失败的修复数量
     * 
     * @return 失败的修复数量
     */
    public int getFailedFixCount() {
        return failedFixes.size();
    }

    /**
     * 获取修复后的字段数量
     * 
     * @return 修复后的字段数量
     */
    public int getFixedFieldCount() {
        return fixedValues.size();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        ConsistencyFixResult that = (ConsistencyFixResult) o;
        return fixed == that.fixed &&
                Objects.equals(fixedValues, that.fixedValues) &&
                Objects.equals(appliedFixes, that.appliedFixes) &&
                Objects.equals(failedFixes, that.failedFixes) &&
                Objects.equals(message, that.message);
    }

    @Override
    public int hashCode() {
        return Objects.hash(fixed, fixedValues, appliedFixes, failedFixes, message);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder();
        sb.append("ConsistencyFixResult{");
        sb.append("fixed=").append(fixed);

        if (!fixedValues.isEmpty()) {
            sb.append(", fixedFields=").append(fixedValues.size());
        }

        if (!appliedFixes.isEmpty()) {
            sb.append(", appliedFixes=").append(appliedFixes.size());
        }

        if (!failedFixes.isEmpty()) {
            sb.append(", failedFixes=").append(failedFixes.size());
        }

        if (message != null) {
            sb.append(", message='").append(message).append("'");
        }

        sb.append("}");
        return sb.toString();
    }
}