package com.dataforge.core.test;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.TestInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

/**
 * 基础测试类，提供通用的测试配置和工具方法
 */
@SpringBootTest
@ActiveProfiles("test")
public abstract class BaseTest {

    protected final Logger logger = LoggerFactory.getLogger(getClass());

    @BeforeEach
    void baseSetUp(TestInfo testInfo) {
        logger.debug("开始执行测试: {}.{}",
                testInfo.getTestClass().map(Class::getSimpleName).orElse("Unknown"),
                testInfo.getDisplayName());
    }

    /**
     * 创建测试用的随机字符串
     */
    protected String randomString(int length) {
        StringBuilder sb = new StringBuilder();
        String chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        for (int i = 0; i < length; i++) {
            sb.append(chars.charAt((int) (Math.random() * chars.length())));
        }
        return sb.toString();
    }

    /**
     * 创建测试用的随机数字
     */
    protected int randomInt(int min, int max) {
        return (int) (Math.random() * (max - min + 1)) + min;
    }

    /**
     * 等待指定毫秒数
     */
    protected void waitFor(long milliseconds) {
        try {
            Thread.sleep(milliseconds);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("测试等待被中断", e);
        }
    }
}