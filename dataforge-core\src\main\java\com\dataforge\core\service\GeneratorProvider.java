package com.dataforge.core.service;

import com.dataforge.core.generator.DataGenerator;

import java.util.Collection;

/**
 * 生成器提供者接口
 * 
 * 用于SPI扩展机制，允许第三方模块提供自定义的数据生成器。
 * 实现此接口的类将通过Java ServiceLoader机制自动发现和加载。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface GeneratorProvider {

    /**
     * 获取提供者名称
     * 
     * @return 提供者名称
     */
    String getProviderName();

    /**
     * 获取提供者版本
     * 
     * @return 版本信息
     */
    String getVersion();

    /**
     * 获取提供者描述
     * 
     * @return 描述信息
     */
    String getDescription();

    /**
     * 获取提供的生成器列表
     * 
     * @return 生成器集合
     */
    Collection<DataGenerator<?>> getGenerators();

    /**
     * 检查提供者是否可用
     * 
     * @return 如果可用返回true，否则返回false
     */
    default boolean isAvailable() {
        return true;
    }

    /**
     * 初始化提供者
     * 在加载生成器之前调用
     * 
     * @throws Exception 如果初始化失败
     */
    default void initialize() throws Exception {
        // 默认实现为空，子类可以根据需要重写
    }

    /**
     * 清理提供者资源
     * 在提供者不再使用时调用
     */
    default void cleanup() {
        // 默认实现为空，子类可以根据需要重写
    }

    /**
     * 获取提供者优先级
     * 数值越小优先级越高
     * 
     * @return 优先级值
     */
    default int getPriority() {
        return 1000;
    }
}