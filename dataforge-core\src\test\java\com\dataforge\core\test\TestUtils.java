package com.dataforge.core.test;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.model.ValidationConfig;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

/**
 * 测试工具类，提供创建测试数据的便捷方法
 */
public class TestUtils {

    private static final Random RANDOM = new Random();

    /**
     * 创建测试用的GenerationConfig
     */
    public static GenerationConfig createTestGenerationConfig(String dataType, int count) {
        GenerationConfig config = new GenerationConfig();
        config.setDataType(dataType);
        config.setCount(count);
        config.setParameters(new HashMap<>());
        config.setOutputConfig(createTestOutputConfig());
        config.setValidationConfig(createTestValidationConfig());
        return config;
    }

    /**
     * 创建测试用的OutputConfig
     */
    public static OutputConfig createTestOutputConfig() {
        OutputConfig config = new OutputConfig();
        config.setFormat("json");
        config.setTarget("stdout");
        config.setOptions(new HashMap<>());
        return config;
    }

    /**
     * 创建测试用的ValidationConfig
     */
    public static ValidationConfig createTestValidationConfig() {
        ValidationConfig config = new ValidationConfig();
        config.setEnabled(true);
        config.setStrictMode(false);
        config.setFailOnValidationError(false);
        return config;
    }

    /**
     * 创建测试用的GenerationContext
     */
    public static GenerationContext createTestGenerationContext() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("test", true);
        return new GenerationContext.Builder()
                .withParameters(parameters)
                .withSeed(System.currentTimeMillis())
                .build();
    }

    /**
     * 创建测试用的GenerationContext，带自定义参数
     */
    public static GenerationContext createTestGenerationContext(Map<String, Object> parameters) {
        Map<String, Object> testParams = new HashMap<>(parameters);
        testParams.put("test", true);
        return new GenerationContext.Builder()
                .withParameters(testParams)
                .withSeed(System.currentTimeMillis())
                .build();
    }

    /**
     * 验证字符串是否为有效的身份证号格式
     */
    public static boolean isValidIdCardFormat(String idCard) {
        if (idCard == null || idCard.length() != 18) {
            return false;
        }
        return idCard.matches("\\d{17}[\\dXx]");
    }

    /**
     * 验证字符串是否为有效的手机号格式
     */
    public static boolean isValidPhoneFormat(String phone) {
        if (phone == null || phone.length() != 11) {
            return false;
        }
        return phone.matches("1[3-9]\\d{9}");
    }

    /**
     * 验证字符串是否为有效的邮箱格式
     */
    public static boolean isValidEmailFormat(String email) {
        if (email == null || email.isEmpty()) {
            return false;
        }
        return email.matches("^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$");
    }

    /**
     * 创建测试用的Map数据
     */
    public static Map<String, Object> createTestDataMap() {
        Map<String, Object> data = new HashMap<>();
        data.put("name", "张三");
        data.put("age", 25);
        data.put("gender", "男");
        data.put("phone", "13800138000");
        data.put("email", "<EMAIL>");
        return data;
    }
}