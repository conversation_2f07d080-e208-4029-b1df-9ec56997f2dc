package com.dataforge.core.relation;

import java.util.Map;
import java.util.Set;

/**
 * 数据一致性规则接口
 * 
 * 定义数据一致性检查和修复的标准方法。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface ConsistencyRule {

    /**
     * 获取规则名称
     * 
     * @return 规则名称
     */
    String getRuleName();

    /**
     * 获取规则描述
     * 
     * @return 规则描述
     */
    String getDescription();

    /**
     * 获取涉及的字段
     * 
     * @return 涉及的字段集合
     */
    Set<String> getInvolvedFields();

    /**
     * 获取规则优先级
     * 数值越小优先级越高
     * 
     * @return 优先级
     */
    int getPriority();

    /**
     * 检查数据一致性
     * 
     * @param fieldValues 字段值映射
     * @return 一致性检查结果
     */
    ConsistencyResult check(Map<String, Object> fieldValues);

    /**
     * 检查是否可以修复
     * 
     * @return 如果可以修复返回true，否则返回false
     */
    boolean canFix();

    /**
     * 修复数据一致性
     * 
     * @param fieldValues 字段值映射
     * @return 修复结果
     */
    ConsistencyFixResult fix(Map<String, Object> fieldValues);

    /**
     * 检查是否启用
     * 
     * @return 如果启用返回true，否则返回false
     */
    default boolean isEnabled() {
        return true;
    }
}