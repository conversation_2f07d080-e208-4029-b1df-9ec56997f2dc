package com.dataforge.core.generator;

import com.dataforge.core.model.GenerationContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 抽象数据生成器基类
 * 
 * 提供数据生成器的通用功能实现，包括参数处理、随机数生成、日志记录等。
 * 具体的数据生成器应该继承此类并实现抽象方法。
 * 
 * @param <T> 生成的数据类型
 * <AUTHOR> Team
 * @version 1.0.0
 */
public abstract class AbstractDataGenerator<T> implements DataGenerator<T> {

    protected final Logger logger = LoggerFactory.getLogger(getClass());
    private final List<GeneratorParameter> supportedParameters;

    /**
     * 构造函数
     */
    protected AbstractDataGenerator() {
        this.supportedParameters = new ArrayList<>();
        initializeParameters();
    }

    /**
     * 初始化支持的参数列表
     * 子类应该重写此方法来定义自己支持的参数
     */
    protected abstract void initializeParameters();

    /**
     * 执行实际的数据生成逻辑
     * 子类必须实现此方法
     * 
     * @param context 生成上下文
     * @return 生成的数据
     */
    protected abstract T doGenerate(GenerationContext context);

    /**
     * 执行实际的数据验证逻辑
     * 子类可以重写此方法来提供自定义验证
     * 
     * @param data 待验证的数据
     * @return 如果数据有效返回true，否则返回false
     */
    protected boolean doValidate(T data) {
        return data != null;
    }

    @Override
    public final T generate(GenerationContext context) {
        logger.debug("开始生成数据，类型: {}", getType());

        try {
            T result = doGenerate(context);

            if (logger.isDebugEnabled()) {
                logger.debug("数据生成完成，类型: {}, 结果: {}", getType(), result);
            }

            return result;
        } catch (Exception e) {
            logger.error("数据生成失败，类型: {}", getType(), e);
            throw new RuntimeException("数据生成失败: " + e.getMessage(), e);
        }
    }

    @Override
    public final boolean validate(T data) {
        try {
            boolean isValid = doValidate(data);

            if (logger.isDebugEnabled()) {
                logger.debug("数据验证完成，类型: {}, 数据: {}, 结果: {}", getType(), data, isValid);
            }

            return isValid;
        } catch (Exception e) {
            logger.error("数据验证失败，类型: {}, 数据: {}", getType(), data, e);
            return false;
        }
    }

    @Override
    public final GeneratorParameter[] getSupportedParameters() {
        return supportedParameters.toArray(new GeneratorParameter[0]);
    }

    /**
     * 添加支持的参数
     * 
     * @param parameter 参数定义
     */
    protected final void addParameter(GeneratorParameter parameter) {
        supportedParameters.add(parameter);
    }

    /**
     * 获取参数值
     * 
     * @param context       生成上下文
     * @param parameterName 参数名称
     * @param defaultValue  默认值
     * @param <V>           参数值类型
     * @return 参数值
     */
    @SuppressWarnings("unchecked")
    protected final <V> V getParameter(GenerationContext context, String parameterName, V defaultValue) {
        Object value = context.getParameter(parameterName);
        if (value == null) {
            return defaultValue;
        }

        try {
            return (V) value;
        } catch (ClassCastException e) {
            logger.warn("参数类型转换失败，参数: {}, 期望类型: {}, 实际值: {}, 使用默认值: {}",
                    parameterName, defaultValue.getClass().getSimpleName(), value, defaultValue);
            return defaultValue;
        }
    }

    /**
     * 获取必需参数值
     * 
     * @param context       生成上下文
     * @param parameterName 参数名称
     * @param <V>           参数值类型
     * @return 参数值
     * @throws IllegalArgumentException 如果参数不存在或为空
     */
    @SuppressWarnings("unchecked")
    protected final <V> V getRequiredParameter(GenerationContext context, String parameterName) {
        Object value = context.getParameter(parameterName);
        if (value == null) {
            throw new IllegalArgumentException("必需参数缺失: " + parameterName);
        }

        return (V) value;
    }

    /**
     * 获取随机数生成器
     * 
     * @return ThreadLocalRandom实例
     */
    protected final ThreadLocalRandom getRandom() {
        return ThreadLocalRandom.current();
    }

    /**
     * 生成指定范围内的随机整数
     * 
     * @param min 最小值（包含）
     * @param max 最大值（不包含）
     * @return 随机整数
     */
    protected final int randomInt(int min, int max) {
        return getRandom().nextInt(min, max);
    }

    /**
     * 生成指定范围内的随机长整数
     * 
     * @param min 最小值（包含）
     * @param max 最大值（不包含）
     * @return 随机长整数
     */
    protected final long randomLong(long min, long max) {
        return getRandom().nextLong(min, max);
    }

    /**
     * 生成指定范围内的随机双精度浮点数
     * 
     * @param min 最小值（包含）
     * @param max 最大值（不包含）
     * @return 随机双精度浮点数
     */
    protected final double randomDouble(double min, double max) {
        return getRandom().nextDouble(min, max);
    }

    /**
     * 从数组中随机选择一个元素
     * 
     * @param array 数组
     * @param <V>   元素类型
     * @return 随机选择的元素
     */
    protected final <V> V randomChoice(V[] array) {
        if (array == null || array.length == 0) {
            return null;
        }
        return array[getRandom().nextInt(array.length)];
    }

    /**
     * 从列表中随机选择一个元素
     * 
     * @param list 列表
     * @param <V>  元素类型
     * @return 随机选择的元素
     */
    protected final <V> V randomChoice(List<V> list) {
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.get(getRandom().nextInt(list.size()));
    }
}