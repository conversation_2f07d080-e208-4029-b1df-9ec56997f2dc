package com.dataforge.core.validation;

import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 记录校验结果
 * 
 * 封装单条记录（包含多个字段）的校验结果。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class RecordValidationResult {

    private final Map<String, ValidationResult> fieldResults;
    private final int validFieldCount;
    private final int invalidFieldCount;
    private final String message;

    /**
     * 构造函数
     * 
     * @param fieldResults      字段校验结果映射
     * @param validFieldCount   有效字段数量
     * @param invalidFieldCount 无效字段数量
     */
    public RecordValidationResult(Map<String, ValidationResult> fieldResults,
            int validFieldCount,
            int invalidFieldCount) {
        this(fieldResults, validFieldCount, invalidFieldCount, null);
    }

    /**
     * 构造函数
     * 
     * @param fieldResults      字段校验结果映射
     * @param validFieldCount   有效字段数量
     * @param invalidFieldCount 无效字段数量
     * @param message           消息
     */
    public RecordValidationResult(Map<String, ValidationResult> fieldResults,
            int validFieldCount,
            int invalidFieldCount,
            String message) {
        this.fieldResults = fieldResults != null ? Collections.unmodifiableMap(new HashMap<>(fieldResults))
                : Collections.emptyMap();
        this.validFieldCount = validFieldCount;
        this.invalidFieldCount = invalidFieldCount;
        this.message = message;
    }

    /**
     * 创建空的记录校验结果
     * 
     * @param message 消息
     * @return 空的记录校验结果
     */
    public static RecordValidationResult empty(String message) {
        return new RecordValidationResult(Collections.emptyMap(), 0, 0, message);
    }

    /**
     * 获取所有字段校验结果
     * 
     * @return 字段校验结果映射
     */
    public Map<String, ValidationResult> getFieldResults() {
        return fieldResults;
    }

    /**
     * 获取指定字段的校验结果
     * 
     * @param fieldName 字段名
     * @return 校验结果，如果字段不存在返回null
     */
    public ValidationResult getFieldResult(String fieldName) {
        return fieldResults.get(fieldName);
    }

    /**
     * 获取有效字段数量
     * 
     * @return 有效字段数量
     */
    public int getValidFieldCount() {
        return validFieldCount;
    }

    /**
     * 获取无效字段数量
     * 
     * @return 无效字段数量
     */
    public int getInvalidFieldCount() {
        return invalidFieldCount;
    }

    /**
     * 获取总字段数量
     * 
     * @return 总字段数量
     */
    public int getTotalFieldCount() {
        return validFieldCount + invalidFieldCount;
    }

    /**
     * 获取字段有效率
     * 
     * @return 字段有效率（0.0-1.0）
     */
    public double getValidFieldRate() {
        int total = getTotalFieldCount();
        return total > 0 ? (double) validFieldCount / total : 0.0;
    }

    /**
     * 获取字段无效率
     * 
     * @return 字段无效率（0.0-1.0）
     */
    public double getInvalidFieldRate() {
        int total = getTotalFieldCount();
        return total > 0 ? (double) invalidFieldCount / total : 0.0;
    }

    /**
     * 获取消息
     * 
     * @return 消息
     */
    public String getMessage() {
        return message;
    }

    /**
     * 检查是否有消息
     * 
     * @return 如果有消息返回true，否则返回false
     */
    public boolean hasMessage() {
        return message != null && !message.trim().isEmpty();
    }

    /**
     * 检查是否为空
     * 
     * @return 如果为空返回true，否则返回false
     */
    public boolean isEmpty() {
        return fieldResults.isEmpty();
    }

    /**
     * 检查记录是否完全有效
     * 
     * @return 如果所有字段都有效返回true，否则返回false
     */
    public boolean isRecordValid() {
        return !isEmpty() && invalidFieldCount == 0;
    }

    /**
     * 检查是否有效（兼容方法）
     * 
     * @return 如果记录有效返回true，否则返回false
     */
    public boolean isValid() {
        return isRecordValid();
    }

    /**
     * 检查记录是否完全无效
     * 
     * @return 如果所有字段都无效返回true，否则返回false
     */
    public boolean isRecordInvalid() {
        return !isEmpty() && validFieldCount == 0;
    }

    /**
     * 检查是否有无效字段
     * 
     * @return 如果有无效字段返回true，否则返回false
     */
    public boolean hasInvalidFields() {
        return invalidFieldCount > 0;
    }

    /**
     * 检查指定字段是否有效
     * 
     * @param fieldName 字段名
     * @return 如果字段有效返回true，否则返回false
     */
    public boolean isFieldValid(String fieldName) {
        ValidationResult result = fieldResults.get(fieldName);
        return result != null && result.isValid();
    }

    /**
     * 检查指定字段是否无效
     * 
     * @param fieldName 字段名
     * @return 如果字段无效返回true，否则返回false
     */
    public boolean isFieldInvalid(String fieldName) {
        ValidationResult result = fieldResults.get(fieldName);
        return result != null && result.isInvalid();
    }

    /**
     * 获取无效字段名列表
     * 
     * @return 无效字段名列表
     */
    public List<String> getInvalidFieldNames() {
        return fieldResults.entrySet().stream()
                .filter(entry -> entry.getValue().isInvalid())
                .map(Map.Entry::getKey)
                .toList();
    }

    /**
     * 获取有效字段名列表
     * 
     * @return 有效字段名列表
     */
    public List<String> getValidFieldNames() {
        return fieldResults.entrySet().stream()
                .filter(entry -> entry.getValue().isValid())
                .map(Map.Entry::getKey)
                .toList();
    }

    /**
     * 获取无效字段的错误消息
     * 
     * @return 字段名到错误消息的映射
     */
    public Map<String, String> getInvalidFieldErrors() {
        Map<String, String> errors = new HashMap<>();

        for (Map.Entry<String, ValidationResult> entry : fieldResults.entrySet()) {
            ValidationResult result = entry.getValue();
            if (result.isInvalid() && result.hasErrorMessage()) {
                errors.put(entry.getKey(), result.getErrorMessage());
            }
        }

        return errors;
    }

    /**
     * 获取摘要信息
     * 
     * @return 摘要信息
     */
    public String getSummary() {
        if (isEmpty()) {
            return hasMessage() ? message : "无字段校验";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("字段总计: ").append(getTotalFieldCount()).append("个");
        sb.append(", 有效: ").append(validFieldCount).append("个");
        sb.append(", 无效: ").append(invalidFieldCount).append("个");
        sb.append(", 有效率: ").append(String.format("%.1f%%", getValidFieldRate() * 100));

        if (hasMessage()) {
            sb.append(" (").append(message).append(")");
        }

        return sb.toString();
    }

    /**
     * 获取详细的错误报告
     * 
     * @return 详细错误报告
     */
    public String getDetailedErrorReport() {
        if (isRecordValid()) {
            return "记录校验通过，所有字段都有效";
        }

        StringBuilder sb = new StringBuilder();
        sb.append("记录校验失败，以下字段存在问题:\n");

        Map<String, String> errors = getInvalidFieldErrors();
        for (Map.Entry<String, String> entry : errors.entrySet()) {
            sb.append("  - ").append(entry.getKey()).append(": ").append(entry.getValue()).append("\n");
        }

        return sb.toString();
    }

    @Override
    public String toString() {
        return String.format("RecordValidationResult{fields=%d, valid=%d, invalid=%d, rate=%.1f%%}",
                getTotalFieldCount(), validFieldCount, invalidFieldCount, getValidFieldRate() * 100);
    }
}