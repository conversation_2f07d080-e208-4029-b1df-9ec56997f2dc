# DataForge 项目设计文档

## 概述

DataForge 是一个基于 Java 的高性能测试数据生成工具，采用分层单体架构设计。系统通过命令行接口提供服务，支持多种数据类型生成、多种输出格式，并具备良好的扩展性和配置管理能力。

## 架构设计

### 整体架构

系统采用经典的分层架构模式，从上到下分为以下层次：

```
┌─────────────────────────────────────────┐
│           表现层 (CLI Layer)              │
│        CommandLineInterface             │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│        配置管理层 (Config Layer)          │
│   ConfigurationManager, ParameterParser │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│        业务逻辑层 (Service Layer)         │
│  DataForgeService, GeneratorFactory,    │
│  DataRelationManager, ValidationService │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│       数据源管理层 (DataSource Layer)     │
│    DataSourceManager, CacheManager      │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│        数据输出层 (Output Layer)          │
│     OutputWriterFactory, Writers        │
└─────────────────────────────────────────┘
                    │
┌─────────────────────────────────────────┐
│        工具层 (Utility Layer)            │
│   RandomUtils, ValidationUtils, etc.    │
└─────────────────────────────────────────┘
```

### 核心组件设计

#### 1. 表现层 (Presentation Layer)

**主要组件：**

- `DataForgeApplication`: 应用程序入口点
- `CommandLineInterface`: CLI参数解析和命令处理
- `HelpFormatter`: 帮助信息格式化

**技术选型：**

- Apache Commons CLI 用于命令行参数解析
- Spring Boot 用于应用程序框架

#### 2. 配置管理层 (Configuration Management Layer)

**主要组件：**

- `ConfigurationManager`: 统一配置管理
- `ParameterParser`: 参数解析和验证
- `ConfigurationValidator`: 配置验证

**配置优先级：**

1. 命令行参数（最高优先级）
2. 配置文件参数
3. 默认参数（最低优先级）

#### 3. 业务逻辑层 (Business Logic Layer)

**核心接口设计：**

```java
public interface DataGenerator<T> {
    T generate(GenerationContext context);
    boolean validate(T data);
    String getType();
}

public interface GeneratorFactory {
    <T> DataGenerator<T> getGenerator(String type);
    void registerGenerator(String type, DataGenerator<?> generator);
}
```

**主要组件：**

- `DataForgeService`: 核心服务门面
- `GeneratorFactory`: 生成器工厂
- `DataRelationManager`: 数据关联管理
- `ValidationService`: 数据校验服务
- `GenerationContext`: 生成上下文

#### 4. 数据生成器实现

**基础信息类生成器：**

- `NameGenerator`: 姓名生成（支持中英文）
- `PhoneNumberGenerator`: 手机号码生成
- `EmailGenerator`: 邮箱地址生成
- `AgeGenerator`: 年龄生成
- `GenderGenerator`: 性别生成

**标识类生成器：**

- `IdCardNumberGenerator`: 身份证号生成（含校验算法）
- `BankCardNumberGenerator`: 银行卡号生成（Luhn算法）
- `USCCGenerator`: 统一社会信用代码生成
- `UUIDGenerator`: UUID生成

**网络设备类生成器：**

- `IpAddressGenerator`: IP地址生成
- `MacAddressGenerator`: MAC地址生成
- `UrlGenerator`: URL生成
- `DomainNameGenerator`: 域名生成

#### 5. 数据输出层 (Output Layer)

**输出接口设计：**

```java
public interface OutputWriter {
    void write(Stream<Map<String, Object>> dataStream, OutputConfig config);
    String getSupportedFormat();
}
```

**输出实现：**

- `CsvOutputWriter`: CSV格式输出
- `JsonOutputWriter`: JSON格式输出
- `XmlOutputWriter`: XML格式输出
- `SqlOutputWriter`: SQL INSERT语句输出
- `ConsoleOutputWriter`: 控制台输出

## 组件和接口

### 核心接口定义

#### DataGenerator 接口

```java
public interface DataGenerator<T> {
    /**
     * 生成数据
     * @param context 生成上下文
     * @return 生成的数据
     */
    T generate(GenerationContext context);
    
    /**
     * 验证数据有效性
     * @param data 待验证数据
     * @return 是否有效
     */
    boolean validate(T data);
    
    /**
     * 获取生成器类型
     * @return 类型标识
     */
    String getType();
    
    /**
     * 获取支持的参数
     * @return 参数列表
     */
    List<GeneratorParameter> getSupportedParameters();
}
```

#### GenerationContext 类

```java
public class GenerationContext {
    private final Map<String, Object> parameters;
    private final Random random;
    private final DataRelationManager relationManager;
    private final long seed;
    
    // 构造函数和方法
}
```

#### OutputWriter 接口

```java
public interface OutputWriter {
    void write(Stream<Map<String, Object>> dataStream, OutputConfig config) 
        throws IOException;
    String getSupportedFormat();
    boolean supportsStreaming();
}
```

### 数据关联管理

#### DataRelationManager

负责管理数据字段之间的关联关系，确保生成数据的逻辑一致性。

```java
public class DataRelationManager {
    private final Map<String, Object> sharedContext;
    
    public void setRelatedValue(String key, Object value);
    public <T> T getRelatedValue(String key, Class<T> type);
    public boolean hasRelation(String key);
}
```

**关联示例：**

- 身份证号 → 年龄、性别、出生日期
- 地址 → 邮编、电话区号
- 姓名 → 邮箱用户名、账号名

## 数据模型

### 生成配置模型

```java
public class GenerationConfig {
    private String dataType;
    private int count;
    private Map<String, Object> parameters;
    private OutputConfig outputConfig;
    private ValidationConfig validationConfig;
}

public class OutputConfig {
    private String format; // csv, json, xml, sql
    private String target; // file path or "stdout"
    private Map<String, Object> formatOptions;
}
```

### 数据源模型

```java
public class DataSource {
    private String name;
    private String type; // file, database, api
    private String location;
    private Map<String, Object> properties;
}
```

## 错误处理

### 异常层次结构

```java
public class DataForgeException extends Exception {
    // 基础异常类
}

public class ConfigurationException extends DataForgeException {
    // 配置相关异常
}

public class GenerationException extends DataForgeException {
    // 数据生成异常
}

public class ValidationException extends DataForgeException {
    // 数据校验异常
}

public class OutputException extends DataForgeException {
    // 输出相关异常
}
```

### 错误处理策略

1. **配置错误**: 立即终止并显示详细错误信息
2. **生成错误**: 记录日志，跳过当前数据，继续生成
3. **输出错误**: 尝试重试，失败后终止并保存已生成数据
4. **校验错误**: 根据配置决定是否跳过或终止

## 测试策略

### 单元测试

**测试覆盖范围：**

- 所有数据生成器的正确性测试
- 校验算法的准确性测试
- 配置解析的完整性测试
- 输出格式的正确性测试

**测试工具：**

- JUnit 5 作为测试框架
- Mockito 用于模拟依赖
- AssertJ 用于断言增强

### 集成测试

**测试场景：**

- 端到端数据生成流程测试
- 多种输出格式的集成测试
- 大数据量生成的性能测试
- 配置文件加载的集成测试

### 性能测试

**性能指标：**

- 数据生成吞吐量：目标 10,000+ records/sec
- 内存使用：生成100万条记录时峰值内存 < 500MB
- 启动时间：< 5秒
- CPU利用率：多核环境下 > 70%

**测试工具：**

- JMH (Java Microbenchmark Harness) 用于微基准测试
- JProfiler 用于性能分析
- 自定义性能测试套件

### 数据质量测试

**质量验证：**

- 生成数据的格式正确性
- 校验算法的准确性（如身份证、银行卡）
- 数据关联的逻辑一致性
- 边界值和异常数据的正确处理

## 技术选型

### 核心技术栈

- **Java 17**: 主要开发语言
- **Spring Boot 3.x**: 应用框架
- **Maven**: 构建工具
- **Apache Commons CLI**: 命令行解析
- **Jackson**: JSON/YAML处理
- **SLF4J + Logback**: 日志框架

### 第三方库

- **OpenCSV**: CSV文件处理
- **Caffeine**: 内存缓存
- **Apache Commons Lang**: 工具类
- **Guava**: 集合和工具类增强

### 开发工具

- **IntelliJ IDEA**: 开发IDE
- **Git**: 版本控制
- **JUnit 5**: 单元测试
- **Mockito**: 模拟框架
- **JMH**: 性能基准测试

## 部署和运维

### 打包方式

- 使用 Maven 构建可执行 JAR 包
- 包含所有依赖的 Fat JAR
- 支持 Java 17+ 运行环境

### 配置管理

- 支持 YAML 和 Properties 配置文件
- 环境变量覆盖配置
- 配置文件热重载（可选）

### 日志管理

- 分级日志输出（DEBUG, INFO, WARN, ERROR）
- 可配置的日志格式和输出目标
- 性能监控日志

### 监控指标

- 数据生成速率
- 内存使用情况
- 错误率统计
- 响应时间分布
