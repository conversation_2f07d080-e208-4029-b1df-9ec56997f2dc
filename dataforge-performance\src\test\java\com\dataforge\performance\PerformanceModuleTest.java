package com.dataforge.performance;

import com.dataforge.performance.baseline.PerformanceBaseline;
import com.dataforge.performance.runner.PerformanceTestRunner;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import static org.assertj.core.api.Assertions.*;

/**
 * 性能模块基本功能测试
 */
@DisplayName("性能模块测试")
class PerformanceModuleTest {

    @Test
    @DisplayName("性能基线管理器初始化测试")
    void testPerformanceBaselineInitialization() {
        assertThatCode(() -> {
            PerformanceBaseline baseline = new PerformanceBaseline();
            assertThat(baseline).isNotNull();
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("性能测试运行器初始化测试")
    void testPerformanceTestRunnerInitialization() {
        assertThatCode(() -> {
            // 测试运行器的静态方法调用
            PerformanceTestRunner.class.getDeclaredMethods();
        }).doesNotThrowAnyException();
    }

    @Test
    @DisplayName("性能指标数据类测试")
    void testPerformanceMetrics() {
        PerformanceBaseline.PerformanceMetrics metrics = 
            new PerformanceBaseline.PerformanceMetrics(1000.0, 100.0, 1024.0, 1.0);
        
        assertThat(metrics.throughput).isEqualTo(1000.0);
        assertThat(metrics.avgTime).isEqualTo(100.0);
        assertThat(metrics.avgMemory).isEqualTo(1024.0);
        assertThat(metrics.memoryPerRecord).isEqualTo(1.0);
    }

    @Test
    @DisplayName("回归测试结果类测试")
    void testRegressionResult() {
        PerformanceBaseline.RegressionResult result = 
            new PerformanceBaseline.RegressionResult(true, "测试回归");
        
        assertThat(result.hasRegression).isTrue();
        assertThat(result.details).isEqualTo("测试回归");
    }
}