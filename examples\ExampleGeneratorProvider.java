package examples;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.service.GeneratorProvider;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;
import com.dataforge.core.generator.GeneratorParameter;

import java.util.Arrays;
import java.util.Collection;
import java.util.Random;

/**
 * 示例生成器提供者
 * 
 * 演示如何通过SPI机制扩展DataForge的生成器功能。
 * 这个示例提供了一个简单的随机数生成器。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ExampleGeneratorProvider implements GeneratorProvider {

    @Override
    public String getProviderName() {
        return "Example Generator Provider";
    }

    @Override
    public String getVersion() {
        return "1.0.0";
    }

    @Override
    public String getDescription() {
        return "提供示例生成器的SPI扩展实现";
    }

    @Override
    public Collection<DataGenerator<?>> getGenerators() {
        return Arrays.asList(
            new RandomNumberGenerator(),
            new SimpleTextGenerator()
        );
    }

    @Override
    public boolean isAvailable() {
        return true;
    }

    @Override
    public void initialize() throws Exception {
        System.out.println("初始化示例生成器提供者");
    }

    @Override
    public void cleanup() {
        System.out.println("清理示例生成器提供者资源");
    }

    @Override
    public int getPriority() {
        return 500; // 中等优先级
    }

    /**
     * 随机数生成器示例
     */
    public static class RandomNumberGenerator implements DataGenerator<Integer> {
        private final Random random = new Random();

        @Override
        public Integer generate(GenerationContext context) {
            int min = context.getParameterAsInt("min", 1);
            int max = context.getParameterAsInt("max", 100);
            return random.nextInt(max - min + 1) + min;
        }

        @Override
        public ValidationResult validateWithDetails(Integer data) {
            if (data == null) {
                return ValidationResult.error("数据不能为空");
            }
            return ValidationResult.success();
        }

        @Override
        public String getType() {
            return "random-number";
        }

        @Override
        public String getDescription() {
            return "生成指定范围内的随机整数";
        }

        @Override
        public GeneratorParameter[] getSupportedParameters() {
            return new GeneratorParameter[] {
                new GeneratorParameter("min", "最小值", Integer.class, false, 1),
                new GeneratorParameter("max", "最大值", Integer.class, false, 100)
            };
        }
    }

    /**
     * 简单文本生成器示例
     */
    public static class SimpleTextGenerator implements DataGenerator<String> {
        private final String[] templates = {
            "Hello World", "DataForge", "Generator", "Example", "Test"
        };
        private final Random random = new Random();

        @Override
        public String generate(GenerationContext context) {
            String prefix = context.getParameterAsString("prefix", "");
            String suffix = context.getParameterAsString("suffix", "");
            String template = templates[random.nextInt(templates.length)];
            return prefix + template + suffix;
        }

        @Override
        public ValidationResult validateWithDetails(String data) {
            if (data == null || data.trim().isEmpty()) {
                return ValidationResult.error("文本不能为空");
            }
            return ValidationResult.success();
        }

        @Override
        public String getType() {
            return "simple-text";
        }

        @Override
        public String getDescription() {
            return "生成简单的示例文本";
        }

        @Override
        public GeneratorParameter[] getSupportedParameters() {
            return new GeneratorParameter[] {
                new GeneratorParameter("prefix", "前缀", String.class, false, ""),
                new GeneratorParameter("suffix", "后缀", String.class, false, "")
            };
        }
    }
}