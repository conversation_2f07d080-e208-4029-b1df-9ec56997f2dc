package com.dataforge.core.relation;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.LocalDate;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;

/**
 * 关联规则工厂
 * 
 * 提供常见的字段关联规则创建方法。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class RelationRuleFactory {

    private static final Logger logger = LoggerFactory.getLogger(RelationRuleFactory.class);

    /**
     * 创建身份证号关联规则
     * 从身份证号中提取年龄、性别、出生日期、地区代码
     * 
     * @return 身份证号关联规则
     */
    public static FieldRelation createIdCardRelation() {
        return FieldRelation.builder()
                .source("idcard")
                .targets(Set.of("age", "gender", "birth_date", "region_code"))
                .type(RelationType.EXTRACTION)
                .function(createIdCardExtractionFunction())
                .description("从身份证号中提取年龄、性别、出生日期、地区代码")
                .build();
    }

    /**
     * 创建姓名关联规则
     * 从姓名中提取姓氏、名字、拼音
     * 
     * @return 姓名关联规则
     */
    public static FieldRelation createNameRelation() {
        return FieldRelation.builder()
                .source("name")
                .targets(Set.of("surname", "given_name", "name_pinyin"))
                .type(RelationType.EXTRACTION)
                .function(createNameExtractionFunction())
                .description("从姓名中提取姓氏、名字、拼音")
                .build();
    }

    /**
     * 创建邮箱关联规则
     * 基于姓名生成邮箱用户名
     * 
     * @return 邮箱关联规则
     */
    public static FieldRelation createEmailRelation() {
        return FieldRelation.builder()
                .source("name")
                .targets(Set.of("email_username"))
                .type(RelationType.FORMATTING)
                .function(createEmailUsernameFunction())
                .description("基于姓名生成邮箱用户名")
                .build();
    }

    /**
     * 创建地址关联规则
     * 从地址中提取省市区信息
     * 
     * @return 地址关联规则
     */
    public static FieldRelation createAddressRelation() {
        return FieldRelation.builder()
                .source("address")
                .targets(Set.of("province", "city", "district"))
                .type(RelationType.EXTRACTION)
                .function(createAddressExtractionFunction())
                .description("从地址中提取省市区信息")
                .build();
    }

    /**
     * 创建年龄到出生年份的关联规则
     * 
     * @return 年龄关联规则
     */
    public static FieldRelation createAgeRelation() {
        return FieldRelation.builder()
                .source("age")
                .targets(Set.of("birth_year"))
                .type(RelationType.CALCULATION)
                .function(createAgeCalculationFunction())
                .description("根据年龄计算出生年份")
                .build();
    }

    /**
     * 创建身份证号提取函数
     */
    private static Function<Object, RelationResult> createIdCardExtractionFunction() {
        return sourceValue -> {
            if (sourceValue == null) {
                return RelationResult.failure("身份证号为空");
            }

            String idCard = sourceValue.toString().trim();
            if (idCard.length() != 18) {
                return RelationResult.failure("身份证号长度不正确，应为18位");
            }

            try {
                Map<String, Object> result = new HashMap<>();

                // 提取出生日期
                String birthDateStr = idCard.substring(6, 14);
                LocalDate birthDate = LocalDate.parse(birthDateStr, DateTimeFormatter.ofPattern("yyyyMMdd"));
                result.put("birth_date", birthDate.toString());

                // 计算年龄
                int age = Period.between(birthDate, LocalDate.now()).getYears();
                result.put("age", age);

                // 提取性别（倒数第二位，奇数为男，偶数为女）
                int genderCode = Integer.parseInt(idCard.substring(16, 17));
                String gender = (genderCode % 2 == 1) ? "MALE" : "FEMALE";
                result.put("gender", gender);

                // 提取地区代码
                String regionCode = idCard.substring(0, 6);
                result.put("region_code", regionCode);

                return RelationResult.success(result);

            } catch (DateTimeParseException e) {
                return RelationResult.failure("身份证号中的出生日期格式不正确: " + e.getMessage());
            } catch (NumberFormatException e) {
                return RelationResult.failure("身份证号格式不正确: " + e.getMessage());
            } catch (Exception e) {
                logger.error("提取身份证号信息失败", e);
                return RelationResult.failure("提取身份证号信息失败: " + e.getMessage());
            }
        };
    }

    /**
     * 创建姓名提取函数
     */
    private static Function<Object, RelationResult> createNameExtractionFunction() {
        return sourceValue -> {
            if (sourceValue == null) {
                return RelationResult.failure("姓名为空");
            }

            String name = sourceValue.toString().trim();
            if (name.isEmpty()) {
                return RelationResult.failure("姓名为空");
            }

            try {
                Map<String, Object> result = new HashMap<>();

                // 提取姓氏和名字
                if (name.length() >= 2) {
                    String surname = name.substring(0, 1);
                    String givenName = name.substring(1);

                    result.put("surname", surname);
                    result.put("given_name", givenName);

                    // 生成拼音（简化实现）
                    String pinyin = generateSimplePinyin(name);
                    result.put("name_pinyin", pinyin);
                } else {
                    result.put("surname", name);
                    result.put("given_name", "");
                    result.put("name_pinyin", generateSimplePinyin(name));
                }

                return RelationResult.success(result);

            } catch (Exception e) {
                logger.error("提取姓名信息失败", e);
                return RelationResult.failure("提取姓名信息失败: " + e.getMessage());
            }
        };
    }

    /**
     * 创建邮箱用户名生成函数
     */
    private static Function<Object, RelationResult> createEmailUsernameFunction() {
        return sourceValue -> {
            if (sourceValue == null) {
                return RelationResult.failure("姓名为空");
            }

            String name = sourceValue.toString().trim();
            if (name.isEmpty()) {
                return RelationResult.failure("姓名为空");
            }

            try {
                // 生成邮箱用户名（基于拼音）
                String pinyin = generateSimplePinyin(name);
                String username = pinyin.toLowerCase().replaceAll("[^a-z0-9]", "");

                // 如果用户名太短，添加随机数字
                if (username.length() < 3) {
                    username += String.valueOf((int) (Math.random() * 1000));
                }

                return RelationResult.success("email_username", username);

            } catch (Exception e) {
                logger.error("生成邮箱用户名失败", e);
                return RelationResult.failure("生成邮箱用户名失败: " + e.getMessage());
            }
        };
    }

    /**
     * 创建地址提取函数
     */
    private static Function<Object, RelationResult> createAddressExtractionFunction() {
        return sourceValue -> {
            if (sourceValue == null) {
                return RelationResult.failure("地址为空");
            }

            String address = sourceValue.toString().trim();
            if (address.isEmpty()) {
                return RelationResult.failure("地址为空");
            }

            try {
                Map<String, Object> result = new HashMap<>();

                // 简化的地址解析逻辑
                // 实际实现中应该使用专业的地址解析库
                if (address.contains("省")) {
                    int provinceIndex = address.indexOf("省");
                    String province = address.substring(0, provinceIndex + 1);
                    result.put("province", province);

                    String remaining = address.substring(provinceIndex + 1);
                    if (remaining.contains("市")) {
                        int cityIndex = remaining.indexOf("市");
                        String city = remaining.substring(0, cityIndex + 1);
                        result.put("city", city);

                        String remaining2 = remaining.substring(cityIndex + 1);
                        if (remaining2.contains("区") || remaining2.contains("县")) {
                            int districtIndex = Math.max(remaining2.indexOf("区"), remaining2.indexOf("县"));
                            if (districtIndex >= 0) {
                                String district = remaining2.substring(0, districtIndex + 1);
                                result.put("district", district);
                            }
                        }
                    }
                }

                return RelationResult.success(result);

            } catch (Exception e) {
                logger.error("提取地址信息失败", e);
                return RelationResult.failure("提取地址信息失败: " + e.getMessage());
            }
        };
    }

    /**
     * 创建年龄计算函数
     */
    private static Function<Object, RelationResult> createAgeCalculationFunction() {
        return sourceValue -> {
            if (sourceValue == null) {
                return RelationResult.failure("年龄为空");
            }

            try {
                int age;
                if (sourceValue instanceof Number) {
                    age = ((Number) sourceValue).intValue();
                } else {
                    age = Integer.parseInt(sourceValue.toString());
                }

                if (age < 0 || age > 150) {
                    return RelationResult.failure("年龄值不合理: " + age);
                }

                int birthYear = LocalDate.now().getYear() - age;
                return RelationResult.success("birth_year", birthYear);

            } catch (NumberFormatException e) {
                return RelationResult.failure("年龄格式不正确: " + sourceValue);
            } catch (Exception e) {
                logger.error("计算出生年份失败", e);
                return RelationResult.failure("计算出生年份失败: " + e.getMessage());
            }
        };
    }

    /**
     * 生成简化拼音
     * 实际实现中应该使用专业的拼音转换库
     * 
     * @param text 中文文本
     * @return 拼音
     */
    private static String generateSimplePinyin(String text) {
        // 这里是简化实现，实际应该使用专业的拼音转换库如pinyin4j
        // 目前只是简单地移除非字母数字字符
        return text.replaceAll("[^a-zA-Z0-9\\u4e00-\\u9fa5]", "");
    }
}