package com.dataforge.core.model;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 验证结果
 * 
 * 封装配置验证或数据验证的结果，包括错误信息、警告信息等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ValidationResult {

    private final List<String> errors;
    private final List<String> warnings;
    private final List<String> infos;

    /**
     * 构造函数
     */
    public ValidationResult() {
        this.errors = new ArrayList<>();
        this.warnings = new ArrayList<>();
        this.infos = new ArrayList<>();
    }

    /**
     * 添加错误信息
     * 
     * @param error 错误信息
     */
    public void addError(String error) {
        if (error != null && !error.trim().isEmpty()) {
            this.errors.add(error.trim());
        }
    }

    /**
     * 添加警告信息
     * 
     * @param warning 警告信息
     */
    public void addWarning(String warning) {
        if (warning != null && !warning.trim().isEmpty()) {
            this.warnings.add(warning.trim());
        }
    }

    /**
     * 添加信息
     * 
     * @param info 信息
     */
    public void addInfo(String info) {
        if (info != null && !info.trim().isEmpty()) {
            this.infos.add(info.trim());
        }
    }

    /**
     * 合并另一个验证结果
     * 
     * @param other 另一个验证结果
     */
    public void merge(ValidationResult other) {
        if (other != null) {
            this.errors.addAll(other.errors);
            this.warnings.addAll(other.warnings);
            this.infos.addAll(other.infos);
        }
    }

    /**
     * 检查是否有效（无错误）
     * 
     * @return 如果无错误返回true，否则返回false
     */
    public boolean isValid() {
        return errors.isEmpty();
    }

    /**
     * 检查是否有错误
     * 
     * @return 如果有错误返回true，否则返回false
     */
    public boolean hasErrors() {
        return !errors.isEmpty();
    }

    /**
     * 检查是否有警告
     * 
     * @return 如果有警告返回true，否则返回false
     */
    public boolean hasWarnings() {
        return !warnings.isEmpty();
    }

    /**
     * 检查是否有信息
     * 
     * @return 如果有信息返回true，否则返回false
     */
    public boolean hasInfos() {
        return !infos.isEmpty();
    }

    /**
     * 获取错误列表
     * 
     * @return 错误列表的副本
     */
    public List<String> getErrors() {
        return new ArrayList<>(errors);
    }

    /**
     * 获取警告列表
     * 
     * @return 警告列表的副本
     */
    public List<String> getWarnings() {
        return new ArrayList<>(warnings);
    }

    /**
     * 获取信息列表
     * 
     * @return 信息列表的副本
     */
    public List<String> getInfos() {
        return new ArrayList<>(infos);
    }

    /**
     * 获取错误数量
     * 
     * @return 错误数量
     */
    public int getErrorCount() {
        return errors.size();
    }

    /**
     * 获取警告数量
     * 
     * @return 警告数量
     */
    public int getWarningCount() {
        return warnings.size();
    }

    /**
     * 获取信息数量
     * 
     * @return 信息数量
     */
    public int getInfoCount() {
        return infos.size();
    }

    /**
     * 获取第一个错误信息
     * 
     * @return 第一个错误信息，如果没有错误返回null
     */
    public String getFirstError() {
        return errors.isEmpty() ? null : errors.get(0);
    }

    /**
     * 获取所有错误信息的字符串表示
     * 
     * @return 错误信息字符串
     */
    public String getErrorsAsString() {
        return String.join("; ", errors);
    }

    /**
     * 获取所有警告信息的字符串表示
     * 
     * @return 警告信息字符串
     */
    public String getWarningsAsString() {
        return String.join("; ", warnings);
    }

    /**
     * 获取所有信息的字符串表示
     * 
     * @return 信息字符串
     */
    public String getInfosAsString() {
        return String.join("; ", infos);
    }

    /**
     * 清空所有信息
     */
    public void clear() {
        errors.clear();
        warnings.clear();
        infos.clear();
    }

    /**
     * 创建成功的验证结果
     * 
     * @return 成功的验证结果
     */
    public static ValidationResult success() {
        return new ValidationResult();
    }

    /**
     * 创建包含错误的验证结果
     * 
     * @param error 错误信息
     * @return 包含错误的验证结果
     */
    public static ValidationResult error(String error) {
        ValidationResult result = new ValidationResult();
        result.addError(error);
        return result;
    }

    /**
     * 创建包含警告的验证结果
     * 
     * @param warning 警告信息
     * @return 包含警告的验证结果
     */
    public static ValidationResult warning(String warning) {
        ValidationResult result = new ValidationResult();
        result.addWarning(warning);
        return result;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        ValidationResult that = (ValidationResult) o;
        return Objects.equals(errors, that.errors) &&
                Objects.equals(warnings, that.warnings) &&
                Objects.equals(infos, that.infos);
    }

    @Override
    public int hashCode() {
        return Objects.hash(errors, warnings, infos);
    }

    @Override
    public String toString() {
        StringBuilder sb = new StringBuilder("ValidationResult{");
        sb.append("valid=").append(isValid());
        if (hasErrors()) {
            sb.append(", errors=").append(errors.size());
        }
        if (hasWarnings()) {
            sb.append(", warnings=").append(warnings.size());
        }
        if (hasInfos()) {
            sb.append(", infos=").append(infos.size());
        }
        sb.append("}");
        return sb.toString();
    }

    /**
     * 获取详细的字符串表示
     * 
     * @return 详细的字符串表示
     */
    public String toDetailString() {
        StringBuilder sb = new StringBuilder();

        if (hasErrors()) {
            sb.append("错误:\n");
            for (int i = 0; i < errors.size(); i++) {
                sb.append("  ").append(i + 1).append(". ").append(errors.get(i)).append("\n");
            }
        }

        if (hasWarnings()) {
            sb.append("警告:\n");
            for (int i = 0; i < warnings.size(); i++) {
                sb.append("  ").append(i + 1).append(". ").append(warnings.get(i)).append("\n");
            }
        }

        if (hasInfos()) {
            sb.append("信息:\n");
            for (int i = 0; i < infos.size(); i++) {
                sb.append("  ").append(i + 1).append(". ").append(infos.get(i)).append("\n");
            }
        }

        if (sb.length() == 0) {
            sb.append("验证通过，无错误或警告。\n");
        }

        return sb.toString();
    }
}