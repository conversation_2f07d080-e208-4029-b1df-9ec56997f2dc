/**
 * 数据关联管理模块
 * 
 * 该模块提供了数据字段之间关联关系的定义、管理和执行功能。
 * 主要包括：
 * 
 * <ul>
 * <li>{@link com.dataforge.core.relation.FieldRelation} - 字段关联定义</li>
 * <li>{@link com.dataforge.core.relation.RelationType} - 关联类型枚举</li>
 * <li>{@link com.dataforge.core.relation.RelationResult} - 关联执行结果</li>
 * <li>{@link com.dataforge.core.relation.RelationRuleFactory} - 关联规则工厂</li>
 * </ul>
 * 
 * <h3>使用示例：</h3>
 * 
 * <pre>{@code
 * // 创建身份证号关联规则
 * FieldRelation idCardRelation = RelationRuleFactory.createIdCardRelation();
 * 
 * // 注册到关联管理器
 * DataRelationManager manager = new DataRelationManager();
 * manager.registerFieldRelation(idCardRelation);
 * 
 * // 设置身份证号，自动触发关联字段更新
 * manager.setRelatedValue("idcard", "110101199001011234");
 * 
 * // 获取关联的年龄信息
 * Integer age = manager.getRelatedValue("age", Integer.class);
 * }</pre>
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 * @since 1.0.0
 */
package com.dataforge.core.relation;