package com.dataforge.core.output.impl;

import com.dataforge.core.output.AbstractOutputWriter;
import com.dataforge.core.output.OutputConfig;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * JSON输出器
 * 
 * 将数据以JSON格式输出到文件或标准输出，支持格式化和压缩输出模式。
 * 实现流式JSON输出以支持大数据量处理。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class JsonOutputWriter extends AbstractOutputWriter {

    private static final String TYPE = "json";

    private Writer writer;
    private ObjectMapper objectMapper;
    private JsonGenerator jsonGenerator;
    private List<String> headers;
    private boolean prettyPrint;
    private boolean arrayFormat;
    private boolean firstRecord;

    /**
     * 构造函数
     */
    public JsonOutputWriter() {
        this.objectMapper = new ObjectMapper();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    protected void doInitialize(OutputConfig config) throws Exception {
        // 获取JSON格式配置
        this.prettyPrint = config.getFormatOption("prettyPrint", true);
        this.arrayFormat = config.getFormatOption("arrayFormat", true);

        // 初始化输出流
        initializeWriter(config);

        // 创建JsonGenerator
        jsonGenerator = objectMapper.getFactory().createGenerator(writer);
        if (prettyPrint) {
            jsonGenerator.useDefaultPrettyPrinter();
        }

        // 开始JSON数组（如果使用数组格式）
        if (arrayFormat) {
            jsonGenerator.writeStartArray();
        }

        firstRecord = true;
    }

    /**
     * 初始化输出流
     * 
     * @param config 输出配置
     * @throws IOException 当初始化失败时抛出
     */
    private void initializeWriter(OutputConfig config) throws IOException {
        if (config.isFileOutput()) {
            // 文件输出
            String encoding = config.getEncoding();
            if (StandardCharsets.UTF_8.name().equals(encoding)) {
                this.writer = new BufferedWriter(new FileWriter(config.getTarget(), StandardCharsets.UTF_8));
            } else {
                this.writer = new BufferedWriter(new FileWriter(config.getTarget()));
            }
        } else {
            // 控制台输出
            this.writer = new BufferedWriter(new OutputStreamWriter(System.out, StandardCharsets.UTF_8));
        }
    }

    @Override
    protected void doWriteHeader(List<String> headers) throws Exception {
        this.headers = headers;
        // JSON格式不需要单独的表头处理
    }

    @Override
    protected void doWriteRecord(Map<String, Object> record) throws Exception {
        if (!arrayFormat && !firstRecord) {
            // 如果不是数组格式，每个对象单独一行
            jsonGenerator.writeRaw(System.lineSeparator());
        }

        // 写入JSON对象
        jsonGenerator.writeStartObject();

        if (headers != null && !headers.isEmpty()) {
            // 按照表头顺序输出字段
            for (String header : headers) {
                Object value = record.get(header);
                writeJsonField(header, value);
            }
        } else {
            // 输出所有字段
            for (Map.Entry<String, Object> entry : record.entrySet()) {
                writeJsonField(entry.getKey(), entry.getValue());
            }
        }

        jsonGenerator.writeEndObject();
        firstRecord = false;
    }

    /**
     * 写入JSON字段
     * 
     * @param fieldName 字段名
     * @param value     字段值
     * @throws IOException 当写入失败时抛出
     */
    private void writeJsonField(String fieldName, Object value) throws IOException {
        if (value == null) {
            jsonGenerator.writeNullField(fieldName);
        } else if (value instanceof String) {
            jsonGenerator.writeStringField(fieldName, (String) value);
        } else if (value instanceof Integer) {
            jsonGenerator.writeNumberField(fieldName, (Integer) value);
        } else if (value instanceof Long) {
            jsonGenerator.writeNumberField(fieldName, (Long) value);
        } else if (value instanceof Double) {
            jsonGenerator.writeNumberField(fieldName, (Double) value);
        } else if (value instanceof Float) {
            jsonGenerator.writeNumberField(fieldName, (Float) value);
        } else if (value instanceof Boolean) {
            jsonGenerator.writeBooleanField(fieldName, (Boolean) value);
        } else {
            // 其他类型转换为字符串
            jsonGenerator.writeStringField(fieldName, value.toString());
        }
    }

    @Override
    protected void doFlush() throws Exception {
        if (jsonGenerator != null) {
            jsonGenerator.flush();
        }
        if (writer != null) {
            writer.flush();
        }
    }

    @Override
    protected void doClose() throws Exception {
        try {
            if (jsonGenerator != null) {
                // 结束JSON数组（如果使用数组格式）
                if (arrayFormat) {
                    jsonGenerator.writeEndArray();
                }

                jsonGenerator.flush();
                jsonGenerator.close();
            }
        } finally {
            if (writer != null) {
                writer.close();
            }
        }
    }
}