package com.dataforge.core.service;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.model.ValidationConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ConfigurationManager测试类
 */
public class ConfigurationManagerTest {

  private ConfigurationManager configurationManager;

  @TempDir
  Path tempDir;

  @BeforeEach
  void setUp() {
    configurationManager = new ConfigurationManager();
  }

  @Test
  void testLoadYamlConfiguration() throws IOException {
    // 创建YAML配置文件
    String yamlContent = """
        dataType: name
        count: 100
        parameters:
          name.type: CN
          name.gender: MALE
        output:
          format: json
          file: output.json
        validation:
          enabled: true
          strictMode: false
        """;

    Path configFile = tempDir.resolve("config.yml");
    Files.writeString(configFile, yamlContent);

    // 加载配置
    GenerationConfig config = configurationManager.loadFromFile(configFile.toString());

    // 验证配置
    assertNotNull(config);
    assertEquals("name", config.getDataType());
    assertEquals(100, config.getCount());
    assertEquals("CN", config.getParameter("name.type"));
    assertEquals("MALE", config.getParameter("name.gender"));
    assertEquals("json", config.getOutputConfig().getFormat());
    assertEquals("output.json", config.getOutputConfig().getFile());
    assertTrue(config.getValidationConfig().isEnabled());
    assertFalse(config.getValidationConfig().isStrictMode());
  }

  @Test
  void testLoadJsonConfiguration() throws IOException {
    // 创建JSON配置文件
    String jsonContent = """
        {
          "dataType": "phone",
          "count": 50,
          "parameters": {
            "phone.region": "CN",
            "phone.prefix": "139,188"
          },
          "output": {
            "format": "csv",
            "file": "phones.csv"
          },
          "validation": {
            "enabled": true,
            "strictMode": true
          }
        }
        """;

    Path configFile = tempDir.resolve("config.json");
    Files.writeString(configFile, jsonContent);

    // 加载配置
    GenerationConfig config = configurationManager.loadFromFile(configFile.toString());

    // 验证配置
    assertNotNull(config);
    assertEquals("phone", config.getDataType());
    assertEquals(50, config.getCount());
    assertEquals("CN", config.getParameter("phone.region"));
    assertEquals("139,188", config.getParameter("phone.prefix"));
    assertEquals("csv", config.getOutputConfig().getFormat());
    assertEquals("phones.csv", config.getOutputConfig().getFile());
    assertTrue(config.getValidationConfig().isEnabled());
    assertTrue(config.getValidationConfig().isStrictMode());
  }

  @Test
  void testSaveYamlConfiguration() throws IOException {
    // 创建配置对象
    GenerationConfig config = new GenerationConfig("email", 25);
    config.addParameter("email.domains", "qq.com,163.com");
    config.getOutputConfig().setFormat("xml");
    config.getOutputConfig().setFile("emails.xml");
    config.getValidationConfig().setEnabled(false);

    Path configFile = tempDir.resolve("save-test.yml");

    // 保存配置
    configurationManager.saveToFile(config, configFile.toString());

    // 验证文件存在
    assertTrue(Files.exists(configFile));

    // 重新加载并验证
    GenerationConfig loadedConfig = configurationManager.loadFromFile(configFile.toString());
    assertEquals("email", loadedConfig.getDataType());
    assertEquals(25, loadedConfig.getCount());
    assertEquals("qq.com,163.com", loadedConfig.getParameter("email.domains"));
    assertEquals("xml", loadedConfig.getOutputConfig().getFormat());
    assertEquals("emails.xml", loadedConfig.getOutputConfig().getFile());
    assertFalse(loadedConfig.getValidationConfig().isEnabled());
  }

  @Test
  void testSaveJsonConfiguration() throws IOException {
    // 创建配置对象
    GenerationConfig config = new GenerationConfig("idcard", 75);
    config.addParameter("idcard.region", "110000");
    config.addParameter("idcard.gender", "FEMALE");
    config.getOutputConfig().setFormat("sql");
    config.getOutputConfig().setFile("idcards.sql");

    Path configFile = tempDir.resolve("save-test.json");

    // 保存配置
    configurationManager.saveToFile(config, configFile.toString());

    // 验证文件存在
    assertTrue(Files.exists(configFile));

    // 重新加载并验证
    GenerationConfig loadedConfig = configurationManager.loadFromFile(configFile.toString());
    assertEquals("idcard", loadedConfig.getDataType());
    assertEquals(75, loadedConfig.getCount());
    assertEquals("110000", loadedConfig.getParameter("idcard.region"));
    assertEquals("FEMALE", loadedConfig.getParameter("idcard.gender"));
    assertEquals("sql", loadedConfig.getOutputConfig().getFormat());
    assertEquals("idcards.sql", loadedConfig.getOutputConfig().getFile());
  }

  @Test
  void testMergeConfigurations() {
    // 创建基础配置
    GenerationConfig baseConfig = new GenerationConfig("name", 10);
    baseConfig.addParameter("name.type", "CN");
    baseConfig.getOutputConfig().setFormat("csv");
    baseConfig.getValidationConfig().setEnabled(true);

    // 创建覆盖配置
    GenerationConfig overrideConfig = new GenerationConfig("name", 20);
    overrideConfig.addParameter("name.gender", "MALE");
    overrideConfig.getOutputConfig().setFormat("json");
    overrideConfig.getOutputConfig().setFile("output.json");

    // 合并配置
    GenerationConfig mergedConfig = configurationManager.mergeConfigurations(baseConfig, overrideConfig);

    // 验证合并结果
    assertEquals("name", mergedConfig.getDataType());
    assertEquals(20, mergedConfig.getCount()); // 覆盖值
    assertEquals("CN", mergedConfig.getParameter("name.type")); // 基础值
    assertEquals("MALE", mergedConfig.getParameter("name.gender")); // 覆盖值
    assertEquals("json", mergedConfig.getOutputConfig().getFormat()); // 覆盖值
    assertEquals("output.json", mergedConfig.getOutputConfig().getFile()); // 覆盖值
    assertTrue(mergedConfig.getValidationConfig().isEnabled()); // 基础值
  }

  @Test
  void testMergeWithNullConfigurations() {
    GenerationConfig config = new GenerationConfig("test", 5);

    // 测试null基础配置
    GenerationConfig result1 = configurationManager.mergeConfigurations(null, config);
    assertEquals("test", result1.getDataType());
    assertEquals(5, result1.getCount());

    // 测试null覆盖配置
    GenerationConfig result2 = configurationManager.mergeConfigurations(config, null);
    assertEquals("test", result2.getDataType());
    assertEquals(5, result2.getCount());

    // 测试两个都为null
    GenerationConfig result3 = configurationManager.mergeConfigurations(null, null);
    assertNotNull(result3);
    assertNull(result3.getDataType());
    assertEquals(10, result3.getCount()); // 默认值
  }

  @Test
  void testLoadNonExistentFile() {
    String nonExistentFile = tempDir.resolve("non-existent.yml").toString();

    IOException exception = assertThrows(IOException.class, () -> {
      configurationManager.loadFromFile(nonExistentFile);
    });

    assertTrue(exception.getMessage().contains("配置文件不存在"));
  }

  @Test
  void testLoadInvalidYamlFile() throws IOException {
    // 创建无效的YAML文件
    String invalidYaml = """
        dataType: name
        count: invalid_number
        parameters:
          - invalid_structure
        """;

    Path configFile = tempDir.resolve("invalid.yml");
    Files.writeString(configFile, invalidYaml);

    IOException exception = assertThrows(IOException.class, () -> {
      configurationManager.loadFromFile(configFile.toString());
    });

    assertTrue(exception.getMessage().contains("配置文件解析失败"));
  }

  @Test
  void testLoadInvalidJsonFile() throws IOException {
    // 创建无效的JSON文件
    String invalidJson = """
        {
          "dataType": "name",
          "count": "invalid_number",
          "parameters": {
            "name.type": "CN"
          }
          // 缺少闭合括号
        """;

    Path configFile = tempDir.resolve("invalid.json");
    Files.writeString(configFile, invalidJson);

    IOException exception = assertThrows(IOException.class, () -> {
      configurationManager.loadFromFile(configFile.toString());
    });

    assertTrue(exception.getMessage().contains("配置文件解析失败"));
  }

  @Test
  void testFileExtensionDetection() throws IOException {
    GenerationConfig config = new GenerationConfig("test", 1);

    // 测试.yml扩展名
    Path ymlFile = tempDir.resolve("test.yml");
    configurationManager.saveToFile(config, ymlFile.toString());
    assertTrue(Files.exists(ymlFile));

    // 测试.yaml扩展名
    Path yamlFile = tempDir.resolve("test.yaml");
    configurationManager.saveToFile(config, yamlFile.toString());
    assertTrue(Files.exists(yamlFile));

    // 测试.json扩展名
    Path jsonFile = tempDir.resolve("test.json");
    configurationManager.saveToFile(config, jsonFile.toString());
    assertTrue(Files.exists(jsonFile));

    // 测试无扩展名（应该默认使用YAML）
    Path noExtFile = tempDir.resolve("test");
    configurationManager.saveToFile(config, noExtFile.toString());
    assertTrue(Files.exists(noExtFile));
  }

  @Test
  void testCreateDefaultConfiguration() {
    GenerationConfig defaultConfig = configurationManager.createDefaultConfiguration();

    assertNotNull(defaultConfig);
    assertEquals(10, defaultConfig.getCount());
    assertEquals("csv", defaultConfig.getOutputConfig().getFormat());
    assertEquals("UTF-8", defaultConfig.getOutputConfig().getEncoding());
    assertTrue(defaultConfig.getValidationConfig().isEnabled());
    assertFalse(defaultConfig.getValidationConfig().isStrictMode());
    assertTrue(defaultConfig.getPerformanceConfig().isEnableParallel());
  }

  @Test
  void testCreateConfigurationFromCLI() {
    java.util.Map<String, Object> cliParams = new java.util.HashMap<>();
    cliParams.put("dataType", "name");
    cliParams.put("count", 50);
    cliParams.put("seed", 12345L);
    cliParams.put("output.format", "json");
    cliParams.put("output.file", "output.json");
    cliParams.put("validation.enabled", true);
    cliParams.put("validation.strict", true);
    cliParams.put("performance.threads", 4);
    cliParams.put("performance.parallel", false);
    cliParams.put("name.type", "CN");
    cliParams.put("name.gender", "MALE");

    GenerationConfig cliConfig = configurationManager.createConfigurationFromCLI(cliParams);

    assertEquals("name", cliConfig.getDataType());
    assertEquals(50, cliConfig.getCount());
    assertEquals(12345L, cliConfig.getSeed());
    assertEquals("json", cliConfig.getOutputConfig().getFormat());
    assertEquals("output.json", cliConfig.getOutputConfig().getFile());
    assertTrue(cliConfig.getValidationConfig().isEnabled());
    assertTrue(cliConfig.getValidationConfig().isStrictMode());
    assertEquals(4, cliConfig.getPerformanceConfig().getThreadPoolSize());
    assertFalse(cliConfig.getPerformanceConfig().isEnableParallel());
    assertEquals("CN", cliConfig.getParameters().get("name.type"));
    assertEquals("MALE", cliConfig.getParameters().get("name.gender"));
  }

  @Test
  void testConfigurationPriorityManagement() throws IOException {
    // 创建配置文件
    GenerationConfig fileConfig = new GenerationConfig("phone", 20);
    fileConfig.addParameter("phone.prefix", "138");
    fileConfig.getOutputConfig().setFormat("csv");
    fileConfig.getOutputConfig().setFile("file-output.csv");
    fileConfig.getValidationConfig().setEnabled(false);
    fileConfig.getPerformanceConfig().setThreadPoolSize(2);

    Path configFile = tempDir.resolve("priority-test.yml");
    configurationManager.saveToFile(fileConfig, configFile.toString());

    // 创建CLI参数
    java.util.Map<String, Object> cliParams = new java.util.HashMap<>();
    cliParams.put("dataType", "name"); // 覆盖文件中的phone
    cliParams.put("count", 100); // 覆盖文件中的20
    cliParams.put("output.format", "json"); // 覆盖文件中的csv
    cliParams.put("validation.enabled", true); // 覆盖文件中的false
    cliParams.put("name.type", "CN"); // 新增参数

    // 应用配置优先级
    GenerationConfig finalConfig = configurationManager.applyConfigurationPriority(cliParams, configFile.toString());

    // 验证CLI参数优先级最高
    assertEquals("name", finalConfig.getDataType()); // CLI覆盖
    assertEquals(100, finalConfig.getCount()); // CLI覆盖
    assertEquals("json", finalConfig.getOutputConfig().getFormat()); // CLI覆盖
    assertTrue(finalConfig.getValidationConfig().isEnabled()); // CLI覆盖

    // 验证文件配置生效（CLI未指定的部分）
    assertEquals("file-output.csv", finalConfig.getOutputConfig().getFile()); // 文件配置
    assertEquals(2, finalConfig.getPerformanceConfig().getThreadPoolSize()); // 文件配置

    // 验证默认配置生效（文件和CLI都未指定的部分）
    assertEquals("UTF-8", finalConfig.getOutputConfig().getEncoding()); // 默认配置

    // 验证CLI新增参数
    assertEquals("CN", finalConfig.getParameters().get("name.type"));
  }

  @Test
  void testConfigurationPriorityWithoutFile() {
    java.util.Map<String, Object> cliParams = new java.util.HashMap<>();
    cliParams.put("dataType", "email");
    cliParams.put("count", 30);
    cliParams.put("output.format", "xml");

    GenerationConfig finalConfig = configurationManager.applyConfigurationPriorityIgnoreErrors(cliParams, null);

    // 验证CLI参数生效
    assertEquals("email", finalConfig.getDataType());
    assertEquals(30, finalConfig.getCount());
    assertEquals("xml", finalConfig.getOutputConfig().getFormat());

    // 验证默认配置生效
    assertEquals("UTF-8", finalConfig.getOutputConfig().getEncoding());
    assertTrue(finalConfig.getValidationConfig().isEnabled());
    assertTrue(finalConfig.getPerformanceConfig().isEnableParallel());
  }

  @Test
  void testConfigurationPriorityWithInvalidFile() {
    java.util.Map<String, Object> cliParams = new java.util.HashMap<>();
    cliParams.put("dataType", "age");
    cliParams.put("count", 15);

    // 使用不存在的配置文件
    GenerationConfig finalConfig = configurationManager.applyConfigurationPriorityIgnoreErrors(cliParams,
        "nonexistent.yml");

    // 验证CLI参数和默认配置生效
    assertEquals("age", finalConfig.getDataType());
    assertEquals(15, finalConfig.getCount());
    assertEquals("csv", finalConfig.getOutputConfig().getFormat()); // 默认值
    assertTrue(finalConfig.getValidationConfig().isEnabled()); // 默认值
  }

  @Test
  void testEnhancedConfigurationMerging() {
    // 基础配置
    GenerationConfig baseConfig = new GenerationConfig("name", 10);
    baseConfig.getOutputConfig().setFormat("csv");
    baseConfig.getOutputConfig().setFile("base.csv");
    baseConfig.getOutputConfig().setEncoding("UTF-8");
    baseConfig.getValidationConfig().setEnabled(true);
    baseConfig.getValidationConfig().setStrictMode(false);
    baseConfig.getPerformanceConfig().setThreadPoolSize(4);
    baseConfig.getPerformanceConfig().setEnableParallel(true);

    // 覆盖配置（部分字段）
    GenerationConfig overrideConfig = new GenerationConfig();
    overrideConfig.setDataType("phone");
    overrideConfig.getOutputConfig().setFormat("json"); // 只覆盖格式
    overrideConfig.getValidationConfig().setStrictMode(true); // 只覆盖严格模式
    overrideConfig.getPerformanceConfig().setThreadPoolSize(8); // 只覆盖线程数

    GenerationConfig mergedConfig = configurationManager.mergeConfigurationsEnhanced(baseConfig, overrideConfig);

    // 验证覆盖的字段
    assertEquals("phone", mergedConfig.getDataType());
    assertEquals("json", mergedConfig.getOutputConfig().getFormat());
    assertTrue(mergedConfig.getValidationConfig().isStrictMode());
    assertEquals(8, mergedConfig.getPerformanceConfig().getThreadPoolSize());

    // 验证未覆盖的字段保持原值
    assertEquals(10, mergedConfig.getCount());
    assertEquals("base.csv", mergedConfig.getOutputConfig().getFile());
    assertEquals("UTF-8", mergedConfig.getOutputConfig().getEncoding());
    assertTrue(mergedConfig.getValidationConfig().isEnabled());
    assertTrue(mergedConfig.getPerformanceConfig().isEnableParallel());
  }
}