package com.dataforge.generators.identifier;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 统一社会信用代码生成器
 * 
 * 生成符合GB32100-2015标准的18位统一社会信用代码。
 * 支持不同机构类型和地区的代码生成，确保校验位正确。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class USCCGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "uscc";
    private static final String DESCRIPTION = "生成统一社会信用代码";

    // 统一社会信用代码验证正则表达式
    private static final Pattern USCC_PATTERN = Pattern
            .compile("^[0-9A-HJ-NPQRTUWXY]{2}[0-9]{6}[0-9A-HJ-NPQRTUWXY]{10}$");

    // 校验位字符集
    private static final String CHECK_CHARS = "0123456789ABCDEFGHJKLMNPQRTUWXY";

    // 校验位权重
    private static final int[] WEIGHTS = { 1, 3, 9, 27, 19, 26, 16, 17, 20, 29, 25, 13, 8, 24, 10, 30, 28 };

    // 登记管理部门代码（第1位）
    private static final Map<String, String> REGISTRATION_AUTHORITIES = new HashMap<>();
    static {
        REGISTRATION_AUTHORITIES.put("1", "机构编制");
        REGISTRATION_AUTHORITIES.put("5", "民政");
        REGISTRATION_AUTHORITIES.put("9", "工商");
        REGISTRATION_AUTHORITIES.put("Y", "其他");
    }

    // 机构类别代码（第2位）
    private static final Map<String, String> ORGANIZATION_TYPES = new HashMap<>();
    static {
        ORGANIZATION_TYPES.put("1", "企业");
        ORGANIZATION_TYPES.put("2", "事业单位");
        ORGANIZATION_TYPES.put("3", "社会团体");
        ORGANIZATION_TYPES.put("4", "其他组织");
        ORGANIZATION_TYPES.put("5", "工会");
        ORGANIZATION_TYPES.put("9", "其他");
    }

    // 常见地区代码（第3-8位，行政区划代码）
    private static final List<String> COMMON_AREA_CODES = Arrays.asList(
            "110000", // 北京市
            "120000", // 天津市
            "130000", // 河北省
            "140000", // 山西省
            "150000", // 内蒙古自治区
            "210000", // 辽宁省
            "220000", // 吉林省
            "230000", // 黑龙江省
            "310000", // 上海市
            "320000", // 江苏省
            "330000", // 浙江省
            "340000", // 安徽省
            "350000", // 福建省
            "360000", // 江西省
            "370000", // 山东省
            "410000", // 河南省
            "420000", // 湖北省
            "430000", // 湖南省
            "440000", // 广东省
            "450000", // 广西壮族自治区
            "460000", // 海南省
            "500000", // 重庆市
            "510000", // 四川省
            "520000", // 贵州省
            "530000", // 云南省
            "540000", // 西藏自治区
            "610000", // 陕西省
            "620000", // 甘肃省
            "630000", // 青海省
            "640000", // 宁夏回族自治区
            "650000" // 新疆维吾尔自治区
    );

    private final Random random;

    /**
     * 构造函数
     */
    public USCCGenerator() {
        this.random = new Random();
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("registrationAuthority", String.class, "random",
                "登记管理部门：1（机构编制）、5（民政）、9（工商）、Y（其他），或使用random随机选择", false));
        addParameter(new GeneratorParameter("organizationType", String.class, "random",
                "机构类别：1（企业）、2（事业单位）、3（社会团体）、4（其他组织）、5（工会）、9（其他），或使用random随机选择", false));
        addParameter(new GeneratorParameter("areaCode", String.class, "random",
                "行政区划代码（6位），如110000，或使用random随机选择", false));
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        String registrationAuthority = context.getParameter("registrationAuthority", "random");
        String organizationType = context.getParameter("organizationType", "random");
        String areaCode = context.getParameter("areaCode", "random");

        // 生成第1位：登记管理部门代码
        String regAuth = selectRegistrationAuthority(registrationAuthority);

        // 生成第2位：机构类别代码
        String orgType = selectOrganizationType(organizationType);

        // 生成第3-8位：行政区划代码
        String selectedAreaCode = selectAreaCode(areaCode);

        // 生成第9-17位：主体标识码（组织机构代码）
        String organizationCode = generateOrganizationCode();

        // 组合前17位
        String first17Chars = regAuth + orgType + selectedAreaCode + organizationCode;

        // 计算第18位校验码
        char checkCode = calculateCheckCode(first17Chars);

        return first17Chars + checkCode;
    }

    /**
     * 选择登记管理部门代码
     * 
     * @param registrationAuthority 指定的登记管理部门或"random"
     * @return 登记管理部门代码
     */
    private String selectRegistrationAuthority(String registrationAuthority) {
        if ("random".equals(registrationAuthority) || registrationAuthority == null) {
            List<String> authorities = Arrays.asList("1", "5", "9", "Y");
            return authorities.get(random.nextInt(authorities.size()));
        }

        if (!REGISTRATION_AUTHORITIES.containsKey(registrationAuthority)) {
            throw new IllegalArgumentException("无效的登记管理部门代码: " + registrationAuthority);
        }

        return registrationAuthority;
    }

    /**
     * 选择机构类别代码
     * 
     * @param organizationType 指定的机构类别或"random"
     * @return 机构类别代码
     */
    private String selectOrganizationType(String organizationType) {
        if ("random".equals(organizationType) || organizationType == null) {
            List<String> types = Arrays.asList("1", "2", "3", "4", "5", "9");
            return types.get(random.nextInt(types.size()));
        }

        if (!ORGANIZATION_TYPES.containsKey(organizationType)) {
            throw new IllegalArgumentException("无效的机构类别代码: " + organizationType);
        }

        return organizationType;
    }

    /**
     * 选择行政区划代码
     * 
     * @param areaCode 指定的行政区划代码或"random"
     * @return 行政区划代码
     */
    private String selectAreaCode(String areaCode) {
        if ("random".equals(areaCode) || areaCode == null) {
            return COMMON_AREA_CODES.get(random.nextInt(COMMON_AREA_CODES.size()));
        }

        // 验证行政区划代码格式
        if (!areaCode.matches("\\d{6}")) {
            throw new IllegalArgumentException("行政区划代码必须是6位数字");
        }

        return areaCode;
    }

    /**
     * 生成组织机构代码（第9-17位）
     * 
     * @return 9位组织机构代码
     */
    private String generateOrganizationCode() {
        StringBuilder code = new StringBuilder();

        // 生成8位主体标识码
        for (int i = 0; i < 8; i++) {
            code.append(CHECK_CHARS.charAt(random.nextInt(CHECK_CHARS.length())));
        }

        // 计算组织机构代码的校验位（第9位）
        char orgCheckCode = calculateOrgCheckCode(code.toString());
        code.append(orgCheckCode);

        return code.toString();
    }

    /**
     * 计算组织机构代码的校验位
     * 
     * @param orgCode 8位组织机构代码
     * @return 校验位
     */
    private char calculateOrgCheckCode(String orgCode) {
        int[] orgWeights = { 3, 7, 9, 10, 5, 8, 4, 2 };
        int sum = 0;

        for (int i = 0; i < 8; i++) {
            char c = orgCode.charAt(i);
            int value = CHECK_CHARS.indexOf(c);
            sum += value * orgWeights[i];
        }

        int remainder = 11 - (sum % 11);
        if (remainder == 10) {
            return 'X';
        } else if (remainder == 11) {
            return '0';
        } else {
            return CHECK_CHARS.charAt(remainder);
        }
    }

    /**
     * 计算统一社会信用代码的校验位
     * 根据GB32100-2015标准计算第18位校验码
     * 
     * @param first17Chars 前17位字符
     * @return 校验位字符
     */
    private char calculateCheckCode(String first17Chars) {
        int sum = 0;

        for (int i = 0; i < 17; i++) {
            char c = first17Chars.charAt(i);
            int value = CHECK_CHARS.indexOf(c);
            if (value == -1) {
                throw new IllegalArgumentException("无效字符: " + c);
            }
            sum += value * WEIGHTS[i];
        }

        int remainder = 31 - (sum % 31);
        if (remainder == 31) {
            remainder = 0;
        }

        return CHECK_CHARS.charAt(remainder);
    }

    @Override
    public ValidationResult validateWithDetails(String data) {
        if (data == null) {
            return ValidationResult.error("统一社会信用代码不能为空");
        }

        String trimmed = data.trim().toUpperCase();
        if (trimmed.isEmpty()) {
            return ValidationResult.error("统一社会信用代码不能为空");
        }

        // 长度检查
        if (trimmed.length() != 18) {
            return ValidationResult.error("统一社会信用代码必须是18位");
        }

        // 格式检查
        if (!USCC_PATTERN.matcher(trimmed).matches()) {
            return ValidationResult.error("统一社会信用代码格式不正确");
        }

        // 登记管理部门代码检查
        String regAuth = trimmed.substring(0, 1);
        if (!REGISTRATION_AUTHORITIES.containsKey(regAuth)) {
            return ValidationResult.error("无效的登记管理部门代码: " + regAuth);
        }

        // 机构类别代码检查
        String orgType = trimmed.substring(1, 2);
        if (!ORGANIZATION_TYPES.containsKey(orgType)) {
            return ValidationResult.error("无效的机构类别代码: " + orgType);
        }

        // 行政区划代码检查
        String areaCode = trimmed.substring(2, 8);
        if (!areaCode.matches("\\d{6}")) {
            return ValidationResult.error("行政区划代码格式不正确");
        }

        // 校验位检查
        try {
            String first17 = trimmed.substring(0, 17);
            char expectedCheckCode = calculateCheckCode(first17);
            char actualCheckCode = trimmed.charAt(17);

            if (expectedCheckCode != actualCheckCode) {
                return ValidationResult.error("统一社会信用代码校验位不正确");
            }
        } catch (Exception e) {
            return ValidationResult.error("统一社会信用代码校验失败: " + e.getMessage());
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }
}