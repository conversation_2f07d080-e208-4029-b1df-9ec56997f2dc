# Test configuration for DataForge Core
logging.level.com.dataforge=DEBUG
logging.level.org.springframework=WARN
logging.level.org.hibernate=WARN

# Test database configuration (if needed)
spring.datasource.url=jdbc:h2:mem:testdb
spring.datasource.driver-class-name=org.h2.Driver
spring.datasource.username=sa
spring.datasource.password=

# JPA configuration for tests
spring.jpa.hibernate.ddl-auto=create-drop
spring.jpa.show-sql=false
spring.jpa.properties.hibernate.format_sql=false

# Test specific settings
dataforge.test.mode=true
dataforge.cache.enabled=false
dataforge.performance.threads=2