package com.dataforge.core.model;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 数据生成配置
 * 
 * 定义数据生成的完整配置信息，包括数据类型、生成数量、参数设置、
 * 输出配置和校验配置等。支持从YAML/JSON配置文件加载。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class GenerationConfig {

    @JsonProperty("dataType")
    private String dataType;

    @JsonProperty("count")
    private int count = 10;

    @JsonProperty("parameters")
    private Map<String, Object> parameters = new HashMap<>();

    @JsonProperty("fields")
    private List<FieldConfig> fields = new ArrayList<>();

    @JsonProperty("output")
    private OutputConfig outputConfig = new OutputConfig();

    @JsonProperty("validation")
    private ValidationConfig validationConfig = new ValidationConfig();

    @JsonProperty("performance")
    private PerformanceConfig performanceConfig = new PerformanceConfig();

    @JsonProperty("seed")
    private Long seed;

    @JsonProperty("requestId")
    private String requestId;

    /**
     * 默认构造函数
     */
    public GenerationConfig() {
    }

    /**
     * 构造函数
     * 
     * @param dataType 数据类型
     * @param count    生成数量
     */
    public GenerationConfig(String dataType, int count) {
        this.dataType = dataType;
        this.count = count;
    }

    // Getters and Setters

    public String getDataType() {
        return dataType;
    }

    public void setDataType(String dataType) {
        this.dataType = dataType;
    }

    public int getCount() {
        return count;
    }

    public void setCount(int count) {
        this.count = Math.max(1, count); // 确保至少生成1条数据
    }

    public Map<String, Object> getParameters() {
        return parameters;
    }

    public void setParameters(Map<String, Object> parameters) {
        this.parameters = parameters != null ? parameters : new HashMap<>();
    }

    public List<FieldConfig> getFields() {
        return fields;
    }

    public void setFields(List<FieldConfig> fields) {
        this.fields = fields != null ? fields : new ArrayList<>();
    }

    public OutputConfig getOutputConfig() {
        return outputConfig;
    }

    public void setOutputConfig(OutputConfig outputConfig) {
        this.outputConfig = outputConfig != null ? outputConfig : new OutputConfig();
    }

    public ValidationConfig getValidationConfig() {
        return validationConfig;
    }

    public void setValidationConfig(ValidationConfig validationConfig) {
        this.validationConfig = validationConfig != null ? validationConfig : new ValidationConfig();
    }

    public PerformanceConfig getPerformanceConfig() {
        return performanceConfig;
    }

    public void setPerformanceConfig(PerformanceConfig performanceConfig) {
        this.performanceConfig = performanceConfig != null ? performanceConfig : new PerformanceConfig();
    }

    /**
     * 设置输出配置列表
     * 
     * @param outputConfigs 输出配置列表
     */
    public void setOutputConfigs(List<OutputConfig> outputConfigs) {
        // 这个方法是为了兼容性，实际只使用单个输出配置
        if (outputConfigs != null && !outputConfigs.isEmpty()) {
            this.outputConfig = outputConfigs.get(0);
        }
    }

    public Long getSeed() {
        return seed;
    }

    public void setSeed(Long seed) {
        this.seed = seed;
    }

    public String getRequestId() {
        return requestId;
    }

    public void setRequestId(String requestId) {
        this.requestId = requestId;
    }

    /**
     * 添加参数
     * 
     * @param key   参数键
     * @param value 参数值
     */
    public void addParameter(String key, Object value) {
        if (key != null) {
            this.parameters.put(key, value);
        }
    }

    /**
     * 获取参数值
     * 
     * @param key 参数键
     * @return 参数值，如果不存在返回null
     */
    public Object getParameter(String key) {
        return parameters.get(key);
    }

    /**
     * 获取参数值并转换为指定类型
     * 
     * @param key  参数键
     * @param type 目标类型
     * @param <T>  类型参数
     * @return 转换后的参数值，如果不存在或转换失败返回null
     */
    @SuppressWarnings("unchecked")
    public <T> T getParameter(String key, Class<T> type) {
        Object value = parameters.get(key);
        if (value == null) {
            return null;
        }

        if (type.isAssignableFrom(value.getClass())) {
            return (T) value;
        }

        return null;
    }

    /**
     * 添加字段配置
     * 
     * @param fieldConfig 字段配置
     */
    public void addField(FieldConfig fieldConfig) {
        if (fieldConfig != null) {
            this.fields.add(fieldConfig);
        }
    }

    /**
     * 检查是否为多字段生成模式
     * 
     * @return 如果是多字段模式返回true，否则返回false
     */
    public boolean isMultiFieldMode() {
        return !fields.isEmpty();
    }

    /**
     * 验证配置的有效性
     * 
     * @return 验证结果
     */
    public ValidationResult validate() {
        ValidationResult result = new ValidationResult();

        // 验证基本配置
        if (isMultiFieldMode()) {
            // 多字段模式验证
            if (fields.isEmpty()) {
                result.addError("多字段模式下字段列表不能为空");
            } else {
                for (int i = 0; i < fields.size(); i++) {
                    FieldConfig field = fields.get(i);
                    ValidationResult fieldResult = field.validate();
                    if (!fieldResult.isValid()) {
                        result.addError("字段[" + i + "]配置无效: " + fieldResult.getErrors());
                    }
                }
            }
        } else {
            // 单字段模式验证
            if (dataType == null || dataType.trim().isEmpty()) {
                result.addError("数据类型不能为空");
            }
        }

        if (count <= 0) {
            result.addError("生成数量必须大于0");
        }

        // 验证输出配置
        ValidationResult outputResult = outputConfig.validate();
        if (!outputResult.isValid()) {
            result.addError("输出配置无效: " + outputResult.getErrors());
        }

        // 验证校验配置
        ValidationResult validationResult = validationConfig.validate();
        if (!validationResult.isValid()) {
            result.addError("校验配置无效: " + validationResult.getErrors());
        }

        return result;
    }

    /**
     * 创建配置的副本
     * 
     * @return 配置副本
     */
    public GenerationConfig copy() {
        GenerationConfig copy = new GenerationConfig();
        copy.dataType = this.dataType;
        copy.count = this.count;
        copy.parameters = new HashMap<>(this.parameters);
        copy.fields = new ArrayList<>();
        for (FieldConfig field : this.fields) {
            copy.fields.add(field.copy());
        }
        copy.outputConfig = this.outputConfig.copy();
        copy.validationConfig = this.validationConfig.copy();
        copy.performanceConfig = this.performanceConfig.copy();
        copy.seed = this.seed;
        copy.requestId = this.requestId;
        return copy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        GenerationConfig that = (GenerationConfig) o;
        return count == that.count &&
                Objects.equals(dataType, that.dataType) &&
                Objects.equals(parameters, that.parameters) &&
                Objects.equals(fields, that.fields) &&
                Objects.equals(outputConfig, that.outputConfig) &&
                Objects.equals(validationConfig, that.validationConfig) &&
                Objects.equals(seed, that.seed);
    }

    @Override
    public int hashCode() {
        return Objects.hash(dataType, count, parameters, fields, outputConfig, validationConfig, seed);
    }

    @Override
    public String toString() {
        return String.format("GenerationConfig{dataType='%s', count=%d, fieldsCount=%d, requestId='%s'}",
                dataType, count, fields.size(), requestId);
    }
}