package com.dataforge.core.performance;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.service.GeneratorFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.ExecutionException;

/**
 * 并行数据生成管理器
 * 
 * 整合并行生成服务和性能监控，提供统一的多线程数据生成接口。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ParallelDataGenerationManager {

    private static final Logger logger = LoggerFactory.getLogger(ParallelDataGenerationManager.class);

    private final ParallelGenerationService parallelService;
    private final PerformanceMonitor performanceMonitor;
    private final GeneratorFactory generatorFactory;

    /**
     * 构造函数
     * 
     * @param threadPoolConfig 线程池配置
     * @param generatorFactory 生成器工厂
     */
    public ParallelDataGenerationManager(ThreadPoolConfig threadPoolConfig, GeneratorFactory generatorFactory) {
        this.parallelService = new ParallelGenerationService(threadPoolConfig);
        this.performanceMonitor = new PerformanceMonitor();
        this.generatorFactory = generatorFactory;
    }

    /**
     * 使用默认配置的构造函数
     * 
     * @param generatorFactory 生成器工厂
     */
    public ParallelDataGenerationManager(GeneratorFactory generatorFactory) {
        this(new ThreadPoolConfig(), generatorFactory);
    }

    /**
     * 并行生成指定类型的数据
     * 
     * @param generatorType 生成器类型
     * @param context       生成上下文
     * @param count         生成数量
     * @param <T>           数据类型
     * @return 生成的数据列表
     * @throws Exception 如果生成过程中出现异常
     */
    @SuppressWarnings("unchecked")
    public <T> List<T> generateData(String generatorType, GenerationContext context, int count) throws Exception {
        // 获取生成器
        DataGenerator<T> generator = (DataGenerator<T>) generatorFactory.getGenerator(generatorType);
        if (generator == null) {
            throw new IllegalArgumentException("Unknown generator type: " + generatorType);
        }

        // 记录开始时间
        String operationId = performanceMonitor.recordGenerationStart(generatorType, count);

        try {
            logger.info("Starting parallel generation: type={}, count={}", generatorType, count);

            // 执行并行生成
            List<T> results = parallelService.generateParallel(generator, context, count);

            // 记录成功完成
            performanceMonitor.recordGenerationEnd(generatorType, operationId, results.size(), true);

            logger.info("Parallel generation completed successfully: type={}, generated={}",
                    generatorType, results.size());

            return results;

        } catch (Exception e) {
            // 记录失败
            performanceMonitor.recordGenerationEnd(generatorType, operationId, 0, false);

            logger.error("Parallel generation failed: type={}, count={}", generatorType, count, e);
            throw e;
        }
    }

    /**
     * 批量生成多种类型的数据
     * 
     * @param requests 生成请求列表
     * @return 生成结果列表
     */
    public List<GenerationResult> generateBatch(List<GenerationRequest> requests) {
        return requests.parallelStream()
                .map(this::processRequest)
                .toList();
    }

    /**
     * 处理单个生成请求
     */
    private GenerationResult processRequest(GenerationRequest request) {
        try {
            List<?> data = generateData(request.getGeneratorType(), request.getContext(), request.getCount());
            return new GenerationResult(request.getRequestId(), true, data, null);
        } catch (Exception e) {
            return new GenerationResult(request.getRequestId(), false, null, e.getMessage());
        }
    }

    /**
     * 获取生成器的性能统计
     * 
     * @param generatorType 生成器类型
     * @return 性能统计信息
     */
    public PerformanceMonitor.GenerationStats getGeneratorStats(String generatorType) {
        return performanceMonitor.getStats(generatorType);
    }

    /**
     * 获取总体性能统计
     * 
     * @return 总体性能统计信息
     */
    public PerformanceMonitor.OverallStats getOverallStats() {
        return performanceMonitor.getOverallStats();
    }

    /**
     * 获取线程池状态
     * 
     * @return 线程池状态信息
     */
    public ParallelGenerationService.ThreadPoolStatus getThreadPoolStatus() {
        return parallelService.getThreadPoolStatus();
    }

    /**
     * 重置性能统计
     */
    public void resetStats() {
        performanceMonitor.reset();
    }

    /**
     * 关闭管理器
     */
    public void shutdown() {
        logger.info("Shutting down parallel data generation manager");
        parallelService.shutdown();
    }

    /**
     * 生成请求
     */
    public static class GenerationRequest {
        private final String requestId;
        private final String generatorType;
        private final GenerationContext context;
        private final int count;

        public GenerationRequest(String requestId, String generatorType, GenerationContext context, int count) {
            this.requestId = requestId;
            this.generatorType = generatorType;
            this.context = context;
            this.count = count;
        }

        // Getters
        public String getRequestId() {
            return requestId;
        }

        public String getGeneratorType() {
            return generatorType;
        }

        public GenerationContext getContext() {
            return context;
        }

        public int getCount() {
            return count;
        }
    }

    /**
     * 生成结果
     */
    public static class GenerationResult {
        private final String requestId;
        private final boolean success;
        private final List<?> data;
        private final String errorMessage;

        public GenerationResult(String requestId, boolean success, List<?> data, String errorMessage) {
            this.requestId = requestId;
            this.success = success;
            this.data = data;
            this.errorMessage = errorMessage;
        }

        // Getters
        public String getRequestId() {
            return requestId;
        }

        public boolean isSuccess() {
            return success;
        }

        public List<?> getData() {
            return data;
        }

        public String getErrorMessage() {
            return errorMessage;
        }

        @Override
        public String toString() {
            return String.format("GenerationResult{requestId='%s', success=%s, dataSize=%d, error='%s'}",
                    requestId, success, data != null ? data.size() : 0, errorMessage);
        }
    }
}