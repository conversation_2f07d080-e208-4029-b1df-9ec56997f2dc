package com.dataforge.generators.identifier;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

import static org.assertj.core.api.Assertions.*;

/**
 * UUID生成器单元测试
 */
@DisplayName("UUID生成器测试")
class UUIDGeneratorTest {

    private UUIDGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new UUIDGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext(parameters, 12345L);
    }

    @Test
    @DisplayName("基本UUID生成测试")
    void testBasicGeneration() {
        String uuid = generator.generate(context);

        assertThat(uuid).isNotNull();
        assertThat(uuid).hasSize(36); // 标准UUID格式长度
        assertThat(uuid).matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}");
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证唯一性")
    void testRepeatedGenerationUniqueness() {
        Set<String> generatedUUIDs = new HashSet<>();

        for (int i = 0; i < 100; i++) {
            String uuid = generator.generate(context);
            assertThat(uuid).isNotNull();
            assertThat(generatedUUIDs).doesNotContain(uuid); // 验证唯一性
            generatedUUIDs.add(uuid);
        }
    }

    @Test
    @DisplayName("UUID4类型生成测试")
    void testUUID4Generation() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("version", "4");
        GenerationContext contextV4 = new GenerationContext(parameters, 12345L);

        String uuid = generator.generate(contextV4);

        assertThat(uuid).isNotNull();
        assertThat(uuid.charAt(14)).isEqualTo('4'); // 版本位应该是4
        assertThat(generator.validate(uuid)).isTrue();
    }

    @Test
    @DisplayName("大写格式UUID生成测试")
    void testUppercaseGeneration() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("uppercase", true);
        GenerationContext contextUpper = new GenerationContext(parameters, 12345L);

        String uuid = generator.generate(contextUpper);

        assertThat(uuid).isNotNull();
        assertThat(uuid).matches("[0-9A-F]{8}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{4}-[0-9A-F]{12}");
        assertThat(generator.validate(uuid)).isTrue();
    }

    @Test
    @DisplayName("无连字符UUID生成测试")
    void testNoDashGeneration() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("noDash", true);
        GenerationContext contextNoDash = new GenerationContext(parameters, 12345L);

        String uuid = generator.generate(contextNoDash);

        assertThat(uuid).isNotNull();
        assertThat(uuid).hasSize(32); // 无连字符时长度为32
        assertThat(uuid).matches("[0-9a-f]{32}");
        assertThat(uuid).doesNotContain("-");
        assertThat(generator.validate(uuid)).isTrue();
    }

    @Test
    @DisplayName("ULID格式生成测试")
    void testULIDGeneration() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("format", "ULID");
        GenerationContext contextULID = new GenerationContext(parameters, 12345L);

        String ulid = generator.generate(contextULID);

        assertThat(ulid).isNotNull();
        assertThat(ulid).hasSize(26); // ULID长度为26
        assertThat(ulid).matches("[0-9A-HJKMNP-TV-Z]{26}"); // ULID字符集
        assertThat(generator.validate(ulid)).isTrue();
    }

    @Test
    @DisplayName("UUID校验测试 - 有效UUID")
    void testValidationWithValidUUID() {
        String validUUID = "550e8400-e29b-41d4-a716-************";
        assertThat(generator.validate(validUUID)).isTrue();

        String validUUIDUppercase = "550E8400-E29B-41D4-A716-************";
        assertThat(generator.validate(validUUIDUppercase)).isTrue();
    }

    @Test
    @DisplayName("UUID校验测试 - 无效UUID")
    void testValidationWithInvalidUUID() {
        // 测试长度不正确的UUID
        assertThat(generator.validate("550e8400-e29b-41d4-a716")).isFalse();

        // 测试包含无效字符的UUID
        assertThat(generator.validate("550g8400-e29b-41d4-a716-************")).isFalse();

        // 测试格式不正确的UUID
        assertThat(generator.validate("550e8400e29b41d4a716************")).isFalse(); // 缺少连字符

        // 测试空值
        assertThat(generator.validate(null)).isFalse();
        assertThat(generator.validate("")).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("uuid");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        List<GeneratorParameter> parameters = generator.getSupportedParameters();

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("version", "uppercase", "noDash", "format");
    }

    @Test
    @DisplayName("生成数据格式一致性测试")
    void testGeneratedDataConsistency() {
        Pattern uuidPattern = Pattern.compile("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}");

        for (int i = 0; i < 100; i++) {
            String uuid = generator.generate(context);
            assertThat(uuid).matches(uuidPattern);
            assertThat(generator.validate(uuid)).isTrue();
        }
    }

    @Test
    @DisplayName("Snowflake ID生成测试")
    void testSnowflakeGeneration() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("format", "SNOWFLAKE");
        GenerationContext contextSnowflake = new GenerationContext(parameters, 12345L);

        String snowflakeId = generator.generate(contextSnowflake);

        assertThat(snowflakeId).isNotNull();
        assertThat(snowflakeId).matches("\\d+"); // 纯数字
        assertThat(Long.parseLong(snowflakeId)).isPositive();
        assertThat(generator.validate(snowflakeId)).isTrue();
    }

    @Test
    @DisplayName("不同版本UUID测试")
    void testDifferentVersions() {
        String[] versions = { "1", "4" };

        for (String version : versions) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("version", version);
            GenerationContext versionContext = new GenerationContext(parameters, 12345L);

            String uuid = generator.generate(versionContext);
            assertThat(uuid).isNotNull();
            if ("4".equals(version)) {
                assertThat(uuid.charAt(14)).isEqualTo('4');
            }
            assertThat(generator.validate(uuid)).isTrue();
        }
    }

    @Test
    @DisplayName("组合参数测试")
    void testCombinedParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("version", "4");
        parameters.put("uppercase", true);
        parameters.put("noDash", true);
        GenerationContext combinedContext = new GenerationContext(parameters, 12345L);

        String uuid = generator.generate(combinedContext);

        assertThat(uuid).isNotNull();
        assertThat(uuid).hasSize(32);
        assertThat(uuid).matches("[0-9A-F]{32}");
        assertThat(uuid).doesNotContain("-");
        assertThat(generator.validate(uuid)).isTrue();
    }

    @Test
    @DisplayName("性能测试 - 大量生成")
    void testPerformanceWithLargeGeneration() {
        long startTime = System.currentTimeMillis();
        Set<String> uuids = new HashSet<>();

        for (int i = 0; i < 10000; i++) {
            String uuid = generator.generate(context);
            uuids.add(uuid);
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertThat(uuids).hasSize(10000); // 验证所有UUID都是唯一的
        assertThat(duration).isLessThan(5000); // 应该在5秒内完成
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("version", "invalid");
        parameters.put("format", "INVALID");
        GenerationContext invalidContext = new GenerationContext(parameters, 12345L);

        // 应该能够处理无效参数并生成有效的UUID
        String uuid = generator.generate(invalidContext);
        assertThat(uuid).isNotNull();
        assertThat(generator.validate(uuid)).isTrue();
    }
}