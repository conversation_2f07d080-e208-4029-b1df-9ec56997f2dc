package com.dataforge.generators.identifier;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static org.assertj.core.api.Assertions.*;

/**
 * 银行卡号生成器单元测试
 */
@DisplayName("银行卡号生成器测试")
class BankCardNumberGeneratorTest {

    private BankCardNumberGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new BankCardNumberGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext.Builder()
                .withParameters(parameters)
                .withSeed(12345L)
                .build();
    }

    @Test
    @DisplayName("基本银行卡号生成测试")
    void testBasicGeneration() {
        String bankCard = generator.generate(context);

        assertThat(bankCard).isNotNull();
        assertThat(bankCard).matches("\\d+");
        assertThat(bankCard.length()).isBetween(13, 19); // 银行卡号长度范围
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证Luhn算法")
    void testRepeatedGenerationWithLuhnValidation() {
        String bankCard = generator.generate(context);

        assertThat(bankCard).isNotNull();
        assertThat(generator.validate(bankCard)).isTrue();
        assertThat(isValidLuhn(bankCard)).isTrue();
    }

    @Test
    @DisplayName("指定银行类型生成测试")
    void testGenerationWithBankType() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("bank", "ICBC"); // 工商银行
        GenerationContext contextWithBank = new GenerationContext.Builder()
                .withParameters(parameters)
                .withSeed(12345L)
                .build();

        String bankCard = generator.generate(contextWithBank);

        assertThat(bankCard).isNotNull();
        assertThat(generator.validate(bankCard)).isTrue();
    }

    @Test
    @DisplayName("指定卡号长度生成测试")
    void testGenerationWithSpecificLength() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("cardLength", 16);
        GenerationContext contextWithLength = new GenerationContext.Builder()
                .withParameters(parameters)
                .withSeed(12345L)
                .build();

        String bankCard = generator.generate(contextWithLength);

        assertThat(bankCard).isNotNull();
        assertThat(bankCard).hasSize(16);
        assertThat(generator.validate(bankCard)).isTrue();
    }

    @Test
    @DisplayName("银行卡号校验测试 - 有效号码")
    void testValidationWithValidBankCard() {
        // 使用已知的有效银行卡号进行测试（Luhn算法通过）
        String validBankCard = "****************"; // 测试用Visa卡号
        assertThat(generator.validate(validBankCard)).isTrue();
    }

    @Test
    @DisplayName("银行卡号校验测试 - 无效号码")
    void testValidationWithInvalidBankCard() {
        // 测试长度不正确的银行卡号
        assertThat(generator.validate("123456")).isFalse();

        // 测试包含非数字字符的银行卡号
        assertThat(generator.validate("411111111111111A")).isFalse();

        // 测试Luhn算法失败的银行卡号
        assertThat(generator.validate("****************")).isFalse();
    }

    @Test
    @DisplayName("银行卡号校验测试 - 空值和null")
    void testValidationWithNullAndEmpty() {
        assertThat(generator.validate(null)).isFalse();
        assertThat(generator.validate("")).isFalse();
        assertThat(generator.validate("   ")).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("bankcard");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        GeneratorParameter[] parametersArray = generator.getSupportedParameters();
        List<GeneratorParameter> parameters = Arrays.asList(parametersArray);

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("bank", "cardLength");
    }

    @Test
    @DisplayName("生成数据格式一致性测试")
    void testGeneratedDataConsistency() {
        Pattern bankCardPattern = Pattern.compile("\\d{13,19}");

        for (int i = 0; i < 100; i++) {
            String bankCard = generator.generate(context);
            assertThat(bankCard).matches(bankCardPattern);
            assertThat(generator.validate(bankCard)).isTrue();
        }
    }

    @Test
    @DisplayName("不同银行BIN码测试")
    void testDifferentBankBinCodes() {
        String[] bankTypes = { "ICBC", "CCB", "ABC", "BOC", "COMM" };

        for (String bankType : bankTypes) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("bank", bankType);
            GenerationContext bankContext = new GenerationContext.Builder()
                    .withParameters(parameters)
                    .withSeed(12345L)
                    .build();

            String bankCard = generator.generate(bankContext);
            assertThat(bankCard).isNotNull();
            assertThat(generator.validate(bankCard)).isTrue();
        }
    }

    @Test
    @DisplayName("边界值测试 - 最短和最长卡号")
    void testBoundaryLengths() {
        // 测试最短长度
        Map<String, Object> shortParams = new HashMap<>();
        shortParams.put("cardLength", 13);
        GenerationContext shortContext = new GenerationContext.Builder()
                .withParameters(shortParams)
                .withSeed(12345L)
                .build();

        String shortCard = generator.generate(shortContext);
        assertThat(shortCard).hasSize(13);
        assertThat(generator.validate(shortCard)).isTrue();

        // 测试最长长度
        Map<String, Object> longParams = new HashMap<>();
        longParams.put("cardLength", 19);
        GenerationContext longContext = new GenerationContext.Builder()
                .withParameters(longParams)
                .withSeed(12345L)
                .build();

        String longCard = generator.generate(longContext);
        assertThat(longCard).hasSize(19);
        assertThat(generator.validate(longCard)).isTrue();
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("cardLength", 5); // 无效长度
        GenerationContext invalidContext = new GenerationContext.Builder()
                .withParameters(parameters)
                .withSeed(12345L)
                .build();

        // 应该能够处理无效参数并生成有效的银行卡号
        String bankCard = generator.generate(invalidContext);
        assertThat(bankCard).isNotNull();
        assertThat(generator.validate(bankCard)).isTrue();
    }

    /**
     * Luhn算法验证辅助方法
     */
    private boolean isValidLuhn(String cardNumber) {
        if (cardNumber == null || cardNumber.trim().isEmpty()) {
            return false;
        }

        try {
            int sum = 0;
            boolean alternate = false;

            for (int i = cardNumber.length() - 1; i >= 0; i--) {
                int digit = Character.getNumericValue(cardNumber.charAt(i));

                if (alternate) {
                    digit *= 2;
                    if (digit > 9) {
                        digit = (digit % 10) + 1;
                    }
                }

                sum += digit;
                alternate = !alternate;
            }

            return (sum % 10) == 0;
        } catch (NumberFormatException e) {
            return false;
        }
    }
}