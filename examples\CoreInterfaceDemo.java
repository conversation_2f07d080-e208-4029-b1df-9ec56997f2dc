package examples;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.model.ValidationResult;

/**
 * 核心接口和模型演示
 * 
 * 演示DataGenerator接口、GenerationContext类和配置模型的基本使用方法。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class CoreInterfaceDemo {

    /**
     * 示例数据生成器实现
     */
    public static class SimpleStringGenerator extends AbstractDataGenerator<String> {

        @Override
        protected void initializeParameters() {
            addParameter(GeneratorParameter.optional("prefix", String.class, "data", "字符串前缀"));
            addParameter(GeneratorParameter.optional("length", Integer.class, 10, "字符串长度"));
        }

        @Override
        protected String doGenerate(GenerationContext context) {
            String prefix = getParameter(context, "prefix", "data");
            Integer length = getParameter(context, "length", 10);
            
            StringBuilder sb = new StringBuilder(prefix);
            for (int i = prefix.length(); i < length; i++) {
                sb.append((char) ('a' + getRandom().nextInt(26)));
            }
            
            return sb.toString();
        }

        @Override
        protected boolean doValidate(String data) {
            return data != null && data.length() > 0;
        }

        @Override
        public ValidationResult validateWithDetails(String data) {
            ValidationResult result = new ValidationResult();
            if (data == null) {
                result.addError("数据不能为null");
            } else if (data.isEmpty()) {
                result.addError("数据不能为空字符串");
            }
            return result;
        }

        @Override
        public String getType() {
            return "simple-string";
        }

        @Override
        public String getDescription() {
            return "简单字符串生成器";
        }
    }

    public static void main(String[] args) {
        System.out.println("=== DataForge 核心接口和模型演示 ===\n");

        // 1. 演示DataGenerator接口
        demonstrateDataGenerator();

        // 2. 演示GenerationContext类
        demonstrateGenerationContext();

        // 3. 演示配置模型
        demonstrateConfigurationModels();
    }

    /**
     * 演示DataGenerator接口的使用
     */
    private static void demonstrateDataGenerator() {
        System.out.println("1. DataGenerator接口演示:");
        
        DataGenerator<String> generator = new SimpleStringGenerator();
        
        // 显示生成器信息
        System.out.println("   生成器类型: " + generator.getType());
        System.out.println("   生成器描述: " + generator.getDescription());
        System.out.println("   支持的参数:");
        for (GeneratorParameter param : generator.getSupportedParameters()) {
            System.out.println("     - " + param.getName() + " (" + param.getType().getSimpleName() + 
                             "): " + param.getDescription());
        }

        // 创建生成上下文
        GenerationContext context = new GenerationContext.Builder()
            .withParameter("prefix", "demo")
            .withParameter("length", 15)
            .withSeed(12345L)
            .build();

        // 生成数据
        try {
            String result = generator.generate(context);
            System.out.println("   生成结果: " + result);
            
            // 验证数据
            boolean isValid = generator.validate(result);
            System.out.println("   验证结果: " + (isValid ? "有效" : "无效"));
            
            ValidationResult validationResult = generator.validateWithDetails(result);
            if (!validationResult.isValid()) {
                System.out.println("   验证错误: " + validationResult.getErrorsAsString());
            }
        } catch (Exception e) {
            System.out.println("   生成失败: " + e.getMessage());
        }
        
        System.out.println();
    }

    /**
     * 演示GenerationContext类的使用
     */
    private static void demonstrateGenerationContext() {
        System.out.println("2. GenerationContext类演示:");
        
        // 使用Builder模式创建上下文
        GenerationContext context = new GenerationContext.Builder()
            .withParameter("stringParam", "测试字符串")
            .withParameter("intParam", 42)
            .withParameter("boolParam", true)
            .withSeed(98765L)
            .withRequestId("demo-request-001")
            .build();

        // 演示参数访问
        System.out.println("   请求ID: " + context.getRequestId());
        System.out.println("   随机种子: " + context.getSeed());
        System.out.println("   创建时间: " + context.getCreatedTime());
        
        // 演示参数获取
        String stringParam = context.getParameter("stringParam", String.class);
        Integer intParam = context.getParameter("intParam", Integer.class);
        Boolean boolParam = context.getParameter("boolParam", Boolean.class);
        
        System.out.println("   字符串参数: " + stringParam);
        System.out.println("   整数参数: " + intParam);
        System.out.println("   布尔参数: " + boolParam);
        
        // 演示默认值
        String defaultParam = context.getParameter("nonExistentParam", "默认值");
        System.out.println("   不存在的参数(使用默认值): " + defaultParam);
        
        // 演示随机数生成器
        System.out.println("   随机数示例: " + context.getRandom().nextInt(100));
        
        // 演示子上下文创建
        GenerationContext childContext = context.createChildContext(
            java.util.Map.of("childParam", "子上下文参数")
        );
        System.out.println("   子上下文参数: " + childContext.getParameter("childParam"));
        System.out.println("   继承的参数: " + childContext.getParameter("stringParam"));
        
        System.out.println();
    }

    /**
     * 演示配置模型的使用
     */
    private static void demonstrateConfigurationModels() {
        System.out.println("3. 配置模型演示:");
        
        // 创建生成配置
        GenerationConfig config = new GenerationConfig("simple-string", 100);
        config.addParameter("prefix", "config");
        config.addParameter("length", 20);
        config.setSeed(54321L);
        
        // 配置输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("json");
        outputConfig.setTarget("file");
        outputConfig.setFile("demo-output.json");
        outputConfig.setPretty(true);
        outputConfig.addOption("indent", 2);
        config.setOutputConfig(outputConfig);
        
        // 配置验证
        ValidationConfig validationConfig = new ValidationConfig();
        validationConfig.setEnabled(true);
        validationConfig.setStrictMode(false);
        validationConfig.setFailOnValidationError(false);
        validationConfig.setMaxErrorCount(50);
        config.setValidationConfig(validationConfig);
        
        // 显示配置信息
        System.out.println("   生成配置:");
        System.out.println("     数据类型: " + config.getDataType());
        System.out.println("     生成数量: " + config.getCount());
        System.out.println("     随机种子: " + config.getSeed());
        System.out.println("     参数数量: " + config.getParameters().size());
        
        System.out.println("   输出配置:");
        System.out.println("     输出格式: " + config.getOutputConfig().getFormat());
        System.out.println("     输出目标: " + config.getOutputConfig().getTarget());
        System.out.println("     输出文件: " + config.getOutputConfig().getFile());
        System.out.println("     格式化输出: " + config.getOutputConfig().isPretty());
        
        System.out.println("   验证配置:");
        System.out.println("     启用验证: " + config.getValidationConfig().isEnabled());
        System.out.println("     严格模式: " + config.getValidationConfig().isStrictMode());
        System.out.println("     错误时停止: " + config.getValidationConfig().isFailOnValidationError());
        System.out.println("     最大错误数: " + config.getValidationConfig().getMaxErrorCount());
        
        // 验证配置
        ValidationResult configValidation = config.validate();
        System.out.println("   配置验证结果: " + (configValidation.isValid() ? "有效" : "无效"));
        if (!configValidation.isValid()) {
            System.out.println("   配置错误: " + configValidation.getErrorsAsString());
        }
        
        // 演示配置复制
        GenerationConfig configCopy = config.copy();
        System.out.println("   配置复制成功: " + (configCopy.equals(config) ? "是" : "否"));
        
        System.out.println();
    }
}