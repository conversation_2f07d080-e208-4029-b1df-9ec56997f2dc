package com.dataforge.performance.baseline;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.PerformanceConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.service.DataForgeService;
import com.dataforge.core.service.GeneratorFactory;
import com.dataforge.core.service.DataRelationManager;
import com.dataforge.core.relation.ConsistencyManager;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;

/**
 * 性能回归测试基线管理器
 * 
 * 用于建立和维护性能测试的基线数据，检测性能回归
 */
public class PerformanceBaseline {

    private static final String BASELINE_FILE = "performance-baseline.properties";
    private static final String BASELINE_HISTORY_DIR = "baseline-history";
    private static final DateTimeFormatter TIMESTAMP_FORMAT = DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss");

    private final DataForgeService dataForgeService;
    private final Properties baselineData;
    private final Path baselineFile;
    private final Path historyDir;

    public PerformanceBaseline() {
        GeneratorFactory generatorFactory = new GeneratorFactory();
        DataRelationManager relationManager = new DataRelationManager();
        ConsistencyManager consistencyManager = new ConsistencyManager();
        this.dataForgeService = new DataForgeService(generatorFactory, relationManager, consistencyManager);
        
        this.baselineData = new Properties();
        this.baselineFile = Paths.get(BASELINE_FILE);
        this.historyDir = Paths.get(BASELINE_HISTORY_DIR);
        
        loadBaseline();
        ensureHistoryDirectory();
    }

    /**
     * 建立性能基线
     */
    public void establishBaseline() {
        System.out.println("正在建立性能基线...");
        
        Map<String, PerformanceMetrics> metrics = new HashMap<>();
        
        // 测试各种数据类型的性能
        metrics.put("uuid_small", measurePerformance("uuid", 1000, false));
        metrics.put("uuid_medium", measurePerformance("uuid", 10000, false));
        metrics.put("uuid_large", measurePerformance("uuid", 100000, true));
        
        metrics.put("email_small", measurePerformance("email", 1000, false));
        metrics.put("email_medium", measurePerformance("email", 10000, false));
        metrics.put("email_large", measurePerformance("email", 100000, true));
        
        metrics.put("idcard_small", measurePerformance("idcard", 1000, false));
        metrics.put("idcard_medium", measurePerformance("idcard", 10000, false));
        metrics.put("idcard_large", measurePerformance("idcard", 50000, true));
        
        metrics.put("name_small", measurePerformance("name", 1000, false));
        metrics.put("name_medium", measurePerformance("name", 10000, false));
        metrics.put("name_large", measurePerformance("name", 100000, true));
        
        metrics.put("phone_small", measurePerformance("phone", 1000, false));
        metrics.put("phone_medium", measurePerformance("phone", 10000, false));
        metrics.put("phone_large", measurePerformance("phone", 100000, true));
        
        // 保存基线数据
        saveBaseline(metrics);
        
        System.out.println("性能基线建立完成！");
        printBaseline(metrics);
    }

    /**
     * 检查性能回归
     */
    public RegressionResult checkRegression() {
        System.out.println("正在检查性能回归...");
        
        if (baselineData.isEmpty()) {
            System.out.println("警告: 未找到性能基线数据，请先建立基线");
            return new RegressionResult(false, "未找到基线数据");
        }
        
        Map<String, PerformanceMetrics> currentMetrics = new HashMap<>();
        Map<String, Double> regressions = new HashMap<>();
        boolean hasRegression = false;
        
        // 重新测试所有基线项目
        for (String key : baselineData.stringPropertyNames()) {
            if (key.endsWith("_throughput")) {
                String testKey = key.replace("_throughput", "");
                String[] parts = testKey.split("_");
                if (parts.length >= 2) {
                    String dataType = parts[0];
                    String size = parts[1];
                    
                    int count = getCountForSize(size);
                    boolean parallel = size.equals("large");
                    
                    PerformanceMetrics current = measurePerformance(dataType, count, parallel);
                    currentMetrics.put(testKey, current);
                    
                    double baselineThroughput = Double.parseDouble(baselineData.getProperty(key));
                    double regressionPercent = ((baselineThroughput - current.throughput) / baselineThroughput) * 100;
                    
                    regressions.put(testKey, regressionPercent);
                    
                    // 如果性能下降超过10%，认为是回归
                    if (regressionPercent > 10) {
                        hasRegression = true;
                        System.out.printf("性能回归检测: %s - 吞吐量下降 %.2f%% (基线: %.2f, 当前: %.2f)%n",
                                testKey, regressionPercent, baselineThroughput, current.throughput);
                    }
                }
            }
        }
        
        // 保存当前测试结果到历史记录
        saveCurrentResults(currentMetrics);
        
        if (!hasRegression) {
            System.out.println("未检测到性能回归");
        }
        
        return new RegressionResult(hasRegression, regressions);
    }

    /**
     * 更新基线
     */
    public void updateBaseline() {
        // 备份当前基线
        backupCurrentBaseline();
        
        // 重新建立基线
        establishBaseline();
    }

    /**
     * 测量性能指标
     */
    private PerformanceMetrics measurePerformance(String dataType, int count, boolean parallel) {
        System.out.printf("测试 %s (%d 条记录, %s)...%n", 
                dataType, count, parallel ? "并行" : "串行");
        
        GenerationConfig config = createTestConfig(dataType, count, parallel);
        
        // 预热
        for (int i = 0; i < 3; i++) {
            dataForgeService.generateData(config);
        }
        
        // 正式测试 - 运行5次取平均值
        long totalTime = 0;
        long totalMemory = 0;
        
        for (int i = 0; i < 5; i++) {
            System.gc();
            long memoryBefore = getUsedMemory();
            long startTime = System.currentTimeMillis();
            
            DataForgeService.GenerationResult result = dataForgeService.generateData(config);
            
            long endTime = System.currentTimeMillis();
            long memoryAfter = getUsedMemory();
            
            totalTime += (endTime - startTime);
            totalMemory += (memoryAfter - memoryBefore);
        }
        
        double avgTime = totalTime / 5.0;
        double avgMemory = totalMemory / 5.0;
        double throughput = (count * 1000.0) / avgTime; // 记录/秒
        double memoryPerRecord = avgMemory / count; // 字节/记录
        
        return new PerformanceMetrics(throughput, avgTime, avgMemory, memoryPerRecord);
    }

    /**
     * 创建测试配置
     */
    private GenerationConfig createTestConfig(String dataType, int count, boolean parallel) {
        GenerationConfig config = new GenerationConfig();
        config.setDataType(dataType);
        config.setCount(count);
        config.setSeed(12345L);
        
        ValidationConfig validationConfig = new ValidationConfig();
        validationConfig.setEnabled(false);
        config.setValidationConfig(validationConfig);
        
        PerformanceConfig perfConfig = new PerformanceConfig();
        perfConfig.setEnableParallel(parallel);
        if (parallel) {
            perfConfig.setThreadPoolSize(4);
            perfConfig.setBatchSize(Math.max(1000, count / 10));
        } else {
            perfConfig.setBatchSize(1000);
        }
        config.setPerformanceConfig(perfConfig);
        
        return config;
    }

    /**
     * 获取当前使用的内存量
     */
    private long getUsedMemory() {
        Runtime runtime = Runtime.getRuntime();
        return runtime.totalMemory() - runtime.freeMemory();
    }

    /**
     * 根据大小标识获取记录数量
     */
    private int getCountForSize(String size) {
        switch (size) {
            case "small": return 1000;
            case "medium": return 10000;
            case "large": return size.contains("idcard") ? 50000 : 100000;
            default: return 1000;
        }
    }

    /**
     * 加载基线数据
     */
    private void loadBaseline() {
        if (Files.exists(baselineFile)) {
            try (InputStream input = Files.newInputStream(baselineFile)) {
                baselineData.load(input);
                System.out.println("已加载性能基线数据");
            } catch (IOException e) {
                System.err.println("加载基线数据失败: " + e.getMessage());
            }
        }
    }

    /**
     * 保存基线数据
     */
    private void saveBaseline(Map<String, PerformanceMetrics> metrics) {
        baselineData.clear();
        baselineData.setProperty("baseline.timestamp", LocalDateTime.now().format(TIMESTAMP_FORMAT));
        
        for (Map.Entry<String, PerformanceMetrics> entry : metrics.entrySet()) {
            String key = entry.getKey();
            PerformanceMetrics metric = entry.getValue();
            
            baselineData.setProperty(key + "_throughput", String.valueOf(metric.throughput));
            baselineData.setProperty(key + "_avgTime", String.valueOf(metric.avgTime));
            baselineData.setProperty(key + "_avgMemory", String.valueOf(metric.avgMemory));
            baselineData.setProperty(key + "_memoryPerRecord", String.valueOf(metric.memoryPerRecord));
        }
        
        try (OutputStream output = Files.newOutputStream(baselineFile)) {
            baselineData.store(output, "DataForge Performance Baseline");
            System.out.println("基线数据已保存到: " + baselineFile);
        } catch (IOException e) {
            System.err.println("保存基线数据失败: " + e.getMessage());
        }
    }

    /**
     * 备份当前基线
     */
    private void backupCurrentBaseline() {
        if (Files.exists(baselineFile)) {
            String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
            Path backupFile = historyDir.resolve("baseline_" + timestamp + ".properties");
            
            try {
                Files.copy(baselineFile, backupFile);
                System.out.println("当前基线已备份到: " + backupFile);
            } catch (IOException e) {
                System.err.println("备份基线失败: " + e.getMessage());
            }
        }
    }

    /**
     * 保存当前测试结果
     */
    private void saveCurrentResults(Map<String, PerformanceMetrics> metrics) {
        String timestamp = LocalDateTime.now().format(TIMESTAMP_FORMAT);
        Path resultFile = historyDir.resolve("results_" + timestamp + ".properties");
        
        Properties results = new Properties();
        results.setProperty("test.timestamp", timestamp);
        
        for (Map.Entry<String, PerformanceMetrics> entry : metrics.entrySet()) {
            String key = entry.getKey();
            PerformanceMetrics metric = entry.getValue();
            
            results.setProperty(key + "_throughput", String.valueOf(metric.throughput));
            results.setProperty(key + "_avgTime", String.valueOf(metric.avgTime));
            results.setProperty(key + "_avgMemory", String.valueOf(metric.avgMemory));
            results.setProperty(key + "_memoryPerRecord", String.valueOf(metric.memoryPerRecord));
        }
        
        try (OutputStream output = Files.newOutputStream(resultFile)) {
            results.store(output, "DataForge Performance Test Results");
        } catch (IOException e) {
            System.err.println("保存测试结果失败: " + e.getMessage());
        }
    }

    /**
     * 确保历史记录目录存在
     */
    private void ensureHistoryDirectory() {
        try {
            Files.createDirectories(historyDir);
        } catch (IOException e) {
            System.err.println("创建历史记录目录失败: " + e.getMessage());
        }
    }

    /**
     * 打印基线信息
     */
    private void printBaseline(Map<String, PerformanceMetrics> metrics) {
        System.out.println("\n=== 性能基线报告 ===");
        for (Map.Entry<String, PerformanceMetrics> entry : metrics.entrySet()) {
            String key = entry.getKey();
            PerformanceMetrics metric = entry.getValue();
            
            System.out.printf("%s:%n", key);
            System.out.printf("  吞吐量: %.2f 记录/秒%n", metric.throughput);
            System.out.printf("  平均时间: %.2f 毫秒%n", metric.avgTime);
            System.out.printf("  平均内存: %.2f KB%n", metric.avgMemory / 1024);
            System.out.printf("  每记录内存: %.2f 字节%n", metric.memoryPerRecord);
            System.out.println();
        }
    }

    /**
     * 性能指标数据类
     */
    public static class PerformanceMetrics {
        public final double throughput;      // 吞吐量 (记录/秒)
        public final double avgTime;         // 平均时间 (毫秒)
        public final double avgMemory;       // 平均内存使用 (字节)
        public final double memoryPerRecord; // 每记录内存使用 (字节)

        public PerformanceMetrics(double throughput, double avgTime, double avgMemory, double memoryPerRecord) {
            this.throughput = throughput;
            this.avgTime = avgTime;
            this.avgMemory = avgMemory;
            this.memoryPerRecord = memoryPerRecord;
        }
    }

    /**
     * 回归测试结果类
     */
    public static class RegressionResult {
        public final boolean hasRegression;
        public final Object details;

        public RegressionResult(boolean hasRegression, Object details) {
            this.hasRegression = hasRegression;
            this.details = details;
        }
    }

    /**
     * 主方法 - 用于命令行执行
     */
    public static void main(String[] args) {
        PerformanceBaseline baseline = new PerformanceBaseline();
        
        if (args.length == 0) {
            System.out.println("用法: java PerformanceBaseline [establish|check|update]");
            return;
        }
        
        String command = args[0].toLowerCase();
        switch (command) {
            case "establish":
                baseline.establishBaseline();
                break;
            case "check":
                RegressionResult result = baseline.checkRegression();
                System.exit(result.hasRegression ? 1 : 0);
                break;
            case "update":
                baseline.updateBaseline();
                break;
            default:
                System.out.println("未知命令: " + command);
                System.out.println("支持的命令: establish, check, update");
        }
    }
}