import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.performance.ParallelDataGenerationManager;
import com.dataforge.core.performance.PerformanceMonitor;
import com.dataforge.core.performance.ThreadPoolConfig;
import com.dataforge.core.service.GeneratorFactory;

import java.util.List;

/**
 * 并行数据生成演示
 * 
 * 展示如何使用多线程生成大量数据，并监控性能指标。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ParallelGenerationDemo {

    public static void main(String[] args) {
        System.out.println("=== DataForge 并行数据生成演示 ===\n");

        // 创建生成器工厂
        GeneratorFactory generatorFactory = new GeneratorFactory();

        // 配置线程池
        ThreadPoolConfig threadConfig = new ThreadPoolConfig();
        threadConfig.setCorePoolSize(4);
        threadConfig.setMaximumPoolSize(8);
        threadConfig.setThreadNamePrefix("DataForge-Demo-");

        // 创建并行生成管理器
        ParallelDataGenerationManager manager = new ParallelDataGenerationManager(threadConfig, generatorFactory);

        try {
            // 演示1: 并行生成中文姓名
            demonstrateParallelNameGeneration(manager);

            // 演示2: 并行生成身份证号
            demonstrateParallelIdCardGeneration(manager);

            // 演示3: 批量生成多种类型数据
            demonstrateBatchGeneration(manager);

            // 演示4: 性能对比
            demonstratePerformanceComparison(manager);

            // 显示最终统计信息
            displayFinalStats(manager);

        } catch (Exception e) {
            System.err.println("演示过程中出现错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭管理器
            manager.shutdown();
        }
    }

    /**
     * 演示并行生成中文姓名
     */
    private static void demonstrateParallelNameGeneration(ParallelDataGenerationManager manager) throws Exception {
        System.out.println("1. 并行生成中文姓名演示");
        System.out.println("------------------------");

        GenerationContext context = new GenerationContext.Builder()
                .withParameter("gender", "random")
                .withParameter("nameLength", 2)
                .build();

        long startTime = System.currentTimeMillis();
        List<String> names = manager.generateData("name", context, 10000);
        long endTime = System.currentTimeMillis();

        System.out.println("生成数量: " + names.size());
        System.out.println("耗时: " + (endTime - startTime) + " ms");
        System.out.println("吞吐量: " + (names.size() * 1000.0 / (endTime - startTime)) + " 个/秒");
        System.out.println("示例数据: " + names.subList(0, Math.min(10, names.size())));
        System.out.println();
    }

    /**
     * 演示并行生成身份证号
     */
    private static void demonstrateParallelIdCardGeneration(ParallelDataGenerationManager manager) throws Exception {
        System.out.println("2. 并行生成身份证号演示");
        System.out.println("------------------------");

        GenerationContext context = new GenerationContext.Builder()
                .withParameter("minAge", 18)
                .withParameter("maxAge", 65)
                .withParameter("gender", "random")
                .build();

        long startTime = System.currentTimeMillis();
        List<String> idCards = manager.generateData("idcard", context, 5000);
        long endTime = System.currentTimeMillis();

        System.out.println("生成数量: " + idCards.size());
        System.out.println("耗时: " + (endTime - startTime) + " ms");
        System.out.println("吞吐量: " + (idCards.size() * 1000.0 / (endTime - startTime)) + " 个/秒");
        System.out.println("示例数据: " + idCards.subList(0, Math.min(5, idCards.size())));
        System.out.println();
    }

    /**
     * 演示批量生成多种类型数据
     */
    private static void demonstrateBatchGeneration(ParallelDataGenerationManager manager) {
        System.out.println("3. 批量生成多种类型数据演示");
        System.out.println("----------------------------");

        // 创建多个生成请求
        List<ParallelDataGenerationManager.GenerationRequest> requests = List.of(
                new ParallelDataGenerationManager.GenerationRequest(
                        "req1", "name", 
                        new GenerationContext.Builder().withParameter("gender", "male").build(), 
                        1000
                ),
                new ParallelDataGenerationManager.GenerationRequest(
                        "req2", "phone", 
                        new GenerationContext.Builder().withParameter("format", "standard").build(), 
                        1000
                ),
                new ParallelDataGenerationManager.GenerationRequest(
                        "req3", "email", 
                        new GenerationContext.Builder().withParameter("domain", "random").build(), 
                        1000
                )
        );

        long startTime = System.currentTimeMillis();
        List<ParallelDataGenerationManager.GenerationResult> results = manager.generateBatch(requests);
        long endTime = System.currentTimeMillis();

        System.out.println("批量生成完成，耗时: " + (endTime - startTime) + " ms");
        for (ParallelDataGenerationManager.GenerationResult result : results) {
            System.out.println("  " + result);
        }
        System.out.println();
    }

    /**
     * 演示性能对比（单线程 vs 多线程）
     */
    private static void demonstratePerformanceComparison(ParallelDataGenerationManager manager) throws Exception {
        System.out.println("4. 性能对比演示（大数据量生成）");
        System.out.println("--------------------------------");

        GenerationContext context = new GenerationContext.Builder()
                .withParameter("gender", "random")
                .build();

        int largeCount = 50000;

        // 多线程生成
        long parallelStart = System.currentTimeMillis();
        List<String> parallelResults = manager.generateData("name", context, largeCount);
        long parallelEnd = System.currentTimeMillis();
        long parallelTime = parallelEnd - parallelStart;

        System.out.println("多线程生成:");
        System.out.println("  数量: " + parallelResults.size());
        System.out.println("  耗时: " + parallelTime + " ms");
        System.out.println("  吞吐量: " + (parallelResults.size() * 1000.0 / parallelTime) + " 个/秒");

        // 显示线程池状态
        ParallelGenerationService.ThreadPoolStatus status = manager.getThreadPoolStatus();
        System.out.println("  线程池状态: " + status);
        System.out.println();
    }

    /**
     * 显示最终统计信息
     */
    private static void displayFinalStats(ParallelDataGenerationManager manager) {
        System.out.println("5. 最终性能统计");
        System.out.println("----------------");

        // 总体统计
        PerformanceMonitor.OverallStats overallStats = manager.getOverallStats();
        System.out.println("总体统计: " + overallStats);

        // 各生成器统计
        String[] generatorTypes = {"name", "idcard", "phone", "email"};
        for (String type : generatorTypes) {
            PerformanceMonitor.GenerationStats stats = manager.getGeneratorStats(type);
            if (stats != null) {
                System.out.println(type + " 生成器: " + stats);
            }
        }

        System.out.println();
        System.out.println("=== 演示完成 ===");
    }
}