package com.dataforge.generators.numeric;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * 年龄生成器单元测试
 */
@DisplayName("年龄生成器测试")
class AgeGeneratorTest {

    private AgeGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new AgeGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext(parameters, 12345L);
    }

    @Test
    @DisplayName("基本年龄生成测试")
    void testBasicGeneration() {
        Integer age = generator.generate(context);

        assertThat(age).isNotNull();
        assertThat(age).isBetween(0, 120); // 合理的年龄范围
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证范围")
    void testRepeatedGeneration() {
        Integer age = generator.generate(context);

        assertThat(age).isNotNull();
        assertThat(age).isPositive();
        assertThat(generator.validate(age)).isTrue();
    }

    @Test
    @DisplayName("指定年龄范围生成测试")
    void testGenerationWithRange() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("minAge", 18);
        parameters.put("maxAge", 65);
        GenerationContext contextWithRange = new GenerationContext(parameters, 12345L);

        Integer age = generator.generate(contextWithRange);

        assertThat(age).isNotNull();
        assertThat(age).isBetween(18, 65);
        assertThat(generator.validate(age)).isTrue();
    }

    @Test
    @DisplayName("指定年龄分布生成测试")
    void testGenerationWithDistribution() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("distribution", "NORMAL");
        parameters.put("mean", 35);
        parameters.put("stdDev", 10);
        GenerationContext contextWithDist = new GenerationContext(parameters, 12345L);

        Integer age = generator.generate(contextWithDist);

        assertThat(age).isNotNull();
        assertThat(age).isPositive();
        assertThat(generator.validate(age)).isTrue();
    }

    @Test
    @DisplayName("年龄校验测试 - 有效年龄")
    void testValidationWithValidAge() {
        Integer[] validAges = { 0, 1, 18, 25, 65, 100, 120 };

        for (Integer age : validAges) {
            assertThat(generator.validate(age)).isTrue();
        }
    }

    @Test
    @DisplayName("年龄校验测试 - 无效年龄")
    void testValidationWithInvalidAge() {
        Integer[] invalidAges = { -1, -10, 150, 200 };

        for (Integer age : invalidAges) {
            assertThat(generator.validate(age)).isFalse();
        }

        // 测试null值
        assertThat(generator.validate(null)).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("age");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        List<GeneratorParameter> parameters = generator.getSupportedParameters();

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("minAge", "maxAge", "distribution", "mean", "stdDev");
    }

    @Test
    @DisplayName("生成数据范围一致性测试")
    void testGeneratedDataConsistency() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("minAge", 20);
        parameters.put("maxAge", 60);
        GenerationContext rangeContext = new GenerationContext(parameters, 12345L);

        for (int i = 0; i < 100; i++) {
            Integer age = generator.generate(rangeContext);
            assertThat(age).isBetween(20, 60);
            assertThat(generator.validate(age)).isTrue();
        }
    }

    @Test
    @DisplayName("不同分布类型测试")
    void testDifferentDistributions() {
        String[] distributions = { "UNIFORM", "NORMAL", "EXPONENTIAL" };

        for (String distribution : distributions) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("distribution", distribution);
            parameters.put("minAge", 18);
            parameters.put("maxAge", 80);
            GenerationContext distContext = new GenerationContext(parameters, 12345L);

            Integer age = generator.generate(distContext);
            assertThat(age).isNotNull();
            assertThat(age).isBetween(18, 80);
            assertThat(generator.validate(age)).isTrue();
        }
    }

    @Test
    @DisplayName("年龄分布统计测试")
    void testAgeDistributionStatistics() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("minAge", 0);
        parameters.put("maxAge", 100);
        parameters.put("distribution", "UNIFORM");
        GenerationContext uniformContext = new GenerationContext(parameters, 12345L);

        int[] ageBuckets = new int[10]; // 10个年龄段
        int totalCount = 10000;

        for (int i = 0; i < totalCount; i++) {
            Integer age = generator.generate(uniformContext);
            int bucket = Math.min(age / 10, 9); // 0-9, 10-19, ..., 90+
            ageBuckets[bucket]++;
        }

        // 对于均匀分布，每个年龄段的数量应该相对均匀
        for (int count : ageBuckets) {
            assertThat(count).isBetween(800, 1200); // 允许20%的偏差
        }
    }

    @Test
    @DisplayName("正态分布测试")
    void testNormalDistribution() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("distribution", "NORMAL");
        parameters.put("mean", 40);
        parameters.put("stdDev", 10);
        parameters.put("minAge", 0);
        parameters.put("maxAge", 100);
        GenerationContext normalContext = new GenerationContext(parameters, 12345L);

        double sum = 0;
        int count = 1000;

        for (int i = 0; i < count; i++) {
            Integer age = generator.generate(normalContext);
            sum += age;
        }

        double actualMean = sum / count;
        // 允许5%的误差
        assertThat(actualMean).isBetween(38.0, 42.0);
    }

    @Test
    @DisplayName("边界值测试")
    void testBoundaryValues() {
        // 测试最小年龄
        Map<String, Object> minParams = new HashMap<>();
        minParams.put("minAge", 0);
        minParams.put("maxAge", 0);
        GenerationContext minContext = new GenerationContext(minParams, 12345L);

        Integer minAge = generator.generate(minContext);
        assertThat(minAge).isEqualTo(0);

        // 测试最大年龄
        Map<String, Object> maxParams = new HashMap<>();
        maxParams.put("minAge", 120);
        maxParams.put("maxAge", 120);
        GenerationContext maxContext = new GenerationContext(maxParams, 12345L);

        Integer maxAge = generator.generate(maxContext);
        assertThat(maxAge).isEqualTo(120);
    }

    @Test
    @DisplayName("年龄段分类测试")
    void testAgeCategories() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("category", "ADULT");
        GenerationContext adultContext = new GenerationContext(parameters, 12345L);

        Integer age = generator.generate(adultContext);
        assertThat(age).isBetween(18, 65); // 成年人年龄范围

        // 测试儿童年龄段
        parameters.put("category", "CHILD");
        GenerationContext childContext = new GenerationContext(parameters, 12345L);

        age = generator.generate(childContext);
        assertThat(age).isBetween(0, 17);

        // 测试老年人年龄段
        parameters.put("category", "SENIOR");
        GenerationContext seniorContext = new GenerationContext(parameters, 12345L);

        age = generator.generate(seniorContext);
        assertThat(age).isGreaterThanOrEqualTo(65);
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("minAge", 50);
        parameters.put("maxAge", 30); // 最小值大于最大值
        parameters.put("distribution", "INVALID");
        GenerationContext invalidContext = new GenerationContext(parameters, 12345L);

        // 应该能够处理无效参数并生成有效的年龄
        Integer age = generator.generate(invalidContext);
        assertThat(age).isNotNull();
        assertThat(generator.validate(age)).isTrue();
    }

    @Test
    @DisplayName("性能测试")
    void testPerformance() {
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 100000; i++) {
            Integer age = generator.generate(context);
            assertThat(age).isNotNull();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertThat(duration).isLessThan(2000); // 应该在2秒内完成
    }

    @Test
    @DisplayName("随机性测试")
    void testRandomness() {
        java.util.Set<Integer> ages = new java.util.HashSet<>();

        for (int i = 0; i < 1000; i++) {
            Integer age = generator.generate(context);
            ages.add(age);
        }

        // 应该生成多种不同的年龄值
        assertThat(ages.size()).isGreaterThan(50);
    }

    @Test
    @DisplayName("种子一致性测试")
    void testSeedConsistency() {
        Map<String, Object> parameters = new HashMap<>();
        GenerationContext context1 = new GenerationContext(parameters, 12345L);
        GenerationContext context2 = new GenerationContext(parameters, 12345L);

        Integer age1 = generator.generate(context1);
        Integer age2 = generator.generate(context2);

        // 相同种子应该产生相同结果
        assertThat(age1).isEqualTo(age2);
    }

    @Test
    @DisplayName("年龄与生日关联测试")
    void testAgeWithBirthDate() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("basedOnBirthDate", true);
        parameters.put("birthYear", 1990);
        GenerationContext birthContext = new GenerationContext(parameters, 12345L);

        Integer age = generator.generate(birthContext);

        // 基于1990年出生，当前年龄应该在合理范围内
        int currentYear = java.time.Year.now().getValue();
        int expectedAge = currentYear - 1990;
        assertThat(age).isBetween(expectedAge - 1, expectedAge + 1);
    }

    @Test
    @DisplayName("特殊年龄值测试")
    void testSpecialAgeValues() {
        // 测试法定年龄
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("legalAge", true);
        GenerationContext legalContext = new GenerationContext(parameters, 12345L);

        Integer age = generator.generate(legalContext);
        assertThat(age).isGreaterThanOrEqualTo(18); // 法定成年年龄

        // 测试退休年龄
        parameters.clear();
        parameters.put("retirementAge", true);
        GenerationContext retireContext = new GenerationContext(parameters, 12345L);

        age = generator.generate(retireContext);
        assertThat(age).isBetween(55, 70); // 退休年龄范围
    }
}