package com.dataforge.cli;

import com.dataforge.core.model.GenerationConfig;
import org.apache.commons.cli.ParseException;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import static org.junit.jupiter.api.Assertions.*;

/**
 * CLI集成测试
 */
public class CLIIntegrationTest {

    private CommandLineInterface cli;
    private HelpFormatter helpFormatter;

    @BeforeEach
    void setUp() {
        cli = new CommandLineInterface();
        helpFormatter = new HelpFormatter();
    }

    @Test
    void testHelpRequest() {
        String[] args = { "--help" };
        assertTrue(cli.isHelpRequested(args));
    }

    @Test
    void testVersionRequest() {
        String[] args = { "--version" };
        assertTrue(cli.isVersionRequested(args));
    }

    @Test
    void testBasicParameterParsing() throws ParseException {
        String[] args = { "--type", "name", "--count", "10", "--format", "json" };

        GenerationConfig config = cli.parseArguments(args);

        assertNotNull(config);
        assertEquals("name", config.getDataType());
        assertEquals(10, config.getCount());
        assertEquals("json", config.getOutputConfig().getFormat());
    }

    @Test
    void testHelpGeneration() {
        String helpText = helpFormatter.generateHelpText();

        assertNotNull(helpText);
        assertTrue(helpText.contains("DataForge"));
        assertTrue(helpText.contains("--help"));
        assertTrue(helpText.contains("--type"));
        assertTrue(helpText.contains("--count"));
    }

    @Test
    void testVersionGeneration() {
        String versionText = helpFormatter.generateVersionText();

        assertNotNull(versionText);
        assertTrue(versionText.contains("DataForge"));
    }

    @Test
    void testComplexParameterParsing() throws ParseException {
        String[] args = {
                "--type", "phone",
                "--count", "50",
                "--format", "csv",
                "--output", "test.csv",
                "--phone.region", "CN",
                "--validate", "true"
        };

        GenerationConfig config = cli.parseArguments(args);

        assertNotNull(config);
        assertEquals("phone", config.getDataType());
        assertEquals(50, config.getCount());
        assertEquals("csv", config.getOutputConfig().getFormat());
        assertEquals("test.csv", config.getOutputConfig().getFile());
        assertTrue(config.getValidationConfig().isEnabled());

        // 检查参数映射
        assertTrue(config.getParameters().containsKey("phone.region"));
        assertEquals("CN", config.getParameters().get("phone.region"));
    }

    @Test
    void testInvalidParameterHandling() {
        String[] args = { "--invalid-option", "value" };

        assertThrows(ParseException.class, () -> {
            cli.parseArguments(args);
        });
    }
}