import com.dataforge.core.model.GenerationContext;
import com.dataforge.generators.security.SqlInjectionPayloadGenerator;
import com.dataforge.generators.security.XssAttackScriptGenerator;
import com.dataforge.generators.security.PathTraversalGenerator;

/**
 * 安全测试数据生成演示
 * 
 * 展示DataForge安全测试数据生成器的各种功能，包括SQL注入、
 * XSS攻击脚本和路径穿越等安全测试payload的生成。
 * 
 * ⚠️ 警告：本演示生成的数据仅用于合法的安全测试目的！
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class SecurityTestingDemo {

    public static void main(String[] args) {
        System.out.println("=== DataForge 安全测试数据生成演示 ===");
        System.out.println("⚠️  警告：本演示仅用于合法的安全测试目的！");
        System.out.println("⚠️  请确保在授权的测试环境中使用！\n");

        // 演示1: SQL注入payload生成
        demonstrateSqlInjectionPayloads();

        // 演示2: XSS攻击脚本生成
        demonstrateXssAttackScripts();

        // 演示3: 路径穿越数据生成
        demonstratePathTraversalPayloads();

        // 演示4: 综合安全测试场景
        demonstrateComprehensiveSecurityTesting();

        System.out.println("=== 安全测试演示完成 ===");
        System.out.println("⚠️  请负责任地使用这些测试数据！");
    }

    /**
     * 演示SQL注入payload生成
     */
    private static void demonstrateSqlInjectionPayloads() {
        System.out.println("1. SQL注入Payload生成演示");
        System.out.println("---------------------------");

        SqlInjectionPayloadGenerator sqlGenerator = new SqlInjectionPayloadGenerator();

        // 基础Union注入
        GenerationContext unionContext = new GenerationContext.Builder()
                .withParameter("injectionType", SqlInjectionPayloadGenerator.InjectionType.UNION_BASED)
                .withParameter("databaseType", SqlInjectionPayloadGenerator.DatabaseType.MYSQL)
                .withParameter("complexity", 1)
                .build();

        System.out.println("Union Based注入 (MySQL):");
        for (int i = 0; i < 3; i++) {
            System.out.println("  " + sqlGenerator.generate(unionContext));
        }

        // 时间盲注
        GenerationContext timeContext = new GenerationContext.Builder()
                .withParameter("injectionType", SqlInjectionPayloadGenerator.InjectionType.TIME_BASED)
                .withParameter("databaseType", SqlInjectionPayloadGenerator.DatabaseType.POSTGRESQL)
                .withParameter("complexity", 2)
                .build();

        System.out.println("\nTime Based盲注 (PostgreSQL):");
        for (int i = 0; i < 3; i++) {
            System.out.println("  " + sqlGenerator.generate(timeContext));
        }

        // 编码绕过
        GenerationContext encodedContext = new GenerationContext.Builder()
                .withParameter("injectionType", SqlInjectionPayloadGenerator.InjectionType.BOOLEAN_BASED)
                .withParameter("databaseType", SqlInjectionPayloadGenerator.DatabaseType.MSSQL)
                .withParameter("encodingType", SqlInjectionPayloadGenerator.EncodingType.URL)
                .withParameter("includeComments", true)
                .build();

        System.out.println("\nURL编码绕过 (MSSQL):");
        for (int i = 0; i < 2; i++) {
            System.out.println("  " + sqlGenerator.generate(encodedContext));
        }

        System.out.println();
    }

    /**
     * 演示XSS攻击脚本生成
     */
    private static void demonstrateXssAttackScripts() {
        System.out.println("2. XSS攻击脚本生成演示");
        System.out.println("-----------------------");

        XssAttackScriptGenerator xssGenerator = new XssAttackScriptGenerator();

        // 基础Script标签XSS
        GenerationContext scriptContext = new GenerationContext.Builder()
                .withParameter("xssType", XssAttackScriptGenerator.XssType.REFLECTED)
                .withParameter("tagType", XssAttackScriptGenerator.TagType.SCRIPT)
                .withParameter("complexity", 1)
                .build();

        System.out.println("Script标签XSS:");
        for (int i = 0; i < 3; i++) {
            System.out.println("  " + xssGenerator.generate(scriptContext));
        }

        // IMG标签XSS
        GenerationContext imgContext = new GenerationContext.Builder()
                .withParameter("xssType", XssAttackScriptGenerator.XssType.STORED)
                .withParameter("tagType", XssAttackScriptGenerator.TagType.IMG)
                .withParameter("useRandomEvent", true)
                .withParameter("complexity", 2)
                .build();

        System.out.println("\nIMG标签XSS:");
        for (int i = 0; i < 3; i++) {
            System.out.println("  " + xssGenerator.generate(imgContext));
        }

        // SVG标签XSS with绕过
        GenerationContext svgContext = new GenerationContext.Builder()
                .withParameter("xssType", XssAttackScriptGenerator.XssType.DOM_BASED)
                .withParameter("tagType", XssAttackScriptGenerator.TagType.SVG)
                .withParameter("bypassType", XssAttackScriptGenerator.BypassType.MIXED_CASE)
                .withParameter("complexity", 3)
                .build();

        System.out.println("\nSVG标签XSS (大小写绕过):");
        for (int i = 0; i < 2; i++) {
            System.out.println("  " + xssGenerator.generate(svgContext));
        }

        // 编码绕过XSS
        GenerationContext encodedXssContext = new GenerationContext.Builder()
                .withParameter("tagType", XssAttackScriptGenerator.TagType.IFRAME)
                .withParameter("bypassType", XssAttackScriptGenerator.BypassType.URL_ENCODING)
                .build();

        System.out.println("\nURL编码绕过XSS:");
        for (int i = 0; i < 2; i++) {
            System.out.println("  " + xssGenerator.generate(encodedXssContext));
        }

        System.out.println();
    }

    /**
     * 演示路径穿越数据生成
     */
    private static void demonstratePathTraversalPayloads() {
        System.out.println("3. 路径穿越数据生成演示");
        System.out.println("-----------------------");

        PathTraversalGenerator pathGenerator = new PathTraversalGenerator();

        // Windows系统文件穿越
        GenerationContext windowsContext = new GenerationContext.Builder()
                .withParameter("osType", PathTraversalGenerator.OsType.WINDOWS)
                .withParameter("traversalType", PathTraversalGenerator.TraversalType.BASIC)
                .withParameter("targetFileType", PathTraversalGenerator.TargetFileType.SYSTEM)
                .withParameter("depth", 5)
                .build();

        System.out.println("Windows系统文件穿越:");
        for (int i = 0; i < 3; i++) {
            System.out.println("  " + pathGenerator.generate(windowsContext));
        }

        // Unix密码文件穿越
        GenerationContext unixContext = new GenerationContext.Builder()
                .withParameter("osType", PathTraversalGenerator.OsType.UNIX)
                .withParameter("traversalType", PathTraversalGenerator.TraversalType.DEEP)
                .withParameter("targetFileType", PathTraversalGenerator.TargetFileType.PASSWORD)
                .build();

        System.out.println("\nUnix密码文件穿越:");
        for (int i = 0; i < 3; i++) {
            System.out.println("  " + pathGenerator.generate(unixContext));
        }

        // URL编码穿越
        GenerationContext encodedPathContext = new GenerationContext.Builder()
                .withParameter("osType", PathTraversalGenerator.OsType.MIXED)
                .withParameter("traversalType", PathTraversalGenerator.TraversalType.URL_ENCODED)
                .withParameter("targetFileType", PathTraversalGenerator.TargetFileType.CONFIG)
                .withParameter("encodingType", PathTraversalGenerator.EncodingType.DOUBLE_URL)
                .build();

        System.out.println("\n双重URL编码穿越:");
        for (int i = 0; i < 2; i++) {
            System.out.println("  " + pathGenerator.generate(encodedPathContext));
        }

        // 空字节注入穿越
        GenerationContext nullByteContext = new GenerationContext.Builder()
                .withParameter("traversalType", PathTraversalGenerator.TraversalType.NULL_BYTE)
                .withParameter("targetFileType", PathTraversalGenerator.TargetFileType.LOG)
                .withParameter("depth", 3)
                .build();

        System.out.println("\n空字节注入穿越:");
        for (int i = 0; i < 2; i++) {
            System.out.println("  " + pathGenerator.generate(nullByteContext));
        }

        System.out.println();
    }

    /**
     * 演示综合安全测试场景
     */
    private static void demonstrateComprehensiveSecurityTesting() {
        System.out.println("4. 综合安全测试场景演示");
        System.out.println("-----------------------");

        SqlInjectionPayloadGenerator sqlGen = new SqlInjectionPayloadGenerator();
        XssAttackScriptGenerator xssGen = new XssAttackScriptGenerator();
        PathTraversalGenerator pathGen = new PathTraversalGenerator();

        System.out.println("Web应用安全测试套件:");

        // 登录绕过测试
        System.out.println("\n登录绕过测试:");
        GenerationContext loginBypassContext = new GenerationContext.Builder()
                .withParameter("injectionType", SqlInjectionPayloadGenerator.InjectionType.BOOLEAN_BASED)
                .withParameter("complexity", 1)
                .build();
        
        String[] loginPayloads = {
            "admin' OR '1'='1'--",
            "admin' OR 1=1#",
            "' OR ''='"
        };
        
        for (String payload : loginPayloads) {
            System.out.println("  用户名: " + payload);
        }

        // 文件上传绕过测试
        System.out.println("\n文件上传绕过测试:");
        GenerationContext fileUploadContext = new GenerationContext.Builder()
                .withParameter("tagType", XssAttackScriptGenerator.TagType.SCRIPT)
                .withParameter("complexity", 2)
                .build();

        String[] fileNames = {
            "shell.php.jpg",
            "shell.php%00.jpg",
            "shell.php;.jpg",
            "shell.php\u0000.jpg"
        };

        for (String fileName : fileNames) {
            System.out.println("  文件名: " + fileName);
        }

        // 目录遍历测试
        System.out.println("\n目录遍历测试:");
        GenerationContext dirTraversalContext = new GenerationContext.Builder()
                .withParameter("traversalType", PathTraversalGenerator.TraversalType.MIXED)
                .withParameter("targetFileType", PathTraversalGenerator.TargetFileType.CONFIG)
                .build();

        for (int i = 0; i < 3; i++) {
            System.out.println("  " + pathGen.generate(dirTraversalContext));
        }

        // 反射型XSS测试
        System.out.println("\n反射型XSS测试:");
        GenerationContext reflectedXssContext = new GenerationContext.Builder()
                .withParameter("xssType", XssAttackScriptGenerator.XssType.REFLECTED)
                .withParameter("tagType", XssAttackScriptGenerator.TagType.IMG)
                .withParameter("bypassType", XssAttackScriptGenerator.BypassType.HTML_ENTITY)
                .build();

        for (int i = 0; i < 2; i++) {
            System.out.println("  " + xssGen.generate(reflectedXssContext));
        }

        System.out.println("\n测试建议:");
        System.out.println("  1. 在隔离的测试环境中进行测试");
        System.out.println("  2. 获得适当的授权后再进行测试");
        System.out.println("  3. 记录所有测试活动和发现的问题");
        System.out.println("  4. 及时修复发现的安全漏洞");
        System.out.println("  5. 定期更新测试payload库");

        System.out.println();
    }
}