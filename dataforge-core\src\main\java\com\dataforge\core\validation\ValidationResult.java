package com.dataforge.core.validation;

import java.util.Objects;

/**
 * 校验结果
 * 
 * 封装单个数据项的校验结果，包括校验状态和错误信息。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ValidationResult {

    private final boolean valid;
    private final String errorMessage;
    private final String errorCode;
    private final Object validatedValue;

    /**
     * 私有构造函数
     * 
     * @param valid          是否有效
     * @param errorMessage   错误消息
     * @param errorCode      错误代码
     * @param validatedValue 被校验的值
     */
    private ValidationResult(boolean valid, String errorMessage, String errorCode, Object validatedValue) {
        this.valid = valid;
        this.errorMessage = errorMessage;
        this.errorCode = errorCode;
        this.validatedValue = validatedValue;
    }

    /**
     * 创建成功的校验结果
     * 
     * @param validatedValue 被校验的值
     * @return 成功的校验结果
     */
    public static ValidationResult success(Object validatedValue) {
        return new ValidationResult(true, null, null, validatedValue);
    }

    /**
     * 创建成功的校验结果（无值）
     * 
     * @return 成功的校验结果
     */
    public static ValidationResult success() {
        return new ValidationResult(true, null, null, null);
    }

    /**
     * 创建成功的校验结果（带消息）
     * 
     * @param message        成功消息
     * @param validatedValue 被校验的值
     * @return 成功的校验结果
     */
    public static ValidationResult success(String message, Object validatedValue) {
        return new ValidationResult(true, message, null, validatedValue);
    }

    /**
     * 创建失败的校验结果
     * 
     * @param errorMessage 错误消息
     * @return 失败的校验结果
     */
    public static ValidationResult failure(String errorMessage) {
        return new ValidationResult(false, errorMessage, null, null);
    }

    /**
     * 创建失败的校验结果
     * 
     * @param errorMessage   错误消息
     * @param validatedValue 被校验的值
     * @return 失败的校验结果
     */
    public static ValidationResult failure(String errorMessage, Object validatedValue) {
        return new ValidationResult(false, errorMessage, null, validatedValue);
    }

    /**
     * 创建失败的校验结果
     * 
     * @param errorMessage   错误消息
     * @param errorCode      错误代码
     * @param validatedValue 被校验的值
     * @return 失败的校验结果
     */
    public static ValidationResult failure(String errorMessage, String errorCode, Object validatedValue) {
        return new ValidationResult(false, errorMessage, errorCode, validatedValue);
    }

    /**
     * 检查是否有效
     * 
     * @return 如果有效返回true，否则返回false
     */
    public boolean isValid() {
        return valid;
    }

    /**
     * 检查是否无效
     * 
     * @return 如果无效返回true，否则返回false
     */
    public boolean isInvalid() {
        return !valid;
    }

    /**
     * 获取错误消息
     * 
     * @return 错误消息，如果没有错误返回null
     */
    public String getErrorMessage() {
        return errorMessage;
    }

    /**
     * 获取错误列表（兼容方法）
     * 
     * @return 错误列表，如果没有错误返回空列表
     */
    public java.util.List<String> getErrors() {
        if (valid || errorMessage == null || errorMessage.trim().isEmpty()) {
            return java.util.Collections.emptyList();
        }
        return java.util.Collections.singletonList(errorMessage);
    }

    /**
     * 获取错误代码
     * 
     * @return 错误代码，如果没有错误返回null
     */
    public String getErrorCode() {
        return errorCode;
    }

    /**
     * 获取被校验的值
     * 
     * @return 被校验的值
     */
    public Object getValidatedValue() {
        return validatedValue;
    }

    /**
     * 检查是否有错误消息
     * 
     * @return 如果有错误消息返回true，否则返回false
     */
    public boolean hasErrorMessage() {
        return errorMessage != null && !errorMessage.trim().isEmpty();
    }

    /**
     * 检查是否有错误代码
     * 
     * @return 如果有错误代码返回true，否则返回false
     */
    public boolean hasErrorCode() {
        return errorCode != null && !errorCode.trim().isEmpty();
    }

    /**
     * 获取详细的错误描述
     * 
     * @return 错误描述
     */
    public String getDetailedError() {
        if (valid) {
            return "校验通过";
        }

        StringBuilder sb = new StringBuilder();
        if (hasErrorMessage()) {
            sb.append(errorMessage);
        }

        if (hasErrorCode()) {
            if (sb.length() > 0) {
                sb.append(" (错误代码: ").append(errorCode).append(")");
            } else {
                sb.append("错误代码: ").append(errorCode);
            }
        }

        if (sb.length() == 0) {
            sb.append("校验失败");
        }

        return sb.toString();
    }

    @Override
    public boolean equals(Object o) {
        if (this == o)
            return true;
        if (o == null || getClass() != o.getClass())
            return false;
        ValidationResult that = (ValidationResult) o;
        return valid == that.valid &&
                Objects.equals(errorMessage, that.errorMessage) &&
                Objects.equals(errorCode, that.errorCode) &&
                Objects.equals(validatedValue, that.validatedValue);
    }

    @Override
    public int hashCode() {
        return Objects.hash(valid, errorMessage, errorCode, validatedValue);
    }

    @Override
    public String toString() {
        if (valid) {
            return "ValidationResult{valid=true, value=" + validatedValue + "}";
        } else {
            StringBuilder sb = new StringBuilder();
            sb.append("ValidationResult{valid=false");

            if (hasErrorMessage()) {
                sb.append(", error='").append(errorMessage).append("'");
            }

            if (hasErrorCode()) {
                sb.append(", code='").append(errorCode).append("'");
            }

            if (validatedValue != null) {
                sb.append(", value=").append(validatedValue);
            }

            sb.append("}");
            return sb.toString();
        }
    }
}