package com.dataforge.cli;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import java.util.Arrays;
import java.util.List;

/**
 * 帮助信息格式化器
 * 
 * 负责生成和显示命令行帮助信息、版本信息和使用示例。
 * 提供友好的用户界面和详细的参数说明。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class HelpFormatter {

    @Value("${dataforge.cli.version:1.0.0-SNAPSHOT}")
    private String version;

    @Value("${dataforge.cli.help.width:80}")
    private int helpWidth;

    @Value("${dataforge.cli.help.left-pad:2}")
    private int leftPadding;

    /**
     * 构造函数
     */
    public HelpFormatter() {
    }

    /**
     * 打印帮助信息
     */
    public void printHelp() {
        System.out.println(generateHelpText());
    }

    /**
     * 打印版本信息
     */
    public void printVersion() {
        System.out.println(generateVersionText());
    }

    /**
     * 生成帮助文本
     * 
     * @return 帮助文本
     */
    public String generateHelpText() {
        StringBuilder sb = new StringBuilder();

        // 标题和版本
        sb.append("DataForge - 高效测试数据生成工具\n");
        sb.append("版本: ").append(version).append("\n\n");

        // 使用方法
        sb.append("使用方法:\n");
        sb.append("  java -jar dataforge-cli.jar [选项]\n\n");

        // 描述
        sb.append("描述:\n");
        sb.append("  DataForge 是一款高效、灵活且高度可配置的测试数据生成工具，\n");
        sb.append("  专注于为软件测试团队提供高质量、真实且多样化的测试数据。\n\n");

        // 基本选项
        sb.append("基本选项:\n");
        sb.append(formatOption("-h, --help", "显示此帮助信息"));
        sb.append(formatOption("-v, --version", "显示版本信息"));
        sb.append(formatOption("-c, --config <file>", "指定配置文件路径"));
        sb.append("\n");

        // 数据生成选项
        sb.append("数据生成选项:\n");
        sb.append(formatOption("-t, --type <type>", "数据类型 (name, phone, email, idcard, bankcard, etc.)"));
        sb.append(formatOption("-n, --count <number>", "生成数据数量 (默认: 10)"));
        sb.append(formatOption("-s, --seed <number>", "随机种子"));
        sb.append("\n");

        // 输出选项
        sb.append("输出选项:\n");
        sb.append(formatOption("-f, --format <format>", "输出格式 (csv, json, xml, sql, console)"));
        sb.append(formatOption("-o, --output <file>", "输出文件路径"));
        sb.append(formatOption("--pretty", "格式化输出 (适用于JSON/XML)"));
        sb.append(formatOption("--encoding <encoding>", "文件编码 (默认: UTF-8)"));
        sb.append("\n");

        // 校验选项
        sb.append("校验选项:\n");
        sb.append(formatOption("--validate <boolean>", "是否启用数据校验 (true/false, 默认: true)"));
        sb.append(formatOption("--strict", "启用严格校验模式"));
        sb.append(formatOption("--skip-invalid", "跳过无效数据"));
        sb.append("\n");

        // 性能选项
        sb.append("性能选项:\n");
        sb.append(formatOption("--threads <number>", "线程池大小"));
        sb.append(formatOption("--batch-size <number>", "批处理大小"));
        sb.append(formatOption("--no-parallel", "禁用并行处理"));
        sb.append("\n");

        // 支持的数据类型
        sb.append("支持的数据类型:\n");
        sb.append(generateDataTypesHelp());
        sb.append("\n");

        // 使用示例
        sb.append("使用示例:\n");
        sb.append(generateExamples());
        sb.append("\n");

        // 更多信息
        sb.append("更多信息:\n");
        sb.append("  项目主页: https://github.com/your-org/dataforge\n");
        sb.append("  问题反馈: https://github.com/your-org/dataforge/issues\n");

        return sb.toString();
    }

    /**
     * 生成版本文本
     * 
     * @return 版本文本
     */
    public String generateVersionText() {
        StringBuilder sb = new StringBuilder();

        sb.append("DataForge ").append(version).append("\n");
        sb.append("高效测试数据生成工具\n\n");

        sb.append("构建信息:\n");
        sb.append("  Java版本: ").append(System.getProperty("java.version")).append("\n");
        sb.append("  操作系统: ").append(System.getProperty("os.name")).append(" ");
        sb.append(System.getProperty("os.version")).append("\n");
        sb.append("  架构: ").append(System.getProperty("os.arch")).append("\n\n");

        sb.append("版权所有 (c) 2025 DataForge Team\n");
        sb.append("根据 Apache License 2.0 许可证分发\n");

        return sb.toString();
    }

    /**
     * 生成数据类型帮助信息
     * 
     * @return 数据类型帮助文本
     */
    private String generateDataTypesHelp() {
        StringBuilder sb = new StringBuilder();

        // 基础信息类
        sb.append("  基础信息类:\n");
        sb.append("    name        - 姓名 (中文/英文)\n");
        sb.append("    phone       - 手机号码\n");
        sb.append("    email       - 邮箱地址\n");
        sb.append("    age         - 年龄\n");
        sb.append("    gender      - 性别\n");
        sb.append("    password    - 密码\n");
        sb.append("    account     - 账号名\n\n");

        // 标识类
        sb.append("  标识类:\n");
        sb.append("    idcard      - 身份证号码\n");
        sb.append("    bankcard    - 银行卡号\n");
        sb.append("    uscc        - 统一社会信用代码\n");
        sb.append("    uuid        - UUID/ULID\n");
        sb.append("    docnum      - 业务单据号\n\n");

        // 网络设备类
        sb.append("  网络设备类:\n");
        sb.append("    ip          - IP地址 (IPv4/IPv6)\n");
        sb.append("    mac         - MAC地址\n");
        sb.append("    url         - URL/URI\n");
        sb.append("    domain      - 域名\n");
        sb.append("    port        - 端口号\n\n");

        // 安全测试类
        sb.append("  安全测试类:\n");
        sb.append("    sql-inject  - SQL注入payload\n");
        sb.append("    xss         - XSS攻击脚本\n");
        sb.append("    path-trav   - 路径穿越数据\n");
        sb.append("    cmd-inject  - 命令注入数据\n");

        return sb.toString();
    }

    /**
     * 生成使用示例
     * 
     * @return 使用示例文本
     */
    private String generateExamples() {
        StringBuilder sb = new StringBuilder();

        List<String> examples = Arrays.asList(
                "# 生成10个中文姓名",
                "java -jar dataforge-cli.jar --type name --count 10",
                "",
                "# 生成100个手机号码并输出到CSV文件",
                "java -jar dataforge-cli.jar --type phone --count 100 --format csv --output phones.csv",
                "",
                "# 生成身份证号码（指定地区）",
                "java -jar dataforge-cli.jar --type idcard --count 50 --idcard.region 110000",
                "",
                "# 生成银行卡号（指定类型和卡组织）",
                "java -jar dataforge-cli.jar --type bankcard --count 20 --bankcard.type CREDIT --bankcard.issuer VISA",
                "",
                "# 使用配置文件生成复杂数据",
                "java -jar dataforge-cli.jar --config user-data.yml",
                "",
                "# 生成大量数据并启用并行处理",
                "java -jar dataforge-cli.jar --type name --count 100000 --threads 4 --batch-size 1000",
                "",
                "# 生成JSON格式的格式化输出",
                "java -jar dataforge-cli.jar --type email --count 5 --format json --pretty",
                "",
                "# 禁用数据校验生成测试数据",
                "java -jar dataforge-cli.jar --type idcard --count 10 --validate false");

        for (String example : examples) {
            if (example.startsWith("#")) {
                sb.append("  ").append(example).append("\n");
            } else if (example.isEmpty()) {
                sb.append("\n");
            } else {
                sb.append("  ").append(example).append("\n");
            }
        }

        return sb.toString();
    }

    /**
     * 格式化选项说明
     * 
     * @param option      选项名称
     * @param description 选项描述
     * @return 格式化后的选项文本
     */
    private String formatOption(String option, String description) {
        StringBuilder sb = new StringBuilder();

        // 添加左边距
        sb.append("  ");

        // 添加选项名称
        sb.append(String.format("%-25s", option));

        // 添加描述
        if (description != null && !description.isEmpty()) {
            // 处理长描述的换行
            String[] words = description.split(" ");
            StringBuilder line = new StringBuilder();
            int currentLength = 27; // 选项名称 + 左边距的长度

            for (String word : words) {
                if (currentLength + word.length() + 1 > helpWidth) {
                    sb.append(line.toString().trim()).append("\n");
                    line = new StringBuilder();
                    sb.append("  ").append(String.format("%-25s", ""));
                    currentLength = 27;
                }
                line.append(word).append(" ");
                currentLength += word.length() + 1;
            }

            if (line.length() > 0) {
                sb.append(line.toString().trim());
            }
        }

        sb.append("\n");
        return sb.toString();
    }

    /**
     * 生成数据类型特定的帮助信息
     * 
     * @param dataType 数据类型
     * @return 特定数据类型的帮助文本
     */
    public String generateDataTypeHelp(String dataType) {
        StringBuilder sb = new StringBuilder();

        sb.append("数据类型: ").append(dataType).append("\n");
        sb.append("=".repeat(dataType.length() + 6)).append("\n\n");

        switch (dataType.toLowerCase()) {
            case "name":
                sb.append(generateNameHelp());
                break;
            case "phone":
                sb.append(generatePhoneHelp());
                break;
            case "email":
                sb.append(generateEmailHelp());
                break;
            case "idcard":
                sb.append(generateIdCardHelp());
                break;
            case "bankcard":
                sb.append(generateBankCardHelp());
                break;
            default:
                sb.append("暂无该数据类型的详细帮助信息。\n");
                sb.append("使用 --help 查看所有支持的数据类型。\n");
                break;
        }

        return sb.toString();
    }

    /**
     * 生成姓名类型的帮助信息
     */
    private String generateNameHelp() {
        StringBuilder sb = new StringBuilder();

        sb.append("描述: 生成中文或英文姓名\n\n");

        sb.append("参数:\n");
        sb.append(formatOption("--name.type <CN|EN|BOTH>", "姓名类型 (默认: BOTH)"));
        sb.append(formatOption("--name.gender <MALE|FEMALE|ANY>", "性别 (默认: ANY)"));
        sb.append("\n");

        sb.append("示例:\n");
        sb.append("  java -jar dataforge-cli.jar --type name --name.type CN --name.gender MALE\n");
        sb.append("  java -jar dataforge-cli.jar --type name --count 20 --name.type EN\n");

        return sb.toString();
    }

    /**
     * 生成手机号类型的帮助信息
     */
    private String generatePhoneHelp() {
        StringBuilder sb = new StringBuilder();

        sb.append("描述: 生成中国大陆手机号码\n\n");

        sb.append("参数:\n");
        sb.append(formatOption("--phone.region <CN>", "手机号地区 (默认: CN)"));
        sb.append(formatOption("--phone.prefix <prefixes>", "手机号前缀列表，逗号分隔 (如: 139,188,170)"));
        sb.append("\n");

        sb.append("示例:\n");
        sb.append("  java -jar dataforge-cli.jar --type phone --phone.prefix 139,188\n");
        sb.append("  java -jar dataforge-cli.jar --type phone --count 50\n");

        return sb.toString();
    }

    /**
     * 生成邮箱类型的帮助信息
     */
    private String generateEmailHelp() {
        StringBuilder sb = new StringBuilder();

        sb.append("描述: 生成邮箱地址\n\n");

        sb.append("参数:\n");
        sb.append(formatOption("--email.domains <domains>", "邮箱域名列表，逗号分隔 (如: qq.com,163.com,gmail.com)"));
        sb.append(formatOption("--email.username-length <min,max>", "用户名长度范围 (如: 6,12)"));
        sb.append("\n");

        sb.append("示例:\n");
        sb.append("  java -jar dataforge-cli.jar --type email --email.domains qq.com,163.com\n");
        sb.append("  java -jar dataforge-cli.jar --type email --email.username-length 8,16\n");

        return sb.toString();
    }

    /**
     * 生成身份证类型的帮助信息
     */
    private String generateIdCardHelp() {
        StringBuilder sb = new StringBuilder();

        sb.append("描述: 生成中国大陆居民身份证号码\n\n");

        sb.append("参数:\n");
        sb.append(formatOption("--idcard.region <code>", "地区代码 (6位数字，如: 110000北京)"));
        sb.append(formatOption("--idcard.birth-date-range <range>", "出生日期范围 (如: 1980-01-01,2000-12-31)"));
        sb.append(formatOption("--idcard.gender <MALE|FEMALE|ANY>", "性别 (默认: ANY)"));
        sb.append("\n");

        sb.append("示例:\n");
        sb.append("  java -jar dataforge-cli.jar --type idcard --idcard.region 110000\n");
        sb.append("  java -jar dataforge-cli.jar --type idcard --idcard.birth-date-range 1990-01-01,2000-12-31\n");

        return sb.toString();
    }

    /**
     * 生成银行卡类型的帮助信息
     */
    private String generateBankCardHelp() {
        StringBuilder sb = new StringBuilder();

        sb.append("描述: 生成银行卡号/信用卡号\n\n");

        sb.append("参数:\n");
        sb.append(formatOption("--bankcard.type <DEBIT|CREDIT|BOTH>", "银行卡类型 (默认: BOTH)"));
        sb.append(formatOption("--bankcard.issuer <VISA|MC|UNIONPAY|JCB|ANY>", "卡组织 (默认: ANY)"));
        sb.append("\n");

        sb.append("示例:\n");
        sb.append("  java -jar dataforge-cli.jar --type bankcard --bankcard.type CREDIT\n");
        sb.append("  java -jar dataforge-cli.jar --type bankcard --bankcard.issuer VISA\n");

        return sb.toString();
    }
}