package com.dataforge.core.generator;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

/**
 * 数据生成器接口
 * 
 * 定义了数据生成器的基本契约，所有具体的数据生成器都必须实现此接口。
 * 提供数据生成、验证和类型信息等核心功能。
 * 
 * @param <T> 生成的数据类型
 * <AUTHOR> Team
 * @version 1.0.0
 */
public interface DataGenerator<T> {

    /**
     * 生成数据
     * 
     * @param context 生成上下文
     * @return 生成的数据
     * @throws GenerationException 当生成过程中发生错误时抛出
     */
    T generate(GenerationContext context) throws GenerationException;

    /**
     * 验证生成的数据
     * 
     * @param data 待验证的数据
     * @return 如果数据有效返回true，否则返回false
     */
    default boolean validate(T data) {
        ValidationResult result = validateWithDetails(data);
        return result.isValid();
    }

    /**
     * 验证生成的数据并返回详细结果
     * 
     * @param data 待验证的数据
     * @return 验证结果
     */
    ValidationResult validateWithDetails(T data);

    /**
     * 获取生成器支持的数据类型
     * 
     * @return 数据类型标识符
     */
    String getType();

    /**
     * 获取生成器的描述信息
     * 
     * @return 描述信息
     */
    String getDescription();

    /**
     * 获取生成器支持的参数列表
     * 
     * @return 参数列表
     */
    GeneratorParameter[] getSupportedParameters();

    /**
     * 检查生成器是否支持指定的参数
     * 
     * @param parameterName 参数名称
     * @return 如果支持返回true，否则返回false
     */
    default boolean supportsParameter(String parameterName) {
        if (parameterName == null || parameterName.trim().isEmpty()) {
            return false;
        }

        GeneratorParameter[] parameters = getSupportedParameters();
        if (parameters == null) {
            return false;
        }

        for (GeneratorParameter parameter : parameters) {
            if (parameterName.equals(parameter.getName())) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取生成器的版本信息
     * 
     * @return 版本信息
     */
    default String getVersion() {
        return "1.0.0";
    }

    /**
     * 检查生成器是否为线程安全的
     * 
     * @return 如果线程安全返回true，否则返回false
     */
    default boolean isThreadSafe() {
        return true;
    }

    /**
     * 初始化生成器
     * 在生成器被使用前调用，用于执行必要的初始化操作
     * 
     * @param context 生成上下文
     * @throws GenerationException 当初始化失败时抛出
     */
    default void initialize(GenerationContext context) throws GenerationException {
        // 默认实现为空，子类可以根据需要重写
    }

    /**
     * 清理生成器资源
     * 在生成器使用完毕后调用，用于清理资源
     */
    default void cleanup() {
        // 默认实现为空，子类可以根据需要重写
    }
}