package com.dataforge.core.service;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.ValidationResult;
import com.dataforge.core.model.FieldConfig;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.model.PerformanceConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * ConfigurationValidator测试类
 */
public class ConfigurationValidatorTest {

    private ConfigurationValidator validator;

    @BeforeEach
    void setUp() {
        validator = new ConfigurationValidator();
    }

    @Test
    void testValidateNullConfiguration() {
        ValidationResult result = validator.validateConfiguration(null);

        assertFalse(result.isValid());
        assertTrue(result.getErrors().contains("配置对象不能为空"));
    }

    @Test
    void testValidateValidConfiguration() {
        GenerationConfig config = new GenerationConfig("name", 10);
        config.getOutputConfig().setFormat("json");

        ValidationResult result = validator.validateConfiguration(config);

        assertTrue(result.isValid());
        assertTrue(result.getErrors().isEmpty());
    }

    @Test
    void testValidateInvalidDataType() {
        GenerationConfig config = new GenerationConfig("invalid_type", 10);

        ValidationResult result = validator.validateConfiguration(config);

        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("不支持的数据类型")));
    }

    @Test
    void testValidateEmptyDataType() {
        GenerationConfig config = new GenerationConfig("", 10);

        ValidationResult result = validator.validateConfiguration(config);

        assertFalse(result.isValid());
        assertTrue(result.getErrors().contains("数据类型不能为空"));
    }

    @Test
    void testValidateInvalidCount() {
        GenerationConfig config = new GenerationConfig("name", 0);

        ValidationResult result = validator.validateConfiguration(config);

        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("生成数量必须大于0")));
    }

    @Test
    void testValidateLargeCount() {
        GenerationConfig config = new GenerationConfig("name", 2000000);

        ValidationResult result = validator.validateConfiguration(config);

        assertTrue(result.isValid()); // 应该有效，但有警告
        assertTrue(result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("生成数量过大")));
    }

    @Test
    void testValidateNegativeSeed() {
        GenerationConfig config = new GenerationConfig("name", 10);
        config.setSeed(-123L);

        ValidationResult result = validator.validateConfiguration(config);

        assertTrue(result.isValid()); // 应该有效，但有警告
        assertTrue(result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("随机种子为负数")));
    }

    @Test
    void testValidateParameters() {
        GenerationConfig config = new GenerationConfig("name", 10);
        config.addParameter("name.type", "CN");
        config.addParameter("name.gender", "MALE");

        ValidationResult result = validator.validateConfiguration(config);

        assertTrue(result.isValid());
    }

    @Test
    void testValidateInvalidParameters() {
        GenerationConfig config = new GenerationConfig("name", 10);
        config.addParameter("name.type", "INVALID");
        config.addParameter("name.gender", "UNKNOWN");

        ValidationResult result = validator.validateConfiguration(config);

        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("name.type") && error.contains("值无效")));
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("name.gender") && error.contains("值无效")));
    }

    @Test
    void testValidateRegionCodeParameter() {
        GenerationConfig config = new GenerationConfig("idcard", 10);
        config.addParameter("idcard.region", "110000"); // 有效

        ValidationResult result = validator.validateConfiguration(config);
        assertTrue(result.isValid());

        // 测试无效地区代码
        config.addParameter("idcard.region", "12345"); // 无效
        result = validator.validateConfiguration(config);
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("身份证地区代码格式无效")));
    }

    @Test
    void testValidateDateRangeParameter() {
        GenerationConfig config = new GenerationConfig("idcard", 10);
        config.addParameter("idcard.birth-date-range", "1990-01-01,2000-12-31"); // 有效

        ValidationResult result = validator.validateConfiguration(config);
        assertTrue(result.isValid());

        // 测试无效日期范围
        config.addParameter("idcard.birth-date-range", "invalid-range");
        result = validator.validateConfiguration(config);
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("出生日期范围格式无效")));
    }

    @Test
    void testValidateLengthRangeParameter() {
        GenerationConfig config = new GenerationConfig("email", 10);
        config.addParameter("email.username-length", "6,12"); // 有效

        ValidationResult result = validator.validateConfiguration(config);
        assertTrue(result.isValid());

        // 测试无效长度范围
        config.addParameter("email.username-length", "12,6"); // min > max
        result = validator.validateConfiguration(config);
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("最小长度不能大于最大长度")));
    }

    @Test
    void testValidateOutputConfiguration() {
        GenerationConfig config = new GenerationConfig("name", 10);

        // 测试无效输出格式
        config.getOutputConfig().setFormat("invalid_format");
        ValidationResult result = validator.validateConfiguration(config);
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("不支持的输出格式")));

        // 测试有效输出格式
        config.getOutputConfig().setFormat("json");
        result = validator.validateConfiguration(config);
        assertTrue(result.isValid());
    }

    @Test
    void testValidateOutputFileExtension() {
        GenerationConfig config = new GenerationConfig("name", 10);
        config.getOutputConfig().setFormat("json");
        config.getOutputConfig().setFile("output.csv"); // 格式不匹配

        ValidationResult result = validator.validateConfiguration(config);

        assertTrue(result.isValid()); // 应该有效，但有警告
        assertTrue(result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("输出格式") && warning.contains("文件扩展名不匹配")));
    }

    @Test
    void testValidatePerformanceConfiguration() {
        GenerationConfig config = new GenerationConfig("name", 10);
        PerformanceConfig perfConfig = config.getPerformanceConfig();

        // 测试无效线程池大小
        perfConfig.setThreadPoolSize(0);
        ValidationResult result = validator.validateConfiguration(config);
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("线程池大小必须大于0")));

        // 测试过大的线程池
        perfConfig.setThreadPoolSize(100);
        result = validator.validateConfiguration(config);
        assertTrue(result.isValid()); // 有效但有警告
        assertTrue(result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("线程池大小") && warning.contains("可能过大")));
    }

    @Test
    void testValidateValidationConfiguration() {
        GenerationConfig config = new GenerationConfig("name", 10);
        ValidationConfig validConfig = config.getValidationConfig();

        // 测试配置冲突
        validConfig.setStrictMode(true);
        validConfig.setSkipInvalidData(true);

        ValidationResult result = validator.validateConfiguration(config);

        assertTrue(result.isValid()); // 有效但有警告
        assertTrue(result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("严格模式和跳过无效数据同时启用")));
    }

    @Test
    void testValidateFieldConfigurations() {
        GenerationConfig config = new GenerationConfig();

        // 创建字段配置
        FieldConfig field1 = new FieldConfig();
        field1.setName("name");
        field1.setType("name");

        FieldConfig field2 = new FieldConfig();
        field2.setName("phone");
        field2.setType("phone");

        config.addField(field1);
        config.addField(field2);

        ValidationResult result = validator.validateConfiguration(config);
        assertTrue(result.isValid());
    }

    @Test
    void testValidateDuplicateFieldNames() {
        GenerationConfig config = new GenerationConfig();

        // 创建重复名称的字段
        FieldConfig field1 = new FieldConfig();
        field1.setName("name");
        field1.setType("name");

        FieldConfig field2 = new FieldConfig();
        field2.setName("name"); // 重复名称
        field2.setType("phone");

        config.addField(field1);
        config.addField(field2);

        ValidationResult result = validator.validateConfiguration(config);
        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("字段名称重复")));
    }

    @Test
    void testValidatePerformanceVsDataSize() {
        // 测试小数据量启用并行处理的警告
        GenerationConfig config = new GenerationConfig("name", 50);
        config.getPerformanceConfig().setEnableParallel(true);

        ValidationResult result = validator.validateConfiguration(config);
        assertTrue(result.isValid());
        assertTrue(result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("数据量较小") && warning.contains("并行处理")));

        // 测试大数据量未启用并行处理的警告
        config.setCount(200000);
        config.getPerformanceConfig().setEnableParallel(false);

        result = validator.validateConfiguration(config);
        assertTrue(result.isValid());
        assertTrue(result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("大数据量") && warning.contains("建议启用并行处理")));
    }

    @Test
    void testValidateStrictModePerformanceWarning() {
        GenerationConfig config = new GenerationConfig("name", 100000);
        ValidationConfig validConfig = config.getValidationConfig();
        validConfig.setEnabled(true);
        validConfig.setStrictMode(true);

        ValidationResult result = validator.validateConfiguration(config);

        assertTrue(result.isValid());
        assertTrue(result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("大数据量下启用严格校验模式")));
    }

    @Test
    void testValidateNullParameters() {
        GenerationConfig config = new GenerationConfig("name", 10);
        config.addParameter("test.param", null);

        ValidationResult result = validator.validateConfiguration(config);

        assertTrue(result.isValid());
        assertTrue(result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("参数") && warning.contains("值为空")));
    }

    @Test
    void testValidateAgeParameters() {
        GenerationConfig config = new GenerationConfig("age", 10);
        config.addParameter("age.min", "200"); // 不合理的年龄
        config.addParameter("age.max", "300");

        ValidationResult result = validator.validateConfiguration(config);

        assertTrue(result.isValid());
        assertTrue(result.getWarnings().stream()
                .anyMatch(warning -> warning.contains("年龄值可能不合理")));
    }

    @Test
    void testValidateBooleanParameters() {
        GenerationConfig config = new GenerationConfig("name", 10);
        config.addParameter("valid", "invalid_boolean");

        ValidationResult result = validator.validateConfiguration(config);

        assertFalse(result.isValid());
        assertTrue(result.getErrors().stream()
                .anyMatch(error -> error.contains("布尔参数值无效")));
    }
}