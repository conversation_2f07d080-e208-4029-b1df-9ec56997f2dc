import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.output.OutputConfig;
import com.dataforge.core.output.OutputWriterFactory;
import com.dataforge.core.service.GeneratorFactory;
import com.dataforge.core.stream.StreamDataGenerator;
import com.dataforge.core.stream.StreamGenerationService;
import com.dataforge.core.stream.StreamOutputProcessor;

import java.util.List;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Stream;

/**
 * 流式处理演示
 * 
 * 展示DataForge流式数据生成和处理的各种功能，
 * 包括大数据量生成、并行处理、批次处理等。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class StreamProcessingDemo {

    public static void main(String[] args) {
        System.out.println("=== DataForge 流式处理演示 ===\n");

        // 创建必要的服务
        GeneratorFactory generatorFactory = new GeneratorFactory();
        StreamGenerationService streamService = new StreamGenerationService(generatorFactory);
        OutputWriterFactory outputWriterFactory = new OutputWriterFactory();
        StreamOutputProcessor outputProcessor = new StreamOutputProcessor(outputWriterFactory);

        try {
            // 演示1: 基本流式生成
            demonstrateBasicStreaming(streamService);

            // 演示2: 并行流处理
            demonstrateParallelStreaming(streamService);

            // 演示3: 批次流处理
            demonstrateBatchStreaming(streamService);

            // 演示4: 大数据量流式输出
            demonstrateLargeDataStreaming(streamService, outputProcessor);

            // 演示5: 带进度跟踪的流式处理
            demonstrateProgressTracking(streamService);

            // 演示6: 内存优化的流式处理
            demonstrateMemoryOptimizedStreaming(streamService);

            // 显示最终统计
            displayFinalStatistics(streamService);

        } catch (Exception e) {
            System.err.println("演示过程中出现错误: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 清理资源
            streamService.cleanup();
        }
    }

    /**
     * 演示基本流式生成
     */
    private static void demonstrateBasicStreaming(StreamGenerationService streamService) {
        System.out.println("1. 基本流式生成演示");
        System.out.println("--------------------");

        GenerationContext context = new GenerationContext.Builder()
                .withParameter("gender", "random")
                .build();

        long startTime = System.currentTimeMillis();

        // 生成数据流并处理
        Stream<String> nameStream = streamService.generateStream("name", context, 10000);
        
        long count = nameStream
                .filter(name -> name.length() >= 2)  // 过滤条件
                .limit(5000)  // 限制数量
                .peek(name -> {
                    // 可以在这里进行实时处理
                })
                .count();

        long endTime = System.currentTimeMillis();

        System.out.println("生成并处理了 " + count + " 个姓名");
        System.out.println("耗时: " + (endTime - startTime) + " ms");
        System.out.println("吞吐量: " + (count * 1000.0 / (endTime - startTime)) + " 个/秒");
        System.out.println();
    }

    /**
     * 演示并行流处理
     */
    private static void demonstrateParallelStreaming(StreamGenerationService streamService) {
        System.out.println("2. 并行流处理演示");
        System.out.println("------------------");

        GenerationContext context = new GenerationContext.Builder()
                .withParameter("minAge", 18)
                .withParameter("maxAge", 65)
                .build();

        // 比较串行和并行处理的性能
        int dataCount = 50000;

        // 串行处理
        long serialStart = System.currentTimeMillis();
        long serialCount = streamService.generateStream("idcard", context, dataCount)
                .mapToInt(String::length)
                .sum();
        long serialEnd = System.currentTimeMillis();

        // 并行处理
        long parallelStart = System.currentTimeMillis();
        long parallelCount = streamService.generateParallelStream("idcard", context, dataCount)
                .parallel()
                .mapToInt(String::length)
                .sum();
        long parallelEnd = System.currentTimeMillis();

        System.out.println("数据量: " + dataCount);
        System.out.println("串行处理:");
        System.out.println("  耗时: " + (serialEnd - serialStart) + " ms");
        System.out.println("  字符总数: " + serialCount);
        System.out.println("并行处理:");
        System.out.println("  耗时: " + (parallelEnd - parallelStart) + " ms");
        System.out.println("  字符总数: " + parallelCount);
        System.out.println("性能提升: " + 
                String.format("%.1fx", (double)(serialEnd - serialStart) / (parallelEnd - parallelStart)));
        System.out.println();
    }

    /**
     * 演示批次流处理
     */
    private static void demonstrateBatchStreaming(StreamGenerationService streamService) {
        System.out.println("3. 批次流处理演示");
        System.out.println("------------------");

        GenerationContext context = new GenerationContext.Builder()
                .withParameter("format", "standard")
                .build();

        long startTime = System.currentTimeMillis();

        // 生成批次数据流
        Stream<StreamDataGenerator.Batch<String>> batchStream = 
                streamService.generateBatchStream("phone", context, 1000, 50);

        AtomicLong totalItems = new AtomicLong(0);
        AtomicLong totalBatches = new AtomicLong(0);

        batchStream.forEach(batch -> {
            totalBatches.incrementAndGet();
            totalItems.addAndGet(batch.size());
            
            // 处理批次数据
            long validPhones = batch.data().stream()
                    .filter(phone -> phone.length() == 11)
                    .count();
            
            if (batch.batchNumber() % 10 == 0) {
                System.out.println("处理批次 " + (batch.batchNumber() + 1) + "/" + batch.totalBatches() + 
                        ", 有效号码: " + validPhones + "/" + batch.size() + 
                        ", 进度: " + String.format("%.1f%%", batch.getProgress()));
            }
        });

        long endTime = System.currentTimeMillis();

        System.out.println("批次处理完成:");
        System.out.println("  总批次数: " + totalBatches.get());
        System.out.println("  总数据量: " + totalItems.get());
        System.out.println("  耗时: " + (endTime - startTime) + " ms");
        System.out.println();
    }

    /**
     * 演示大数据量流式输出
     */
    private static void demonstrateLargeDataStreaming(StreamGenerationService streamService,
                                                     StreamOutputProcessor outputProcessor) {
        System.out.println("4. 大数据量流式输出演示");
        System.out.println("------------------------");

        GenerationContext context = new GenerationContext.Builder()
                .withParameter("domain", "random")
                .build();

        try {
            // 生成大量邮箱数据
            Stream<String> emailStream = streamService.generateStream("email", context, 100000);

            // 配置输出到控制台（实际应用中可以输出到文件）
            OutputConfig outputConfig = new OutputConfig();
            outputConfig.setFormat("console");
            outputConfig.setTarget("stdout");

            long startTime = System.currentTimeMillis();

            // 流式输出处理
            StreamOutputProcessor.StreamOutputResult result = 
                    outputProcessor.processStream(emailStream, List.of("email"), outputConfig);

            long endTime = System.currentTimeMillis();

            System.out.println("大数据量输出完成:");
            System.out.println("  " + result);
            System.out.println("  实际耗时: " + (endTime - startTime) + " ms");

        } catch (Exception e) {
            System.err.println("大数据量输出演示失败: " + e.getMessage());
        }
        System.out.println();
    }

    /**
     * 演示带进度跟踪的流式处理
     */
    private static void demonstrateProgressTracking(StreamGenerationService streamService) {
        System.out.println("5. 带进度跟踪的流式处理演示");
        System.out.println("----------------------------");

        GenerationContext context = new GenerationContext.Builder()
                .withParameter("gender", "random")
                .build();

        long totalCount = 50000;
        System.out.println("开始生成 " + totalCount + " 个姓名，带进度跟踪...");

        long startTime = System.currentTimeMillis();

        // 使用进度回调
        AtomicLong processedCount = new AtomicLong(0);
        Stream<String> progressStream = streamService.generateStreamWithProgress(
                "name", context, totalCount,
                (current, total, percentage) -> {
                    System.out.printf("\r进度: %d/%d (%.1f%%) - 已用时: %.1fs", 
                            current, total, percentage, 
                            (System.currentTimeMillis() - startTime) / 1000.0);
                }
        );

        // 处理数据流
        long uniqueNames = progressStream
                .distinct()
                .peek(name -> processedCount.incrementAndGet())
                .count();

        long endTime = System.currentTimeMillis();

        System.out.println("\n进度跟踪完成:");
        System.out.println("  总生成数: " + totalCount);
        System.out.println("  唯一姓名: " + uniqueNames);
        System.out.println("  重复率: " + String.format("%.2f%%", 
                (totalCount - uniqueNames) * 100.0 / totalCount));
        System.out.println("  总耗时: " + (endTime - startTime) + " ms");
        System.out.println();
    }

    /**
     * 演示内存优化的流式处理
     */
    private static void demonstrateMemoryOptimizedStreaming(StreamGenerationService streamService) {
        System.out.println("6. 内存优化的流式处理演示");
        System.out.println("-------------------------");

        GenerationContext context = new GenerationContext.Builder()
                .withParameter("bank", "random")
                .build();

        // 获取初始内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();

        System.out.println("初始内存使用: " + formatMemory(initialMemory));

        long startTime = System.currentTimeMillis();

        // 使用缓冲流处理大量数据
        long validCards = streamService.generateBufferedStream("bankcard", context, 200000, 2000)
                .filter(card -> card.length() >= 16)  // 过滤有效卡号
                .mapToLong(card -> {
                    // 模拟一些处理逻辑
                    return card.chars().sum();
                })
                .filter(sum -> sum > 0)
                .count();

        long endTime = System.currentTimeMillis();

        // 强制垃圾回收并检查内存使用
        System.gc();
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();

        System.out.println("处理完成:");
        System.out.println("  有效卡号数: " + validCards);
        System.out.println("  处理耗时: " + (endTime - startTime) + " ms");
        System.out.println("  最终内存使用: " + formatMemory(finalMemory));
        System.out.println("  内存增长: " + formatMemory(finalMemory - initialMemory));
        System.out.println();
    }

    /**
     * 显示最终统计信息
     */
    private static void displayFinalStatistics(StreamGenerationService streamService) {
        System.out.println("7. 最终统计信息");
        System.out.println("----------------");

        System.out.println("总生成数据量: " + streamService.getTotalGenerated());
        System.out.println("注册的流式生成器数量: " + streamService.getRegisteredGeneratorCount());

        // 内存使用情况
        Runtime runtime = Runtime.getRuntime();
        long totalMemory = runtime.totalMemory();
        long freeMemory = runtime.freeMemory();
        long usedMemory = totalMemory - freeMemory;

        System.out.println("当前内存使用情况:");
        System.out.println("  已用内存: " + formatMemory(usedMemory));
        System.out.println("  可用内存: " + formatMemory(freeMemory));
        System.out.println("  总内存: " + formatMemory(totalMemory));

        System.out.println();
        System.out.println("=== 流式处理演示完成 ===");
    }

    /**
     * 格式化内存大小显示
     */
    private static String formatMemory(long bytes) {
        if (bytes < 1024) return bytes + " B";
        if (bytes < 1024 * 1024) return String.format("%.1f KB", bytes / 1024.0);
        if (bytes < 1024 * 1024 * 1024) return String.format("%.1f MB", bytes / (1024.0 * 1024));
        return String.format("%.1f GB", bytes / (1024.0 * 1024 * 1024));
    }
}