package com.dataforge.core.integration;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.FieldConfig;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.service.DataForgeService;
import com.dataforge.core.service.ConfigurationManager;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * 端到端数据生成流程集成测试
 */
@DisplayName("端到端数据生成流程集成测试")
class EndToEndDataGenerationTest {

    @TempDir
    Path tempDir;

    private DataForgeService dataForgeService;
    private ConfigurationManager configurationManager;

    @BeforeEach
    void setUp() {
        dataForgeService = new DataForgeService();
        configurationManager = new ConfigurationManager();
    }

    @Test
    @DisplayName("完整数据生成流程测试 - CSV输出")
    void testCompleteDataGenerationFlowWithCsvOutput() throws IOException {
        // 创建生成配置
        GenerationConfig config = createBasicGenerationConfig();

        // 设置CSV输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve("test_output.csv").toString());
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证输出文件
        Path outputFile = tempDir.resolve("test_output.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).isNotEmpty();

        // 验证CSV头部
        String header = lines.get(0);
        assertThat(header).contains("name", "age", "email", "phone");

        // 验证数据行数
        assertThat(lines.size()).isEqualTo(101); // 100条数据 + 1行头部

        // 验证数据格式
        for (int i = 1; i < lines.size(); i++) {
            String[] fields = lines.get(i).split(",");
            assertThat(fields).hasSize(4);

            // 验证年龄是数字
            assertThat(fields[1]).matches("\\d+");

            // 验证邮箱格式
            assertThat(fields[2]).contains("@");

            // 验证手机号格式
            assertThat(fields[3]).matches("1[3-9]\\d{9}");
        }
    }

    @Test
    @DisplayName("完整数据生成流程测试 - JSON输出")
    void testCompleteDataGenerationFlowWithJsonOutput() throws IOException {
        // 创建生成配置
        GenerationConfig config = createBasicGenerationConfig();

        // 设置JSON输出
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("json");
        outputConfig.setFilePath(tempDir.resolve("test_output.json").toString());
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证输出文件
        Path outputFile = tempDir.resolve("test_output.json");
        assertThat(outputFile).exists();

        String content = Files.readString(outputFile);
        assertThat(content).isNotEmpty();

        // 验证JSON格式
        assertThat(content).startsWith("[");
        assertThat(content).endsWith("]");
        assertThat(content).contains("\"name\":");
        assertThat(content).contains("\"age\":");
        assertThat(content).contains("\"email\":");
        assertThat(content).contains("\"phone\":");
    }

    @Test
    @DisplayName("多字段类型数据生成集成测试")
    void testMultiFieldTypeDataGeneration() throws IOException {
        // 创建包含多种字段类型的配置
        GenerationConfig config = new GenerationConfig();
        config.setCount(50);

        // 添加各种类型的字段
        FieldConfig nameField = new FieldConfig("name", "name");
        FieldConfig ageField = new FieldConfig("age", "age");
        FieldConfig emailField = new FieldConfig("email", "email");
        FieldConfig phoneField = new FieldConfig("phone", "phone");
        FieldConfig idCardField = new FieldConfig("idcard", "idcard");
        FieldConfig uuidField = new FieldConfig("uuid", "uuid");
        FieldConfig genderField = new FieldConfig("gender", "gender");

        config.setFields(Arrays.asList(nameField, ageField, emailField, phoneField,
                idCardField, uuidField, genderField));

        // 设置输出配置
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve("multi_field_output.csv").toString());
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证输出
        Path outputFile = tempDir.resolve("multi_field_output.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(51); // 50条数据 + 1行头部

        // 验证头部包含所有字段
        String header = lines.get(0);
        assertThat(header).contains("name", "age", "email", "phone", "idcard", "uuid", "gender");

        // 验证数据完整性
        for (int i = 1; i < lines.size(); i++) {
            String[] fields = lines.get(i).split(",");
            assertThat(fields).hasSize(7);

            // 验证各字段格式
            assertThat(fields[1]).matches("\\d+"); // age
            assertThat(fields[2]).contains("@"); // email
            assertThat(fields[3]).matches("1[3-9]\\d{9}"); // phone
            assertThat(fields[4]).matches("\\d{18}"); // idcard
            assertThat(fields[5]).matches("[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}"); // uuid
            assertThat(fields[6]).isIn("男", "女", "male", "female", "M", "F"); // gender
        }
    }

    @Test
    @DisplayName("配置文件加载集成测试")
    void testConfigurationFileLoadingIntegration() throws IOException {
        // 创建配置文件
        String configContent = """
                count: 20
                fields:
                  - name: "user_name"
                    type: "name"
                  - name: "user_age"
                    type: "age"
                    parameters:
                      minAge: 18
                      maxAge: 65
                  - name: "user_email"
                    type: "email"
                output:
                  format: "csv"
                  filePath: "%s"
                """.formatted(tempDir.resolve("config_test_output.csv").toString());

        Path configFile = tempDir.resolve("test_config.yml");
        Files.writeString(configFile, configContent);

        // 从配置文件加载配置
        GenerationConfig config = configurationManager.loadConfiguration(configFile.toString());

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证输出
        Path outputFile = tempDir.resolve("config_test_output.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(21); // 20条数据 + 1行头部

        // 验证字段名称
        String header = lines.get(0);
        assertThat(header).contains("user_name", "user_age", "user_email");
    }

    @Test
    @DisplayName("数据关联性集成测试")
    void testDataRelationshipIntegration() throws IOException {
        // 创建包含关联字段的配置
        GenerationConfig config = new GenerationConfig();
        config.setCount(30);

        // 添加关联字段
        FieldConfig nameField = new FieldConfig("name", "name");
        FieldConfig genderField = new FieldConfig("gender", "gender");
        FieldConfig ageField = new FieldConfig("age", "age");

        // 设置字段关联
        Map<String, Object> nameParams = Map.of("basedOnGender", true);
        nameField.setParameters(nameParams);

        config.setFields(Arrays.asList(nameField, genderField, ageField));

        // 设置输出配置
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve("relation_test_output.csv").toString());
        config.setOutputConfig(outputConfig);

        // 执行数据生成
        dataForgeService.generateData(config);

        // 验证输出
        Path outputFile = tempDir.resolve("relation_test_output.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(31); // 30条数据 + 1行头部

        // 验证数据关联性（这里只是基本验证，实际关联逻辑需要根据具体实现）
        for (int i = 1; i < lines.size(); i++) {
            String[] fields = lines.get(i).split(",");
            assertThat(fields).hasSize(3);

            // 验证所有字段都有值
            assertThat(fields[0]).isNotEmpty(); // name
            assertThat(fields[1]).isNotEmpty(); // gender
            assertThat(fields[2]).matches("\\d+"); // age
        }
    }

    @Test
    @DisplayName("大数据量生成集成测试")
    void testLargeDataGenerationIntegration() throws IOException {
        // 创建大数据量配置
        GenerationConfig config = createBasicGenerationConfig();
        config.setCount(10000);

        // 设置输出配置
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve("large_data_output.csv").toString());
        config.setOutputConfig(outputConfig);

        // 记录开始时间
        long startTime = System.currentTimeMillis();

        // 执行数据生成
        dataForgeService.generateData(config);

        // 记录结束时间
        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        // 验证输出
        Path outputFile = tempDir.resolve("large_data_output.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(10001); // 10000条数据 + 1行头部

        // 验证性能（应该在合理时间内完成）
        assertThat(duration).isLessThan(30000); // 30秒内完成

        // 验证文件大小合理
        long fileSize = Files.size(outputFile);
        assertThat(fileSize).isGreaterThan(1000000); // 至少1MB
    }

    @Test
    @DisplayName("错误处理集成测试")
    void testErrorHandlingIntegration() {
        // 测试无效配置
        GenerationConfig invalidConfig = new GenerationConfig();
        invalidConfig.setCount(-1); // 无效数量

        assertThatThrownBy(() -> dataForgeService.generateData(invalidConfig))
                .isInstanceOf(IllegalArgumentException.class);

        // 测试无效输出路径
        GenerationConfig config = createBasicGenerationConfig();
        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath("/invalid/path/output.csv");
        config.setOutputConfig(outputConfig);

        assertThatThrownBy(() -> dataForgeService.generateData(config))
                .isInstanceOf(IOException.class);
    }

    @Test
    @DisplayName("并发数据生成集成测试")
    void testConcurrentDataGenerationIntegration() throws InterruptedException {
        int threadCount = 5;
        Thread[] threads = new Thread[threadCount];

        for (int i = 0; i < threadCount; i++) {
            final int threadIndex = i;
            threads[i] = new Thread(() -> {
                try {
                    GenerationConfig config = createBasicGenerationConfig();
                    config.setCount(100);

                    OutputConfig outputConfig = new OutputConfig();
                    outputConfig.setFormat("csv");
                    outputConfig.setFilePath(tempDir.resolve("concurrent_output_" + threadIndex + ".csv").toString());
                    config.setOutputConfig(outputConfig);

                    dataForgeService.generateData(config);

                    // 验证输出文件
                    Path outputFile = tempDir.resolve("concurrent_output_" + threadIndex + ".csv");
                    assertThat(outputFile).exists();

                } catch (Exception e) {
                    fail("并发测试失败: " + e.getMessage());
                }
            });
            threads[i].start();
        }

        // 等待所有线程完成
        for (Thread thread : threads) {
            thread.join();
        }

        // 验证所有输出文件都已创建
        for (int i = 0; i < threadCount; i++) {
            Path outputFile = tempDir.resolve("concurrent_output_" + i + ".csv");
            assertThat(outputFile).exists();
        }
    }

    @Test
    @DisplayName("流式输出集成测试")
    void testStreamingOutputIntegration() throws IOException {
        // 创建流式输出配置
        GenerationConfig config = createBasicGenerationConfig();
        config.setCount(1000);

        OutputConfig outputConfig = new OutputConfig();
        outputConfig.setFormat("csv");
        outputConfig.setFilePath(tempDir.resolve("streaming_output.csv").toString());
        outputConfig.setStreamingEnabled(true);
        outputConfig.setBatchSize(100);
        config.setOutputConfig(outputConfig);

        // 执行流式数据生成
        dataForgeService.generateDataStreaming(config);

        // 验证输出
        Path outputFile = tempDir.resolve("streaming_output.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(1001); // 1000条数据 + 1行头部
    }

    /**
     * 创建基本的生成配置
     */
    private GenerationConfig createBasicGenerationConfig() {
        GenerationConfig config = new GenerationConfig();
        config.setCount(100);

        // 添加基本字段
        FieldConfig nameField = new FieldConfig("name", "name");
        FieldConfig ageField = new FieldConfig("age", "age");
        FieldConfig emailField = new FieldConfig("email", "email");
        FieldConfig phoneField = new FieldConfig("phone", "phone");

        config.setFields(Arrays.asList(nameField, ageField, emailField, phoneField));

        return config;
    }
}