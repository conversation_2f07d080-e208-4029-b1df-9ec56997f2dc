package com.dataforge.core.service;

import com.dataforge.core.generator.DataGenerator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.ServiceLoader;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantReadWriteLock;

/**
 * 生成器工厂
 * 
 * 负责管理和提供各种数据生成器实例。支持通过SPI机制动态加载自定义生成器。
 * 提供生成器注册、获取和缓存功能。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class GeneratorFactory {

    private static final Logger logger = LoggerFactory.getLogger(GeneratorFactory.class);

    // 生成器注册表，使用ConcurrentHashMap保证线程安全
    private final Map<String, DataGenerator<?>> generators = new ConcurrentHashMap<>();

    // 生成器实例缓存，用于缓存已创建的生成器实例
    private final Map<String, DataGenerator<?>> instanceCache = new ConcurrentHashMap<>();

    // 读写锁，用于保护注册操作
    private final ReentrantReadWriteLock lock = new ReentrantReadWriteLock();

    // 生成器注册表
    private final GeneratorRegistry generatorRegistry;

    /**
     * 构造函数
     * 初始化时加载所有内置生成器和SPI生成器
     */
    public GeneratorFactory() {
        this.generatorRegistry = new GeneratorRegistry();

        // 从注册表中加载生成器到工厂
        loadGeneratorsFromRegistry();

        // 加载SPI生成器
        loadSpiGenerators();

        logger.info("生成器工厂初始化完成，已加载{}个生成器", generators.size());
    }

    /**
     * 带注册表的构造函数
     * 
     * @param generatorRegistry 生成器注册表
     */
    public GeneratorFactory(GeneratorRegistry generatorRegistry) {
        this.generatorRegistry = generatorRegistry != null ? generatorRegistry : new GeneratorRegistry();

        // 从注册表中加载生成器到工厂
        loadGeneratorsFromRegistry();

        // 加载SPI生成器
        loadSpiGenerators();

        logger.info("生成器工厂初始化完成，已加载{}个生成器", generators.size());
    }

    /**
     * 获取生成器
     * 
     * @param type 数据类型
     * @param <T>  生成数据类型
     * @return 对应的生成器，如果不存在返回null
     */
    @SuppressWarnings("unchecked")
    public <T> DataGenerator<T> getGenerator(String type) {
        if (type == null || type.trim().isEmpty()) {
            return null;
        }

        String normalizedType = normalizeType(type);

        // 首先从缓存中获取
        lock.readLock().lock();
        try {
            DataGenerator<?> cachedGenerator = instanceCache.get(normalizedType);
            if (cachedGenerator != null) {
                logger.debug("从缓存获取到类型为{}的生成器: {}", type, cachedGenerator.getClass().getSimpleName());
                return (DataGenerator<T>) cachedGenerator;
            }
        } finally {
            lock.readLock().unlock();
        }

        // 缓存中没有，从注册表中获取并创建实例
        DataGenerator<?> generatorTemplate = generators.get(normalizedType);
        if (generatorTemplate == null) {
            logger.warn("找不到类型为{}的生成器", type);
            return null;
        }

        // 创建生成器实例并缓存
        lock.writeLock().lock();
        try {
            // 双重检查，防止并发创建
            DataGenerator<?> cachedGenerator = instanceCache.get(normalizedType);
            if (cachedGenerator != null) {
                return (DataGenerator<T>) cachedGenerator;
            }

            // 创建新实例
            DataGenerator<?> newInstance = createGeneratorInstance(generatorTemplate);
            if (newInstance != null) {
                instanceCache.put(normalizedType, newInstance);
                logger.debug("创建并缓存类型为{}的生成器: {}", type, newInstance.getClass().getSimpleName());
                return (DataGenerator<T>) newInstance;
            }
        } finally {
            lock.writeLock().unlock();
        }

        logger.warn("无法创建类型为{}的生成器实例", type);
        return null;
    }

    /**
     * 注册生成器
     * 
     * @param type      数据类型
     * @param generator 生成器实例
     */
    public void registerGenerator(String type, DataGenerator<?> generator) {
        if (type == null || type.trim().isEmpty() || generator == null) {
            logger.warn("注册生成器失败：类型或生成器为空");
            return;
        }

        // 先注册到注册表
        if (generatorRegistry.registerGenerator(generator)) {
            String normalizedType = normalizeType(type);

            lock.writeLock().lock();
            try {
                generators.put(normalizedType, generator);
                // 清除缓存中的旧实例
                instanceCache.remove(normalizedType);
                logger.info("注册生成器到工厂: {} -> {}", normalizedType, generator.getClass().getSimpleName());
            } finally {
                lock.writeLock().unlock();
            }
        }
    }

    /**
     * 批量注册生成器
     * 
     * @param generatorMap 生成器映射表
     */
    public void registerGenerators(Map<String, DataGenerator<?>> generatorMap) {
        if (generatorMap == null || generatorMap.isEmpty()) {
            return;
        }

        // 先批量注册到注册表
        generatorRegistry.registerGenerators(generatorMap.values());

        lock.writeLock().lock();
        try {
            for (Map.Entry<String, DataGenerator<?>> entry : generatorMap.entrySet()) {
                String type = entry.getKey();
                DataGenerator<?> generator = entry.getValue();

                if (type != null && !type.trim().isEmpty() && generator != null) {
                    String normalizedType = normalizeType(type);
                    generators.put(normalizedType, generator);
                    instanceCache.remove(normalizedType);
                    logger.debug("批量注册生成器到工厂: {} -> {}", normalizedType, generator.getClass().getSimpleName());
                }
            }
            logger.info("批量注册完成，共注册{}个生成器", generatorMap.size());
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 检查是否存在指定类型的生成器
     * 
     * @param type 数据类型
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasGenerator(String type) {
        if (type == null || type.trim().isEmpty()) {
            return false;
        }

        String normalizedType = normalizeType(type);
        return generators.containsKey(normalizedType);
    }

    /**
     * 获取所有已注册的生成器类型
     * 
     * @return 生成器类型集合
     */
    public Iterable<String> getRegisteredTypes() {
        return generators.keySet();
    }

    /**
     * 获取已注册的生成器数量
     * 
     * @return 生成器数量
     */
    public int getGeneratorCount() {
        return generators.size();
    }

    /**
     * 从注册表中加载生成器到工厂
     */
    private void loadGeneratorsFromRegistry() {
        logger.debug("从注册表加载生成器到工厂");

        Map<String, DataGenerator<?>> registryGenerators = generatorRegistry.getAllGenerators();

        lock.writeLock().lock();
        try {
            generators.putAll(registryGenerators);
            logger.info("从注册表加载{}个生成器到工厂", registryGenerators.size());
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取生成器注册表
     * 
     * @return 生成器注册表
     */
    public GeneratorRegistry getGeneratorRegistry() {
        return generatorRegistry;
    }

    /**
     * 加载内置生成器
     */
    private void loadBuiltinGenerators() {
        logger.debug("加载内置生成器");

        try {
            // 尝试通过反射加载内置生成器
            loadGeneratorByReflection("name", "com.dataforge.generators.text.NameGenerator");
            loadGeneratorByReflection("phone", "com.dataforge.generators.text.PhoneGenerator");
            loadGeneratorByReflection("email", "com.dataforge.generators.text.EmailGenerator");
            loadGeneratorByReflection("gender", "com.dataforge.generators.text.GenderGenerator");
            loadGeneratorByReflection("age", "com.dataforge.generators.numeric.AgeGenerator");
            loadGeneratorByReflection("idcard", "com.dataforge.generators.identifier.IdCardNumberGenerator");
            loadGeneratorByReflection("bankcard", "com.dataforge.generators.identifier.BankCardNumberGenerator");
            loadGeneratorByReflection("uuid", "com.dataforge.generators.identifier.UUIDGenerator");
            loadGeneratorByReflection("uscc", "com.dataforge.generators.identifier.USCCGenerator");

            logger.info("内置生成器加载完成");

        } catch (Exception e) {
            logger.error("加载内置生成器时发生异常", e);
        }
    }

    /**
     * 通过SPI机制加载自定义生成器
     */
    private void loadSpiGenerators() {
        logger.debug("通过SPI加载自定义生成器");

        try {
            // 加载GeneratorProvider实现
            ServiceLoader<GeneratorProvider> providerLoader = ServiceLoader.load(GeneratorProvider.class);

            for (GeneratorProvider provider : providerLoader) {
                loadGeneratorsFromProvider(provider);
            }

            // 兼容旧的直接DataGenerator SPI方式
            @SuppressWarnings("rawtypes")
            ServiceLoader<DataGenerator> generatorLoader = ServiceLoader.load(DataGenerator.class);

            for (DataGenerator<?> generator : generatorLoader) {
                String type = generator.getType();
                if (type != null && !type.trim().isEmpty()) {
                    registerGenerator(type, generator);
                } else {
                    logger.warn("跳过无效的SPI生成器: {}", generator.getClass().getName());
                }
            }

        } catch (Exception e) {
            logger.error("加载SPI生成器时发生异常", e);
        }
    }

    /**
     * 从生成器提供者加载生成器
     * 
     * @param provider 生成器提供者
     */
    private void loadGeneratorsFromProvider(GeneratorProvider provider) {
        try {
            logger.debug("加载生成器提供者: {} v{}", provider.getProviderName(), provider.getVersion());

            // 检查提供者是否可用
            if (!provider.isAvailable()) {
                logger.warn("生成器提供者{}不可用，跳过加载", provider.getProviderName());
                return;
            }

            // 初始化提供者
            provider.initialize();

            // 获取并注册生成器
            Collection<DataGenerator<?>> generators = provider.getGenerators();
            if (generators != null && !generators.isEmpty()) {
                int registeredCount = 0;
                for (DataGenerator<?> generator : generators) {
                    String type = generator.getType();
                    if (type != null && !type.trim().isEmpty()) {
                        registerGenerator(type, generator);
                        registeredCount++;
                    } else {
                        logger.warn("跳过无效的生成器: {}", generator.getClass().getName());
                    }
                }
                logger.info("从提供者{}加载了{}个生成器", provider.getProviderName(), registeredCount);
            } else {
                logger.warn("生成器提供者{}没有提供任何生成器", provider.getProviderName());
            }

        } catch (Exception e) {
            logger.error("加载生成器提供者{}时发生异常: {}", provider.getProviderName(), e.getMessage(), e);
        }
    }

    /**
     * 通过反射加载生成器
     * 
     * @param type      生成器类型
     * @param className 生成器类名
     */
    private void loadGeneratorByReflection(String type, String className) {
        try {
            Class<?> clazz = Class.forName(className);
            Object instance = clazz.getDeclaredConstructor().newInstance();
            if (instance instanceof DataGenerator) {
                registerGenerator(type, (DataGenerator<?>) instance);
            } else {
                logger.warn("类{}不是DataGenerator的实例", className);
            }
        } catch (ClassNotFoundException e) {
            logger.debug("生成器类{}未找到，跳过加载", className);
        } catch (Exception e) {
            logger.warn("加载生成器{}时发生异常: {}", className, e.getMessage());
        }
    }

    /**
     * 创建生成器实例
     * 
     * @param template 生成器模板
     * @return 新的生成器实例
     */
    private DataGenerator<?> createGeneratorInstance(DataGenerator<?> template) {
        try {
            // 如果生成器是线程安全的，直接返回模板实例
            if (template.isThreadSafe()) {
                return template;
            }

            // 否则创建新实例
            Class<?> generatorClass = template.getClass();
            return (DataGenerator<?>) generatorClass.getDeclaredConstructor().newInstance();

        } catch (Exception e) {
            logger.error("创建生成器实例失败: {}", template.getClass().getName(), e);
            return null;
        }
    }

    /**
     * 清除缓存
     */
    public void clearCache() {
        lock.writeLock().lock();
        try {
            instanceCache.clear();
            logger.info("生成器实例缓存已清除");
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 清除指定类型的缓存
     * 
     * @param type 数据类型
     */
    public void clearCache(String type) {
        if (type == null || type.trim().isEmpty()) {
            return;
        }

        String normalizedType = normalizeType(type);
        lock.writeLock().lock();
        try {
            instanceCache.remove(normalizedType);
            logger.debug("清除类型为{}的生成器缓存", normalizedType);
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 获取缓存统计信息
     * 
     * @return 缓存大小
     */
    public int getCacheSize() {
        lock.readLock().lock();
        try {
            return instanceCache.size();
        } finally {
            lock.readLock().unlock();
        }
    }

    /**
     * 注销生成器
     * 
     * @param type 数据类型
     * @return 如果成功注销返回true，否则返回false
     */
    public boolean unregisterGenerator(String type) {
        if (type == null || type.trim().isEmpty()) {
            return false;
        }

        String normalizedType = normalizeType(type);

        // 从注册表中注销
        boolean registryRemoved = generatorRegistry.unregisterGenerator(normalizedType);

        lock.writeLock().lock();
        try {
            DataGenerator<?> removed = generators.remove(normalizedType);
            instanceCache.remove(normalizedType);

            if (removed != null || registryRemoved) {
                logger.info("注销生成器: {}", normalizedType);
                return true;
            }
            return false;
        } finally {
            lock.writeLock().unlock();
        }
    }

    /**
     * 标准化类型名称
     * 
     * @param type 原始类型名称
     * @return 标准化后的类型名称
     */
    private String normalizeType(String type) {
        return type.trim().toLowerCase().replaceAll("[-_\\s]+", "-");
    }
}