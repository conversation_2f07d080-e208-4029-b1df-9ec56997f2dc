# DataForge - 高效测试数据生成工具

[![Java](https://img.shields.io/badge/Java-17+-blue.svg)](https://openjdk.java.net/)
[![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.2+-green.svg)](https://spring.io/projects/spring-boot)
[![Maven](https://img.shields.io/badge/Maven-3.6+-red.svg)](https://maven.apache.org/)
[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)

DataForge 是一款高效、灵活且高度可配置的测试数据生成工具，专注于为软件测试团队提供高质量、真实且多样化的测试数据。

## 🚀 核心特性

- **丰富的数据类型支持**: 支持基础信息、标识符、网络设备、文本、数值、时间等多种数据类型
- **中国特定数据**: 专门优化的身份证号、银行卡号、统一社会信用代码等中国特定数据生成
- **高性能生成**: 支持多线程并发生成，单线程可达10万+记录/秒
- **多种输出格式**: 支持CSV、JSON、XML、SQL等多种输出格式
- **灵活配置**: 支持命令行参数和YAML配置文件
- **数据关联**: 支持字段间的逻辑关联，确保数据一致性
- **扩展机制**: 支持SPI机制的自定义生成器扩展
- **安全测试**: 内置SQL注入、XSS等安全测试payload生成器

## 📋 系统要求

- Java 17 或更高版本
- Maven 3.6 或更高版本
- 内存: 建议 2GB 以上
- 磁盘空间: 100MB 以上

## 🛠️ 快速开始

### 构建项目

```bash
# 克隆项目
git clone https://github.com/your-org/dataforge.git
cd dataforge

# 编译项目
mvn clean compile

# 运行测试
mvn test

# 打包项目
mvn clean package
```

### 基本使用

```bash
# 生成10个中文姓名
java -jar dataforge-cli/target/dataforge-cli-1.0.0-SNAPSHOT.jar --type name --count 10

# 生成100个手机号码并输出到CSV文件
java -jar dataforge-cli/target/dataforge-cli-1.0.0-SNAPSHOT.jar --type phone --count 100 --output-format csv --output-file phones.csv

# 生成身份证号码（指定地区）
java -jar dataforge-cli/target/dataforge-cli-1.0.0-SNAPSHOT.jar --type idcard --count 50 --idcard.region 110000

# 使用配置文件生成复杂数据
java -jar dataforge-cli/target/dataforge-cli-1.0.0-SNAPSHOT.jar --config user-data.yml
```

### 配置文件示例

```yaml
# user-data.yml
generation:
  count: 1000
  fields:
    - name: name
      type: name
      parameters:
        type: CN
        gender: ANY
    - name: phone
      type: phone
      parameters:
        region: CN
        valid: true
    - name: email
      type: email
      parameters:
        domains: ["qq.com", "163.com", "gmail.com"]
    - name: age
      type: age
      parameters:
        min: 18
        max: 65

output:
  format: json
  file: users.json
  pretty: true

validation:
  enabled: true
```

## 📚 支持的数据类型

### 基础信息类

- 姓名 (中文/英文)
- 手机号码
- 邮箱地址
- 年龄
- 性别
- 密码
- 账号名

### 标识类

- 身份证号码
- 银行卡号
- 统一社会信用代码
- UUID/ULID
- 业务单据号
- 产品编码

### 网络设备类

- IP地址 (IPv4/IPv6)
- MAC地址
- URL/URI
- 域名
- HTTP头
- Session ID/Token

### 安全测试类

- SQL注入payload
- XSS攻击脚本
- 路径穿越数据
- 命令注入数据

## 🏗️ 项目结构

```
dataforge/
├── dataforge-core/          # 核心模块
│   ├── model/               # 数据模型
│   ├── service/             # 核心服务
│   ├── generator/           # 生成器接口
│   ├── output/              # 输出接口
│   └── util/                # 工具类
├── dataforge-cli/           # 命令行模块
│   ├── cli/                 # CLI接口
│   └── config/              # 配置管理
├── dataforge-generators/    # 生成器实现
│   ├── basic/               # 基础信息生成器
│   ├── identifier/          # 标识类生成器
│   ├── network/             # 网络设备生成器
│   ├── security/            # 安全测试生成器
│   ├── text/                # 文本类生成器
│   ├── numeric/             # 数值类生成器
│   └── datetime/            # 时间日期生成器
└── docs/                    # 文档
```

## 🧪 测试

```bash
# 运行所有测试
mvn test

# 运行特定模块测试
mvn test -pl dataforge-core

# 生成测试报告
mvn test jacoco:report

# 运行性能测试
mvn test -Pperformance
```

## 📖 文档

- [用户手册](docs/user-guide.md)
- [开发者指南](docs/developer-guide.md)
- [API文档](docs/api-reference.md)
- [配置参考](docs/configuration.md)
- [扩展开发](docs/extension-guide.md)

## 🤝 贡献

我们欢迎所有形式的贡献！请查看 [贡献指南](CONTRIBUTING.md) 了解如何参与项目开发。

### 开发环境设置

1. Fork 项目
2. 创建功能分支: `git checkout -b feature/amazing-feature`
3. 提交更改: `git commit -m 'Add amazing feature'`
4. 推送分支: `git push origin feature/amazing-feature`
5. 创建 Pull Request

## 📄 许可证

本项目采用 Apache License 2.0 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

- [Spring Boot](https://spring.io/projects/spring-boot) - 应用框架
- [Apache Commons CLI](https://commons.apache.org/proper/commons-cli/) - 命令行解析
- [Jackson](https://github.com/FasterXML/jackson) - JSON/YAML处理
- [JUnit 5](https://junit.org/junit5/) - 测试框架

## 📞 联系我们

- 项目主页: <https://github.com/your-org/dataforge>
- 问题反馈: <https://github.com/your-org/dataforge/issues>
- 邮箱: <<EMAIL>>

---

**DataForge** - 让测试数据生成变得简单高效！
