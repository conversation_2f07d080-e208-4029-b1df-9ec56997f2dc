package com.dataforge.core.output;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;
import java.util.ServiceLoader;

/**
 * 输出器工厂
 * 
 * 负责管理和创建各种输出器实例。支持通过SPI机制动态加载自定义输出器。
 * 提供输出器注册、获取和格式自动识别功能。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class OutputWriterFactory {

    private static final Logger logger = LoggerFactory.getLogger(OutputWriterFactory.class);

    private final Map<String, Class<? extends OutputWriter>> writerClasses = new HashMap<>();

    /**
     * 构造函数
     * 初始化时加载所有内置输出器和SPI输出器
     */
    public OutputWriterFactory() {
        // 加载内置输出器
        loadBuiltinWriters();

        // 加载SPI输出器
        loadSpiWriters();

        logger.info("输出器工厂初始化完成，已加载{}个输出器", writerClasses.size());
    }

    /**
     * 创建输出器
     * 
     * @param format 输出格式
     * @return 输出器实例，如果不存在返回null
     */
    public OutputWriter createWriter(String format) {
        if (format == null || format.trim().isEmpty()) {
            return null;
        }

        String normalizedFormat = normalizeFormat(format);
        Class<? extends OutputWriter> writerClass = writerClasses.get(normalizedFormat);

        if (writerClass == null) {
            logger.warn("找不到格式为{}的输出器", format);
            return null;
        }

        try {
            OutputWriter writer = writerClass.getDeclaredConstructor().newInstance();
            logger.debug("创建输出器: {} -> {}", format, writer.getClass().getSimpleName());
            return writer;
        } catch (Exception e) {
            logger.error("创建输出器失败: {}", format, e);
            return null;
        }
    }

    /**
     * 创建输出器并初始化
     * 
     * @param config 输出配置
     * @return 初始化后的输出器实例
     * @throws Exception 当创建或初始化失败时抛出
     */
    public OutputWriter createAndInitialize(OutputConfig config) throws Exception {
        if (config == null) {
            throw new IllegalArgumentException("输出配置不能为空");
        }

        String format = detectFormat(config);
        OutputWriter writer = createWriter(format);

        if (writer == null) {
            throw new IllegalArgumentException("不支持的输出格式: " + format);
        }

        writer.initialize(config);
        return writer;
    }

    /**
     * 注册输出器
     * 
     * @param format      输出格式
     * @param writerClass 输出器类
     */
    public void registerWriter(String format, Class<? extends OutputWriter> writerClass) {
        if (format == null || format.trim().isEmpty() || writerClass == null) {
            return;
        }

        String normalizedFormat = normalizeFormat(format);
        writerClasses.put(normalizedFormat, writerClass);
        logger.info("注册输出器: {} -> {}", normalizedFormat, writerClass.getSimpleName());
    }

    /**
     * 检查是否存在指定格式的输出器
     * 
     * @param format 输出格式
     * @return 如果存在返回true，否则返回false
     */
    public boolean hasWriter(String format) {
        if (format == null || format.trim().isEmpty()) {
            return false;
        }

        String normalizedFormat = normalizeFormat(format);
        return writerClasses.containsKey(normalizedFormat);
    }

    /**
     * 获取所有已注册的输出格式
     * 
     * @return 输出格式集合
     */
    public Iterable<String> getRegisteredFormats() {
        return writerClasses.keySet();
    }

    /**
     * 获取已注册的输出器数量
     * 
     * @return 输出器数量
     */
    public int getWriterCount() {
        return writerClasses.size();
    }

    /**
     * 检测输出格式
     * 根据配置信息自动检测合适的输出格式
     * 
     * @param config 输出配置
     * @return 检测到的输出格式
     */
    private String detectFormat(OutputConfig config) {
        String format = config.getFormat();

        // 如果已指定格式，直接使用
        if (format != null && !format.trim().isEmpty()) {
            return format;
        }

        // 根据目标路径推断格式
        String target = config.getTarget();
        if (target != null && !target.trim().isEmpty()) {
            String lowerTarget = target.toLowerCase();

            if (lowerTarget.endsWith(".csv")) {
                return "csv";
            } else if (lowerTarget.endsWith(".json")) {
                return "json";
            } else if (lowerTarget.endsWith(".xml")) {
                return "xml";
            } else if (lowerTarget.endsWith(".txt")) {
                return "text";
            }
        }

        // 默认使用控制台输出
        return "console";
    }

    /**
     * 加载内置输出器
     */
    private void loadBuiltinWriters() {
        logger.debug("加载内置输出器");

        try {
            // 尝试通过反射加载内置输出器
            loadWriterByReflection("console", "com.dataforge.core.output.impl.ConsoleOutputWriter");
            loadWriterByReflection("csv", "com.dataforge.core.output.impl.CsvOutputWriter");
            loadWriterByReflection("json", "com.dataforge.core.output.impl.JsonOutputWriter");

            logger.info("内置输出器加载完成");

        } catch (Exception e) {
            logger.error("加载内置输出器时发生异常", e);
        }
    }

    /**
     * 通过SPI机制加载自定义输出器
     */
    private void loadSpiWriters() {
        logger.debug("通过SPI加载自定义输出器");

        try {
            ServiceLoader<OutputWriter> serviceLoader = ServiceLoader.load(OutputWriter.class);

            for (OutputWriter writer : serviceLoader) {
                String type = writer.getType();
                if (type != null && !type.trim().isEmpty()) {
                    registerWriter(type, writer.getClass());
                } else {
                    logger.warn("跳过无效的SPI输出器: {}", writer.getClass().getName());
                }
            }

        } catch (Exception e) {
            logger.error("加载SPI输出器时发生异常", e);
        }
    }

    /**
     * 通过反射加载输出器
     * 
     * @param format    输出格式
     * @param className 输出器类名
     */
    @SuppressWarnings("unchecked")
    private void loadWriterByReflection(String format, String className) {
        try {
            Class<?> clazz = Class.forName(className);
            if (OutputWriter.class.isAssignableFrom(clazz)) {
                registerWriter(format, (Class<? extends OutputWriter>) clazz);
            } else {
                logger.warn("类{}不是OutputWriter的实现", className);
            }
        } catch (ClassNotFoundException e) {
            logger.debug("输出器类{}未找到，跳过加载", className);
        } catch (Exception e) {
            logger.warn("加载输出器{}时发生异常: {}", className, e.getMessage());
        }
    }

    /**
     * 标准化格式名称
     * 
     * @param format 原始格式名称
     * @return 标准化后的格式名称
     */
    private String normalizeFormat(String format) {
        return format.trim().toLowerCase().replaceAll("[-_\\s]+", "-");
    }
}