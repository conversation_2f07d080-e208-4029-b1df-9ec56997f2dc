package com.dataforge.generators.identifier;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.security.SecureRandom;
import java.util.Random;
import java.util.UUID;
import java.util.regex.Pattern;

/**
 * UUID生成器
 * 
 * 支持多种UUID类型的生成，包括标准UUID4、ULID和Snowflake算法。
 * 提供不同格式的唯一标识符生成功能。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class UUIDGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "uuid";
    private static final String DESCRIPTION = "生成唯一标识符";

    // UUID验证正则表达式
    private static final Pattern UUID_PATTERN = Pattern.compile(
            "^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$");

    // ULID验证正则表达式
    private static final Pattern ULID_PATTERN = Pattern.compile("^[0123456789ABCDEFGHJKMNPQRSTVWXYZ]{26}$");

    // Crockford Base32编码表（用于ULID）
    private static final char[] CROCKFORD_BASE32 = "0123456789ABCDEFGHJKMNPQRSTVWXYZ".toCharArray();

    // Snowflake算法相关常量
    private static final long EPOCH = 1288834974657L; // 2010-11-04 09:42:54.657 UTC
    private static final long WORKER_ID_BITS = 5L;
    private static final long DATACENTER_ID_BITS = 5L;
    private static final long SEQUENCE_BITS = 12L;
    private static final long MAX_WORKER_ID = -1L ^ (-1L << WORKER_ID_BITS);
    private static final long MAX_DATACENTER_ID = -1L ^ (-1L << DATACENTER_ID_BITS);
    private static final long SEQUENCE_MASK = -1L ^ (-1L << SEQUENCE_BITS);
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;
    private static final long DATACENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;
    private static final long TIMESTAMP_LEFT_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATACENTER_ID_BITS;

    private final Random random;
    private final SecureRandom secureRandom;

    // Snowflake算法状态
    private long lastTimestamp = -1L;
    private long sequence = 0L;
    private final long workerId;
    private final long datacenterId;

    /**
     * 构造函数
     */
    public UUIDGenerator() {
        this.random = new Random();
        this.secureRandom = new SecureRandom();
        this.workerId = random.nextInt((int) MAX_WORKER_ID + 1);
        this.datacenterId = random.nextInt((int) MAX_DATACENTER_ID + 1);
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("type", String.class, "uuid4",
                "UUID类型：uuid4（标准UUID4）、ulid（ULID）、snowflake（Snowflake算法）", false));
        addParameter(new GeneratorParameter("format", String.class, "standard",
                "输出格式：standard（标准格式）、compact（紧凑格式，无连字符）、uppercase（大写）", false));
        addParameter(new GeneratorParameter("workerId", Long.class, null,
                "Snowflake算法的工作节点ID（0-31），仅在type为snowflake时有效", false));
        addParameter(new GeneratorParameter("datacenterId", Long.class, null,
                "Snowflake算法的数据中心ID（0-31），仅在type为snowflake时有效", false));
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        String type = context.getParameter("type", "uuid4");
        String format = context.getParameter("format", "standard");
        Long workerId = context.getParameter("workerId", null);
        Long datacenterId = context.getParameter("datacenterId", null);

        // 根据类型生成UUID
        String result;
        switch (type.toLowerCase()) {
            case "uuid4":
                result = generateUUID4();
                break;
            case "ulid":
                result = generateULID();
                break;
            case "snowflake":
                result = generateSnowflake(workerId, datacenterId);
                break;
            default:
                throw new IllegalArgumentException("不支持的UUID类型: " + type);
        }

        // 格式化输出
        return formatOutput(result, format, type);
    }

    /**
     * 生成标准UUID4
     * 
     * @return UUID4字符串
     */
    private String generateUUID4() {
        return UUID.randomUUID().toString();
    }

    /**
     * 生成ULID（Universally Unique Lexicographically Sortable Identifier）
     * 
     * @return ULID字符串
     */
    private String generateULID() {
        // 时间戳部分（48位，毫秒级）
        long timestamp = System.currentTimeMillis();

        // 随机部分（80位）
        byte[] randomBytes = new byte[10];
        secureRandom.nextBytes(randomBytes);

        // 编码为Crockford Base32
        StringBuilder ulid = new StringBuilder(26);

        // 编码时间戳（10个字符）
        encodeTimestamp(ulid, timestamp);

        // 编码随机部分（16个字符）
        encodeRandom(ulid, randomBytes);

        return ulid.toString();
    }

    /**
     * 编码时间戳为ULID格式
     * 
     * @param ulid      输出缓冲区
     * @param timestamp 时间戳
     */
    private void encodeTimestamp(StringBuilder ulid, long timestamp) {
        for (int i = 9; i >= 0; i--) {
            ulid.append(CROCKFORD_BASE32[(int) (timestamp >>> (i * 5)) & 31]);
        }
    }

    /**
     * 编码随机字节为ULID格式
     * 
     * @param ulid        输出缓冲区
     * @param randomBytes 随机字节
     */
    private void encodeRandom(StringBuilder ulid, byte[] randomBytes) {
        // 将10个字节（80位）编码为16个Base32字符
        long value = 0;
        int bits = 0;

        for (byte b : randomBytes) {
            value = (value << 8) | (b & 0xFF);
            bits += 8;

            while (bits >= 5) {
                ulid.append(CROCKFORD_BASE32[(int) (value >>> (bits - 5)) & 31]);
                bits -= 5;
            }
        }

        // 处理剩余位
        if (bits > 0) {
            ulid.append(CROCKFORD_BASE32[(int) (value << (5 - bits)) & 31]);
        }
    }

    /**
     * 生成Snowflake ID
     * 
     * @param workerId     工作节点ID
     * @param datacenterId 数据中心ID
     * @return Snowflake ID字符串
     */
    private synchronized String generateSnowflake(Long workerId, Long datacenterId) {
        long finalWorkerId = workerId != null ? workerId : this.workerId;
        long finalDatacenterId = datacenterId != null ? datacenterId : this.datacenterId;

        // 参数验证
        if (finalWorkerId > MAX_WORKER_ID || finalWorkerId < 0) {
            throw new IllegalArgumentException("工作节点ID必须在0-" + MAX_WORKER_ID + "之间");
        }
        if (finalDatacenterId > MAX_DATACENTER_ID || finalDatacenterId < 0) {
            throw new IllegalArgumentException("数据中心ID必须在0-" + MAX_DATACENTER_ID + "之间");
        }

        long timestamp = System.currentTimeMillis();

        // 时钟回拨检查
        if (timestamp < lastTimestamp) {
            throw new RuntimeException("时钟回拨，拒绝生成ID");
        }

        // 同一毫秒内序列号递增
        if (lastTimestamp == timestamp) {
            sequence = (sequence + 1) & SEQUENCE_MASK;
            if (sequence == 0) {
                // 序列号溢出，等待下一毫秒
                timestamp = waitNextMillis(lastTimestamp);
            }
        } else {
            sequence = 0L;
        }

        lastTimestamp = timestamp;

        // 组装ID
        long id = ((timestamp - EPOCH) << TIMESTAMP_LEFT_SHIFT)
                | (finalDatacenterId << DATACENTER_ID_SHIFT)
                | (finalWorkerId << WORKER_ID_SHIFT)
                | sequence;

        return String.valueOf(id);
    }

    /**
     * 等待下一毫秒
     * 
     * @param lastTimestamp 上次时间戳
     * @return 新的时间戳
     */
    private long waitNextMillis(long lastTimestamp) {
        long timestamp = System.currentTimeMillis();
        while (timestamp <= lastTimestamp) {
            timestamp = System.currentTimeMillis();
        }
        return timestamp;
    }

    /**
     * 格式化输出
     * 
     * @param result 原始结果
     * @param format 格式
     * @param type   UUID类型
     * @return 格式化后的结果
     */
    private String formatOutput(String result, String format, String type) {
        switch (format.toLowerCase()) {
            case "compact":
                if ("uuid4".equals(type)) {
                    return result.replace("-", "");
                }
                return result;
            case "uppercase":
                return result.toUpperCase();
            case "standard":
            default:
                return result;
        }
    }

    @Override
    public ValidationResult validateWithDetails(String data) {
        if (data == null) {
            return ValidationResult.error("UUID不能为空");
        }

        String trimmed = data.trim();
        if (trimmed.isEmpty()) {
            return ValidationResult.error("UUID不能为空");
        }

        // 尝试验证不同类型的UUID
        if (UUID_PATTERN.matcher(trimmed).matches()) {
            // 标准UUID格式
            try {
                UUID.fromString(trimmed);
                return ValidationResult.success();
            } catch (IllegalArgumentException e) {
                return ValidationResult.error("UUID格式不正确");
            }
        } else if (ULID_PATTERN.matcher(trimmed.toUpperCase()).matches()) {
            // ULID格式
            return ValidationResult.success();
        } else if (trimmed.matches("\\d+")) {
            // Snowflake ID格式（纯数字）
            try {
                Long.parseLong(trimmed);
                return ValidationResult.success();
            } catch (NumberFormatException e) {
                return ValidationResult.error("Snowflake ID格式不正确");
            }
        } else if (trimmed.matches("[0-9a-fA-F]{32}")) {
            // 紧凑格式UUID（无连字符）
            return ValidationResult.success();
        } else {
            return ValidationResult.error("不支持的UUID格式");
        }
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }
}