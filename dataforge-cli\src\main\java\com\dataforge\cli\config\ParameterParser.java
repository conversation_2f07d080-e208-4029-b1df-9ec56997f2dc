package com.dataforge.cli.config;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.ValidationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

/**
 * 参数解析器
 * 
 * 负责解析和验证命令行参数，提供参数格式化和类型转换功能。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Component
public class ParameterParser {

    private static final Logger logger = LoggerFactory.getLogger(ParameterParser.class);

    // 参数验证正则表达式
    private static final Pattern PHONE_PREFIX_PATTERN = Pattern.compile("^\\d{3}$");
    private static final Pattern REGION_CODE_PATTERN = Pattern.compile("^\\d{6}$");
    private static final Pattern DATE_RANGE_PATTERN = Pattern.compile("^\\d{4}-\\d{2}-\\d{2},\\d{4}-\\d{2}-\\d{2}$");
    private static final Pattern LENGTH_RANGE_PATTERN = Pattern.compile("^\\d+,\\d+$");

    /**
     * 解析逗号分隔的字符串列表
     * 
     * @param value 逗号分隔的字符串
     * @return 字符串列表
     */
    public List<String> parseStringList(String value) {
        if (value == null || value.trim().isEmpty()) {
            return List.of();
        }

        return Arrays.stream(value.split(","))
                .map(String::trim)
                .filter(s -> !s.isEmpty())
                .toList();
    }

    /**
     * 解析范围参数 (min,max)
     * 
     * @param value 范围字符串
     * @return 包含min和max的数组，如果解析失败返回null
     */
    public int[] parseIntRange(String value) {
        if (value == null || !LENGTH_RANGE_PATTERN.matcher(value).matches()) {
            return null;
        }

        try {
            String[] parts = value.split(",");
            int min = Integer.parseInt(parts[0].trim());
            int max = Integer.parseInt(parts[1].trim());

            if (min > max) {
                logger.warn("范围参数无效，最小值大于最大值: {}", value);
                return null;
            }

            return new int[] { min, max };
        } catch (NumberFormatException e) {
            logger.warn("范围参数格式错误: {}", value);
            return null;
        }
    }

    /**
     * 解析日期范围参数 (YYYY-MM-DD,YYYY-MM-DD)
     * 
     * @param value 日期范围字符串
     * @return 包含开始和结束日期的数组，如果解析失败返回null
     */
    public String[] parseDateRange(String value) {
        if (value == null || !DATE_RANGE_PATTERN.matcher(value).matches()) {
            return null;
        }

        String[] parts = value.split(",");
        String startDate = parts[0].trim();
        String endDate = parts[1].trim();

        // 简单的日期格式验证
        if (!isValidDateFormat(startDate) || !isValidDateFormat(endDate)) {
            logger.warn("日期格式无效: {}", value);
            return null;
        }

        return new String[] { startDate, endDate };
    }

    /**
     * 验证邮箱域名列表
     * 
     * @param domains 域名列表
     * @return 验证结果
     */
    public ValidationResult validateEmailDomains(List<String> domains) {
        ValidationResult result = new ValidationResult();

        if (domains == null || domains.isEmpty()) {
            result.addError("邮箱域名列表不能为空");
            return result;
        }

        for (String domain : domains) {
            if (!isValidDomain(domain)) {
                result.addError("无效的邮箱域名: " + domain);
            }
        }

        return result;
    }

    /**
     * 验证手机号前缀列表
     * 
     * @param prefixes 前缀列表
     * @return 验证结果
     */
    public ValidationResult validatePhonePrefixes(List<String> prefixes) {
        ValidationResult result = new ValidationResult();

        if (prefixes == null || prefixes.isEmpty()) {
            result.addError("手机号前缀列表不能为空");
            return result;
        }

        for (String prefix : prefixes) {
            if (!PHONE_PREFIX_PATTERN.matcher(prefix).matches()) {
                result.addError("无效的手机号前缀: " + prefix + " (必须是3位数字)");
            }
        }

        return result;
    }

    /**
     * 验证地区代码
     * 
     * @param regionCode 地区代码
     * @return 验证结果
     */
    public ValidationResult validateRegionCode(String regionCode) {
        ValidationResult result = new ValidationResult();

        if (regionCode == null || regionCode.trim().isEmpty()) {
            result.addError("地区代码不能为空");
            return result;
        }

        if (!REGION_CODE_PATTERN.matcher(regionCode).matches()) {
            result.addError("无效的地区代码: " + regionCode + " (必须是6位数字)");
        }

        return result;
    }

    /**
     * 验证生成配置的参数
     * 
     * @param config 生成配置
     * @return 验证结果
     */
    public ValidationResult validateGenerationConfig(GenerationConfig config) {
        ValidationResult result = new ValidationResult();

        if (config == null) {
            result.addError("生成配置不能为空");
            return result;
        }

        // 验证基本配置
        if (!config.isMultiFieldMode()) {
            if (config.getDataType() == null || config.getDataType().trim().isEmpty()) {
                result.addError("数据类型不能为空");
            }
        }

        if (config.getCount() <= 0) {
            result.addError("生成数量必须大于0");
        }

        // 验证数据类型特定参数
        result.merge(validateDataTypeParameters(config));

        // 验证输出配置
        result.merge(config.getOutputConfig().validate());

        // 验证校验配置
        result.merge(config.getValidationConfig().validate());

        // 验证性能配置
        result.merge(config.getPerformanceConfig().validate());

        return result;
    }

    /**
     * 验证数据类型特定参数
     * 
     * @param config 生成配置
     * @return 验证结果
     */
    private ValidationResult validateDataTypeParameters(GenerationConfig config) {
        ValidationResult result = new ValidationResult();
        String dataType = config.getDataType();
        Map<String, Object> parameters = config.getParameters();

        if (dataType == null || parameters == null) {
            return result;
        }

        switch (dataType.toLowerCase()) {
            case "name":
                result.merge(validateNameParameters(parameters));
                break;
            case "phone":
                result.merge(validatePhoneParameters(parameters));
                break;
            case "email":
                result.merge(validateEmailParameters(parameters));
                break;
            case "idcard":
                result.merge(validateIdCardParameters(parameters));
                break;
            case "bankcard":
                result.merge(validateBankCardParameters(parameters));
                break;
            case "age":
                result.merge(validateAgeParameters(parameters));
                break;
            default:
                // 未知数据类型，跳过特定验证
                break;
        }

        return result;
    }

    /**
     * 验证姓名参数
     */
    private ValidationResult validateNameParameters(Map<String, Object> parameters) {
        ValidationResult result = new ValidationResult();

        Object nameType = parameters.get("name.type");
        if (nameType != null) {
            String type = nameType.toString().toUpperCase();
            if (!Arrays.asList("CN", "EN", "BOTH").contains(type)) {
                result.addError("无效的姓名类型: " + nameType + " (支持: CN, EN, BOTH)");
            }
        }

        Object gender = parameters.get("name.gender");
        if (gender != null) {
            String genderStr = gender.toString().toUpperCase();
            if (!Arrays.asList("MALE", "FEMALE", "ANY").contains(genderStr)) {
                result.addError("无效的性别: " + gender + " (支持: MALE, FEMALE, ANY)");
            }
        }

        return result;
    }

    /**
     * 验证手机号参数
     */
    private ValidationResult validatePhoneParameters(Map<String, Object> parameters) {
        ValidationResult result = new ValidationResult();

        Object prefix = parameters.get("phone.prefix");
        if (prefix != null) {
            List<String> prefixes = parseStringList(prefix.toString());
            result.merge(validatePhonePrefixes(prefixes));
        }

        return result;
    }

    /**
     * 验证邮箱参数
     */
    private ValidationResult validateEmailParameters(Map<String, Object> parameters) {
        ValidationResult result = new ValidationResult();

        Object domains = parameters.get("email.domains");
        if (domains != null) {
            List<String> domainList = parseStringList(domains.toString());
            result.merge(validateEmailDomains(domainList));
        }

        Object usernameLength = parameters.get("email.username-length");
        if (usernameLength != null) {
            int[] range = parseIntRange(usernameLength.toString());
            if (range == null) {
                result.addError("无效的用户名长度范围: " + usernameLength);
            } else if (range[0] < 1 || range[1] > 64) {
                result.addError("用户名长度范围超出限制 (1-64): " + usernameLength);
            }
        }

        return result;
    }

    /**
     * 验证身份证参数
     */
    private ValidationResult validateIdCardParameters(Map<String, Object> parameters) {
        ValidationResult result = new ValidationResult();

        Object region = parameters.get("idcard.region");
        if (region != null) {
            result.merge(validateRegionCode(region.toString()));
        }

        Object birthDateRange = parameters.get("idcard.birth-date-range");
        if (birthDateRange != null) {
            String[] range = parseDateRange(birthDateRange.toString());
            if (range == null) {
                result.addError("无效的出生日期范围: " + birthDateRange);
            }
        }

        return result;
    }

    /**
     * 验证银行卡参数
     */
    private ValidationResult validateBankCardParameters(Map<String, Object> parameters) {
        ValidationResult result = new ValidationResult();

        Object cardType = parameters.get("bankcard.type");
        if (cardType != null) {
            String type = cardType.toString().toUpperCase();
            if (!Arrays.asList("DEBIT", "CREDIT", "BOTH").contains(type)) {
                result.addError("无效的银行卡类型: " + cardType + " (支持: DEBIT, CREDIT, BOTH)");
            }
        }

        Object issuer = parameters.get("bankcard.issuer");
        if (issuer != null) {
            String issuerStr = issuer.toString().toUpperCase();
            if (!Arrays.asList("VISA", "MC", "UNIONPAY", "JCB", "ANY").contains(issuerStr)) {
                result.addError("无效的卡组织: " + issuer + " (支持: VISA, MC, UNIONPAY, JCB, ANY)");
            }
        }

        return result;
    }

    /**
     * 验证年龄参数
     */
    private ValidationResult validateAgeParameters(Map<String, Object> parameters) {
        ValidationResult result = new ValidationResult();

        Object minAge = parameters.get("age.min");
        Object maxAge = parameters.get("age.max");

        if (minAge != null && maxAge != null) {
            try {
                int min = Integer.parseInt(minAge.toString());
                int max = Integer.parseInt(maxAge.toString());

                if (min < 0 || max < 0) {
                    result.addError("年龄不能为负数");
                } else if (min > max) {
                    result.addError("最小年龄不能大于最大年龄");
                } else if (max > 150) {
                    result.addWarning("最大年龄过大，可能不现实: " + max);
                }
            } catch (NumberFormatException e) {
                result.addError("年龄参数必须是数字");
            }
        }

        return result;
    }

    /**
     * 验证日期格式
     */
    private boolean isValidDateFormat(String date) {
        if (date == null || date.length() != 10) {
            return false;
        }

        try {
            String[] parts = date.split("-");
            if (parts.length != 3) {
                return false;
            }

            int year = Integer.parseInt(parts[0]);
            int month = Integer.parseInt(parts[1]);
            int day = Integer.parseInt(parts[2]);

            return year >= 1900 && year <= 2100 &&
                    month >= 1 && month <= 12 &&
                    day >= 1 && day <= 31;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    /**
     * 验证域名格式
     */
    private boolean isValidDomain(String domain) {
        if (domain == null || domain.trim().isEmpty()) {
            return false;
        }

        // 简单的域名格式验证
        return domain.matches("^[a-zA-Z0-9][a-zA-Z0-9-]*[a-zA-Z0-9]*\\.[a-zA-Z]{2,}$");
    }
}