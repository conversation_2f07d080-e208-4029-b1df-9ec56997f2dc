### DataForge 项目全面开发规则与指南

作为“Arch-Java”，我将为您详细阐述 `DataForge` 项目的各项开发规则与指南。这些规则旨在确保项目在 **分层单体架构** 下，能够高效、高质量地推进，并最终产出健壮、可维护且安全的测试数据生成工具。

#### 一、总体原则 (General Principles)

1. **目标导向：** 所有设计与实现必须紧密围绕 `DataForge` 的核心目标——成为高效、灵活、可配置的测试数据生成工具，尤其关注中国特定数据类型的真实性。
    
2. **质量优先：** 代码质量、测试覆盖率、文档清晰度是不可妥协的基石。
    
3. **可维护性与可扩展性：** 即使是单体架构，也必须通过严格的模块化、接口隔离和设计模式，确保代码易于理解、修改和功能扩展。
    
4. **性能与效率：** 数据生成是核心，必须关注算法效率和资源利用，避免不必要的开销。
    
5. **安全性：** 从设计之初就融入安全考量，防范潜在漏洞，保护敏感数据。
    
6. **团队协作：** 遵循统一的规范，确保代码风格一致，提高团队协作效率。
    

#### 二、架构与设计规范 (Architecture & Design Guidelines)

1. **分层架构严格遵循：**
    
    - **表现层/CLI层 (`dataforge-app`):** 负责用户交互（命令行输入、HTTP请求解析），将请求转换为核心服务可理解的命令，并处理结果的展示。**严禁**在此层包含核心业务逻辑。
        
    - **服务层 (`dataforge-core` / 各模块的 `service` 层):** 封装核心业务逻辑，协调领域对象和基础设施服务。服务之间通过接口而非实现进行依赖。
        
    - **领域/核心模型层 (`dataforge-core/model`):** 定义核心业务实体、值对象和聚合根，例如 `GenerationRequest`, `GenerationResult`, `GeneratorConfig`。保持其纯粹性，不依赖基础设施。
        
    - **基础设施/数据访问层 (`dataforge-output`, `dataforge-config` 的持久化部分):** 处理与外部系统（文件系统、数据库、网络）的交互。
        
    - **工具/通用模块 (`dataforge-util`, `dataforge-common`):** 存放通用工具类、常量、异常定义等。
        
2. **模块化设计：**
    
    - 每个Maven模块（如 `dataforge-generators`, `dataforge-config`, `dataforge-output`）应有清晰的职责边界，模块间通过明确的依赖关系进行通信。
        
    - 避免循环依赖。
        
    - `dataforge-core` 模块应仅包含核心接口、通用模型和异常，不应包含具体实现。
        
3. **接口优先原则：**
    
    - 模块内部或模块间通信应优先定义接口，而非直接依赖具体实现类。这有助于降低耦合，方便测试和未来替换。
        
4. **设计模式应用：**
    
    - **策略模式：** 强制所有数据类型生成器实现 `DataTypeGenerator` 接口，并通过 `DefaultDataGenerationService` 进行动态发现和调用。
        
    - **工厂模式：** 可考虑为复杂对象的创建（如特定配置对象）提供工厂类。
        
    - **建造者模式：** 对于具有大量可选参数的复杂配置对象，推荐使用建造者模式。
        
    - **单例模式：** 谨慎使用，仅限于无状态的工具类或明确需要全局唯一实例的场景。
        
5. **SOLID 原则：**
    
    - **单一职责原则 (SRP):** 每个类或方法只负责一项功能。
        
    - **开放/封闭原则 (OCP):** 对扩展开放，对修改封闭。通过接口和策略模式实现。
        
    - **里氏替换原则 (LSP):** 子类可以替换父类而不影响程序的正确性。
        
    - **接口隔离原则 (ISP):** 客户端不应被迫依赖它们不使用的接口。
        
    - **依赖倒置原则 (DIP):** 高层模块不应依赖低层模块，两者都应依赖抽象。抽象不应依赖细节，细节应依赖抽象。
        
6. **API 设计：**
    
    - 内部 API 保持一致性，参数和返回类型清晰。
        
    - 如果暴露 RESTful API，应遵循 RESTful 原则，使用标准 HTTP 方法和状态码，资源路径清晰。
        

#### 三、代码风格与质量 (Code Style & Quality)

1. **Google Java Style Guide：**
    
    - **强制遵循** Google Java Style Guide。所有代码提交前必须通过格式化工具（如Google Java Format Maven Plugin）检查。
        
    - IDE（IntelliJ IDEA, Eclipse）应配置相应的代码风格。
        
2. **命名规范：**
    
    - **包名：** 小写，点分隔（`com.dataforge.core.generator`）。
        
    - **类名/接口名：** 大驼峰命名法（`MyClass`, `MyInterface`）。
        
    - **方法名/变量名：** 小驼峰命名法（`myMethod`, `myVariable`）。
        
    - **常量：** 全大写，下划线分隔（`MY_CONSTANT`）。
        
    - **枚举值：** 全大写，下划线分隔（`MALE`, `FEMALE`）。
        
    - 命名应具描述性，避免缩写，除非是业界公认的（如 `ID`, `URL`）。
        
3. **代码注释 (JSDoc 风格)：**
    
    - **强制** 为所有公共类、接口、方法、枚举和复杂字段编写 JSDoc 风格的注释。
        
    - 注释应解释 **为什么** 这样做，而不是 **做什么**（代码本身应清晰地表达做什么）。
        
    - 对于复杂算法或业务逻辑，应提供详细的解释。
        
    - 示例：
        
        ```
        /**
         * 这是一个示例类，用于演示JSDoc注释规范。
         * 它负责处理用户相关的业务逻辑。
         *
         * <AUTHOR>
         * @version 1.0
         * @since 2025-07-23
         */
        public class UserService {
        
            /**
             * 根据用户ID获取用户信息。
             *
             * <p>此方法会首先从缓存中查找用户，如果未命中则从数据库中加载。</p>
             *
             * @param userId 用户的唯一标识符，必须为正整数。
             * @return 对应的用户信息对象，如果用户不存在则返回 {@code Optional.empty()}。
             * @throws IllegalArgumentException 如果 {@code userId} 为负数或零。
             * @throws DataAccessException 如果数据库操作失败。
             */
            public Optional<User> getUserById(long userId) {
                // ... implementation
            }
        }
        ```
        
4. **代码可读性：**
    
    - 保持方法和类的长度适中，避免“巨石”方法和类。
        
    - 合理使用空行和缩进，增强代码结构感。
        
    - 避免魔法数字和字符串，使用常量或枚举。
        
5. **避免代码重复 (DRY - Don't Repeat Yourself)：**
    
    - 识别并抽象通用逻辑，提取为公共方法、工具类或通用组件。
        
6. **错误处理：**
    
    - 使用自定义异常 (`DataGenerationException`) 封装业务逻辑错误。
        
    - 对外部依赖（文件I/O、网络请求）的异常进行捕获和适当处理，避免异常泄露。
        
    - 在服务层进行异常转换，将底层技术异常转换为业务异常。
        
7. **日志规范：**
    
    - 使用 SLF4J 接口和 Logback 实现。
        
    - 定义统一的日志级别使用规范（`DEBUG`, `INFO`, `WARN`, `ERROR`）。
        
    - 日志信息应清晰、有意义，包含必要的上下文信息，但**严禁**在日志中输出敏感数据。
        

#### 四、测试策略 (Testing Strategy)

1. **单元测试 (Unit Tests)：**
    
    - **强制要求：** 所有核心业务逻辑类和方法必须编写单元测试。
        
    - **框架：** JUnit 5 和 Mockito。
        
    - **目标：** 隔离测试单个组件的功能，不依赖外部系统。
        
    - **覆盖率：** 目标代码覆盖率应达到 **80% 以上**。通过JaCoCo等工具进行监控。
        
    - **测试数据：** 单元测试数据应在测试类内部定义，或使用简单的测试数据生成器。
        
2. **集成测试 (Integration Tests)：**
    
    - **目标：** 验证模块之间、服务与基础设施（如文件系统、数据库）之间的协作是否正确。
        
    - **框架：** Spring Boot Test。
        
    - **场景：** 例如，测试 `DataGenerationService` 调用 `DataTypeGenerator` 的完整流程，测试数据输出到文件或数据库。
        
    - **测试数据：** 可以利用 `DataForge` 自身生成小批量、特定场景的测试数据。
        
3. **端到端测试 (End-to-End Tests)：**
    
    - **目标：** 模拟真实用户场景，验证整个系统（CLI 或 API）从输入到输出的完整流程。
        
    - **场景：** 例如，通过CLI命令生成复杂数据并验证输出文件内容。
        
4. **测试数据管理：**
    
    - 鼓励使用 `DataForge` 自身来生成集成测试和端到端测试所需的复杂、多样化数据。
        
5. **持续测试：**
    
    - 测试应作为 CI/CD 流水线的一部分，每次代码提交都触发自动化测试。
        

#### 五、安全指南 (Security Guidelines)

1. **输入校验与净化：**
    
    - **所有** 来自外部（CLI参数、API请求体、配置文件）的输入都必须进行严格的 **白名单校验** 和 **净化**。
        
    - **数量限制：** 对生成数量 (`count`) 等参数设置硬性上限，防止资源耗尽攻击 (DoS)。
        
    - **格式校验：** 使用正则表达式、枚举等方式严格校验输入格式。
        
    - **路径安全：** 凡涉及文件路径的输入，必须严格校验，防止路径遍历 (`../`) 攻击。
        
    - **命令注入防御：** 避免直接拼接用户输入到系统命令中，使用参数化执行或安全API。
        
    - **XSS/SQL注入防御：** 尽管 `DataForge` 生成的是测试数据，但其内部处理逻辑必须对潜在的注入攻击保持警惕。如果 `DataForge` 自身暴露 Web 接口，则必须严格防范。
        
2. **敏感数据处理：**
    
    - **日志脱敏：** 严禁在日志中输出身份证号、银行卡号、密码等敏感信息。使用脱敏处理或配置日志级别。
        
    - **内存清理：** 对于敏感数据（如密码明文），在内存中使用后应尽快清零或设置为 `null`。
        
    - **存储安全：** 如果配置文件包含敏感信息（如数据库连接凭证），应加密存储或使用外部密钥管理服务。
        
    - **临时文件：** 生成的临时文件应具有严格的权限控制，并在使用后立即删除。
        
3. **依赖安全：**
    
    - 定期使用 **OWASP Dependency-Check** 或类似工具扫描项目依赖，及时发现并修复已知的安全漏洞 (CVE)。
        
    - 优先选择活跃维护、社区支持良好且无已知严重漏洞的第三方库。
        
4. **权限控制 (Access Control)：**
    
    - 如果 `DataForge` 暴露为 RESTful API，必须集成 **Spring Security**，实现基于角色的访问控制（RBAC）。
        
    - 区分不同用户角色的权限，例如：普通用户只能生成数据，管理员才能管理配置模板。
        
5. **异常处理：**
    
    - 避免在异常信息中泄露敏感系统信息（如堆栈跟踪、数据库错误信息）。
        
    - 提供统一、友好的错误提示信息。
        

#### 六、文档标准 (Documentation Standards)

1. **项目 README.md：**
    
    - 项目简介、核心目标、技术栈。
        
    - 快速启动指南（如何构建、运行CLI/API）。
        
    - 主要功能列表。
        
    - 贡献指南。
        
2. **设计文档：**
    
    - 针对每个核心模块或复杂功能，编写详细的设计文档，包括但不限于：模块职责、类图、时序图（Mermaid.js）、API 设计、数据结构、关键算法说明。
        
3. **代码注释：**
    
    - 遵循上述“代码注释 (JSDoc 风格)”规范。
        
4. **CLI 使用文档：**
    
    - 提供详细的CLI命令、参数说明和使用示例。
        
5. **API 文档：**
    
    - 如果暴露 RESTful API，使用 **SpringDoc OpenAPI** (Swagger UI) 自动生成并维护 API 文档。
        

#### 七、版本控制与协作 (Version Control & Collaboration)

1. **Git Flow 或 GitHub Flow：**
    
    - 推荐使用 Git Flow 或 GitHub Flow 作为分支管理模型，确保开发、发布、维护流程清晰。
        
    - **`main` / `master` 分支：** 始终保持可发布状态。
        
    - **`develop` 分支：** 集成所有新功能开发。
        
    - **`feature` 分支：** 每个新功能或任务从 `develop` 分支拉取，开发完成后合并回 `develop`。
        
    - **`release` 分支：** 用于发布准备。
        
    - **`hotfix` 分支：** 用于紧急 bug 修复。
        
2. **提交信息 (Commit Messages)：**
    
    - 清晰、简洁、描述性强。遵循 Conventional Commits 规范（可选，但推荐）。
        
    - 格式：`<type>(<scope>): <subject>`
        
        - `type`: feat (新功能), fix (bug修复), docs (文档), style (代码风格), refactor (重构), test (测试), chore (构建/工具)。
            
        - `scope`: 影响的模块或功能（如 `core`, `name-generator`, `cli`）。
            
        - `subject`: 简短的描述，使用祈使句。
            
    - 示例：`feat(idcard-generator): add region code support`
        
3. **代码审查 (Code Reviews)：**
    
    - 所有合并到 `develop` 或 `main` 分支的代码都必须经过至少一名团队成员的代码审查。
        
    - 关注点：代码质量、风格、设计、测试、潜在 bug 和安全问题。
        

#### 八、依赖管理 (Dependency Management)

1. **Maven：**
    
    - 使用 Maven 作为项目构建和依赖管理工具。
        
    - 在 `dataforge-parent/pom.xml` 中集中管理所有模块的公共依赖版本。
        
    - 避免在子模块中重复定义版本号。
        
2. **最小化依赖：**
    
    - 只引入项目实际需要的依赖，避免引入不必要的库，以减少项目体积和潜在的安全风险。
        
3. **依赖更新：**
    
    - 定期审查和更新项目依赖，及时获取新功能、性能改进和安全补丁。
        

#### 九、错误处理与日志 (Error Handling & Logging)

1. **统一异常处理：**
    
    - 在 `dataforge-core/exception` 包中定义自定义业务异常，如 `DataGenerationException`。
        
    - 在 `dataforge-app` 层（CLI命令处理或 RESTful Controller Advice）实现统一的异常捕获和处理，向用户提供友好的错误提示，避免泄露内部实现细节。
        
2. **日志级别：**
    
    - `ERROR`: 记录导致应用无法正常运行的严重错误。
        
    - `WARN`: 记录可能导致问题但应用仍能继续运行的警告。
        
    - `INFO`: 记录应用的关键业务流程信息，如请求处理、数据生成完成。
        
    - `DEBUG`: 记录开发和调试阶段的详细信息。
        
    - `TRACE`: 最详细的日志，用于问题追踪。
        
3. **日志内容：**
    
    - 日志信息应包含时间戳、日志级别、类名/方法名、线程信息。
        
    - 对于异常日志，必须包含完整的堆栈跟踪。
        
    - **再次强调：严禁在日志中输出敏感用户数据或系统配置信息。**
        

#### 十、性能考量 (Performance Considerations)

1. **算法效率：**
    
    - 数据生成是核心，优先选择时间复杂度低的算法。
        
    - 例如，对于需要大量查找的数据（如行政区划代码），考虑使用哈希表或预加载到内存中。
        
2. **资源管理：**
    
    - 对于大批量数据生成，考虑流式处理，避免一次性将所有数据加载到内存中导致 OOM。
        
    - 合理利用 Java NIO 进行文件读写。
        
3. **并发处理：**
    
    - 对于 CPU 密集型的数据生成任务，可以考虑在 `DefaultDataGenerationService` 内部使用 `ExecutorService` 进行有限的并行处理，但需确保线程安全。
        
    - 避免过度创建线程，导致上下文切换开销过大。
        
4. **配置优化：**
    
    - JVM 参数调优（如堆内存大小）。
        
    - Spring Boot 配置优化（如禁用不必要的自动配置）。
        

遵循这些详细的规则和指南，我们将能够构建一个高质量、高性能且易于维护的 `DataForge` 系统。请仔细审阅，如有任何疑问或需要调整之处，请随时提出。