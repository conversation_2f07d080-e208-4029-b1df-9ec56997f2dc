package com.dataforge.generators.identifier;

import com.dataforge.core.generator.AbstractDataGenerator;
import com.dataforge.core.generator.GeneratorParameter;
import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.model.ValidationResult;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.Random;
import java.util.regex.Pattern;

/**
 * 身份证号生成器
 * 
 * 生成符合GB 11643-1999标准的18位身份证号码。
 * 支持指定地区、出生日期范围和性别，确保生成的身份证号格式正确且校验位有效。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class IdCardNumberGenerator extends AbstractDataGenerator<String> {

    private static final String TYPE = "idcard";
    private static final String DESCRIPTION = "生成18位身份证号码";

    // 身份证号验证正则表达式
    private static final Pattern ID_CARD_PATTERN = Pattern
            .compile("^[1-9]\\d{5}(19|20)\\d{2}(0[1-9]|1[0-2])(0[1-9]|[12]\\d|3[01])\\d{3}[0-9Xx]$");

    // 校验位计算权重
    private static final int[] WEIGHTS = { 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2 };

    // 校验位对应表
    private static final char[] CHECK_CODES = { '1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2' };

    // 常见地区代码（前6位）
    private static final List<String> COMMON_AREA_CODES = Arrays.asList(
            "110101", // 北京市东城区
            "110102", // 北京市西城区
            "110105", // 北京市朝阳区
            "110106", // 北京市丰台区
            "110107", // 北京市石景山区
            "110108", // 北京市海淀区
            "110109", // 北京市门头沟区
            "310101", // 上海市黄浦区
            "310104", // 上海市徐汇区
            "310105", // 上海市长宁区
            "310106", // 上海市静安区
            "310107", // 上海市普陀区
            "310109", // 上海市虹口区
            "310110", // 上海市杨浦区
            "440103", // 广东省广州市荔湾区
            "440104", // 广东省广州市越秀区
            "440105", // 广东省广州市海珠区
            "440106", // 广东省广州市天河区
            "440111", // 广东省广州市白云区
            "440112", // 广东省广州市黄埔区
            "440113", // 广东省广州市番禺区
            "440114", // 广东省广州市花都区
            "440115", // 广东省广州市南沙区
            "440117", // 广东省广州市从化区
            "440118", // 广东省广州市增城区
            "320102", // 江苏省南京市玄武区
            "320104", // 江苏省南京市秦淮区
            "320105", // 江苏省南京市建邺区
            "320106", // 江苏省南京市鼓楼区
            "320111", // 江苏省南京市浦口区
            "320113", // 江苏省南京市栖霞区
            "320114", // 江苏省南京市雨花台区
            "320115", // 江苏省南京市江宁区
            "320116", // 江苏省南京市六合区
            "320117" // 江苏省南京市溧水区
    );

    private final Random random;

    /**
     * 构造函数
     */
    public IdCardNumberGenerator() {
        this.random = new Random();
    }

    @Override
    protected void initializeParameters() {
        addParameter(new GeneratorParameter("areaCode", String.class, "random",
                "地区代码（前6位），如110101，或使用random随机选择", false));
        addParameter(new GeneratorParameter("minAge", Integer.class, 18,
                "最小年龄，用于计算出生日期范围", false));
        addParameter(new GeneratorParameter("maxAge", Integer.class, 65,
                "最大年龄，用于计算出生日期范围", false));
        addParameter(new GeneratorParameter("gender", String.class, "random",
                "性别：male（男性）、female（女性）、random（随机）", false));
        addParameter(new GeneratorParameter("birthYear", Integer.class, null,
                "指定出生年份，设置后忽略年龄参数", false));
    }

    @Override
    protected String doGenerate(GenerationContext context) {
        // 获取参数
        String areaCode = context.getParameter("areaCode", "random");
        Integer minAge = context.getParameter("minAge", 18);
        Integer maxAge = context.getParameter("maxAge", 65);
        String gender = context.getParameter("gender", "random");
        Integer birthYear = context.getParameter("birthYear", null);

        // 参数验证
        if (minAge >= maxAge) {
            throw new IllegalArgumentException("最小年龄必须小于最大年龄");
        }

        // 生成地区代码
        String selectedAreaCode = selectAreaCode(areaCode);

        // 生成出生日期
        String birthDate = generateBirthDate(birthYear, minAge, maxAge);

        // 生成顺序码（第15-17位）
        String sequenceCode = generateSequenceCode(gender);

        // 组合前17位
        String first17Digits = selectedAreaCode + birthDate + sequenceCode;

        // 计算校验位
        char checkCode = calculateCheckCode(first17Digits);

        String idCard = first17Digits + checkCode;

        // 将生成的身份证号设置到关联管理器中，触发关联字段的自动生成
        context.getRelationManager().setRelatedValue("idcard", idCard);

        return idCard;
    }

    /**
     * 选择地区代码
     * 
     * @param areaCode 指定的地区代码或"random"
     * @return 选择的地区代码
     */
    private String selectAreaCode(String areaCode) {
        if ("random".equals(areaCode) || areaCode == null) {
            return COMMON_AREA_CODES.get(random.nextInt(COMMON_AREA_CODES.size()));
        }

        // 验证地区代码格式
        if (!areaCode.matches("\\d{6}")) {
            throw new IllegalArgumentException("地区代码必须是6位数字");
        }

        return areaCode;
    }

    /**
     * 生成出生日期（YYYYMMDD格式）
     * 
     * @param birthYear 指定的出生年份
     * @param minAge    最小年龄
     * @param maxAge    最大年龄
     * @return 出生日期字符串
     */
    private String generateBirthDate(Integer birthYear, int minAge, int maxAge) {
        LocalDate now = LocalDate.now();
        LocalDate birthDate;

        if (birthYear != null) {
            // 使用指定的出生年份
            int month = 1 + random.nextInt(12);
            int day = 1 + random.nextInt(28); // 简化处理，避免月份天数问题
            birthDate = LocalDate.of(birthYear, month, day);
        } else {
            // 根据年龄范围计算出生日期
            LocalDate maxBirthDate = now.minusYears(minAge);
            LocalDate minBirthDate = now.minusYears(maxAge + 1);

            long daysBetween = minBirthDate.toEpochDay() - maxBirthDate.toEpochDay();
            long randomDays = (long) (random.nextDouble() * daysBetween);

            birthDate = maxBirthDate.plusDays(randomDays);
        }

        return birthDate.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
    }

    /**
     * 生成顺序码（第15-17位）
     * 第17位为性别标识：奇数为男性，偶数为女性
     * 
     * @param gender 性别
     * @return 3位顺序码
     */
    private String generateSequenceCode(String gender) {
        // 前两位随机生成
        int first = random.nextInt(10);
        int second = random.nextInt(10);

        // 第三位根据性别确定奇偶性
        int third;
        if ("male".equals(gender)) {
            // 男性：奇数
            third = 1 + random.nextInt(5) * 2; // 1, 3, 5, 7, 9
        } else if ("female".equals(gender)) {
            // 女性：偶数
            third = random.nextInt(5) * 2; // 0, 2, 4, 6, 8
        } else {
            // 随机
            third = random.nextInt(10);
        }

        return String.format("%d%d%d", first, second, third);
    }

    /**
     * 计算校验位
     * 根据GB 11643-1999标准计算第18位校验码
     * 
     * @param first17Digits 前17位数字
     * @return 校验位字符
     */
    private char calculateCheckCode(String first17Digits) {
        int sum = 0;

        for (int i = 0; i < 17; i++) {
            int digit = Character.getNumericValue(first17Digits.charAt(i));
            sum += digit * WEIGHTS[i];
        }

        int remainder = sum % 11;
        return CHECK_CODES[remainder];
    }

    @Override
    public ValidationResult validateWithDetails(String data) {
        if (data == null) {
            return ValidationResult.error("身份证号不能为空");
        }

        String trimmed = data.trim().toUpperCase();
        if (trimmed.isEmpty()) {
            return ValidationResult.error("身份证号不能为空");
        }

        // 长度检查
        if (trimmed.length() != 18) {
            return ValidationResult.error("身份证号必须是18位");
        }

        // 格式检查
        if (!ID_CARD_PATTERN.matcher(trimmed).matches()) {
            return ValidationResult.error("身份证号格式不正确");
        }

        // 校验位检查
        String first17 = trimmed.substring(0, 17);
        char expectedCheckCode = calculateCheckCode(first17);
        char actualCheckCode = trimmed.charAt(17);

        if (expectedCheckCode != actualCheckCode) {
            return ValidationResult.error("身份证号校验位不正确");
        }

        // 出生日期有效性检查
        try {
            String birthDateStr = trimmed.substring(6, 14);
            int year = Integer.parseInt(birthDateStr.substring(0, 4));
            int month = Integer.parseInt(birthDateStr.substring(4, 6));
            int day = Integer.parseInt(birthDateStr.substring(6, 8));

            LocalDate birthDate = LocalDate.of(year, month, day);
            LocalDate now = LocalDate.now();

            if (birthDate.isAfter(now)) {
                return ValidationResult.error("出生日期不能晚于当前日期");
            }

            if (birthDate.isBefore(LocalDate.of(1900, 1, 1))) {
                return ValidationResult.error("出生日期不能早于1900年");
            }

        } catch (Exception e) {
            return ValidationResult.error("出生日期无效");
        }

        return ValidationResult.success();
    }

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    public String getDescription() {
        return DESCRIPTION;
    }
}