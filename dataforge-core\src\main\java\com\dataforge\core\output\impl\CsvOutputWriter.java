package com.dataforge.core.output.impl;

import com.dataforge.core.output.AbstractOutputWriter;
import com.dataforge.core.output.OutputConfig;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;

/**
 * CSV输出器
 * 
 * 将数据以CSV格式输出到文件或标准输出，支持自定义分隔符、引号和转义处理。
 * 实现流式写入以支持大文件输出。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class CsvOutputWriter extends AbstractOutputWriter {

    private static final String TYPE = "csv";

    private Writer writer;
    private List<String> headers;

    // CSV格式配置
    private String delimiter;
    private String quote;
    private String escape;
    private String lineEnding;
    private boolean alwaysQuote;

    @Override
    public String getType() {
        return TYPE;
    }

    @Override
    protected void doInitialize(OutputConfig config) throws Exception {
        // 获取CSV格式配置
        this.delimiter = config.getFormatOption("delimiter", ",");
        this.quote = config.getFormatOption("quote", "\"");
        this.escape = config.getFormatOption("escape", "\"");
        this.lineEnding = config.getFormatOption("lineEnding", System.lineSeparator());
        this.alwaysQuote = config.getFormatOption("alwaysQuote", false);

        // 初始化输出流
        initializeWriter(config);
    }

    /**
     * 初始化输出流
     * 
     * @param config 输出配置
     * @throws IOException 当初始化失败时抛出
     */
    private void initializeWriter(OutputConfig config) throws IOException {
        if (config.isFileOutput()) {
            // 文件输出
            String encoding = config.getEncoding();
            if (StandardCharsets.UTF_8.name().equals(encoding)) {
                this.writer = new BufferedWriter(new FileWriter(config.getTarget(), StandardCharsets.UTF_8));
            } else {
                this.writer = new BufferedWriter(new FileWriter(config.getTarget()));
            }
        } else {
            // 控制台输出
            this.writer = new BufferedWriter(new OutputStreamWriter(System.out, StandardCharsets.UTF_8));
        }
    }

    @Override
    protected void doWriteHeader(List<String> headers) throws Exception {
        this.headers = headers;

        if (config.isIncludeHeader() && headers != null && !headers.isEmpty()) {
            writeStringRow(headers);
        }
    }

    @Override
    protected void doWriteRecord(Map<String, Object> record) throws Exception {
        if (headers == null || headers.isEmpty()) {
            throw new IllegalStateException("必须先调用writeHeader设置表头");
        }

        StringBuilder row = new StringBuilder();

        for (int i = 0; i < headers.size(); i++) {
            if (i > 0) {
                row.append(delimiter);
            }

            String header = headers.get(i);
            Object value = record.get(header);
            String cellValue = value != null ? value.toString() : "";

            // 格式化单元格值
            String formattedValue = formatCellValue(cellValue);
            row.append(formattedValue);
        }

        row.append(lineEnding);
        writer.write(row.toString());
    }

    /**
     * 写入字符串列表作为一行（用于表头）
     * 
     * @param values 值列表
     * @throws IOException 当写入失败时抛出
     */
    private void writeStringRow(List<String> values) throws IOException {
        StringBuilder row = new StringBuilder();

        for (int i = 0; i < values.size(); i++) {
            if (i > 0) {
                row.append(delimiter);
            }

            String value = values.get(i);
            String formattedValue = formatCellValue(value != null ? value : "");
            row.append(formattedValue);
        }

        row.append(lineEnding);
        writer.write(row.toString());
    }

    /**
     * 格式化单元格值
     * 处理引号、转义和特殊字符
     * 
     * @param value 原始值
     * @return 格式化后的值
     */
    private String formatCellValue(String value) {
        if (value == null) {
            return "";
        }

        // 检查是否需要引号
        boolean needsQuoting = alwaysQuote ||
                value.contains(delimiter) ||
                value.contains(quote) ||
                value.contains("\n") ||
                value.contains("\r");

        if (needsQuoting) {
            // 转义引号字符
            String escapedValue = value.replace(quote, escape + quote);
            return quote + escapedValue + quote;
        } else {
            return value;
        }
    }

    @Override
    protected void doFlush() throws Exception {
        if (writer != null) {
            writer.flush();
        }
    }

    @Override
    protected void doClose() throws Exception {
        if (writer != null) {
            writer.close();
        }
    }
}