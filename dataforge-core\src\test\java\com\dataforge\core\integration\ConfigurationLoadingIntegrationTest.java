package com.dataforge.core.integration;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.FieldConfig;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.service.ConfigurationManager;
import com.dataforge.core.service.DataForgeService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.io.TempDir;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.List;
import java.util.Map;

import static org.assertj.core.api.Assertions.*;

/**
 * 配置文件加载集成测试
 */
@DisplayName("配置文件加载集成测试")
class ConfigurationLoadingIntegrationTest {

    @TempDir
    Path tempDir;

    private ConfigurationManager configurationManager;
    private DataForgeService dataForgeService;

    @BeforeEach
    void setUp() {
        configurationManager = new ConfigurationManager();
        dataForgeService = new DataForgeService();
    }

    @Test
    @DisplayName("YAML配置文件加载集成测试")
    void testYamlConfigurationLoadingIntegration() throws IOException {
        // 创建YAML配置文件
        String yamlContent = """
                count: 100
                seed: 12345
                fields:
                  - name: "user_name"
                    type: "name"
                    parameters:
                      nameType: "CHINESE"
                  - name: "user_age"
                    type: "age"
                    parameters:
                      minAge: 18
                      maxAge: 65
                  - name: "user_email"
                    type: "email"
                    parameters:
                      domain: "example.com"
                output:
                  format: "csv"
                  filePath: "%s"
                  includeHeader: true
                """.formatted(tempDir.resolve("yaml_output.csv").toString());

        Path yamlFile = tempDir.resolve("test_config.yml");
        Files.writeString(yamlFile, yamlContent);

        // 加载配置
        GenerationConfig config = configurationManager.loadConfiguration(yamlFile.toString());

        // 验证配置加载正确
        assertThat(config.getCount()).isEqualTo(100);
        assertThat(config.getSeed()).isEqualTo(12345L);
        assertThat(config.getFields()).hasSize(3);

        // 验证字段配置
        FieldConfig nameField = config.getFields().get(0);
        assertThat(nameField.getName()).isEqualTo("user_name");
        assertThat(nameField.getType()).isEqualTo("name");
        assertThat(nameField.getParameters().get("nameType")).isEqualTo("CHINESE");

        FieldConfig ageField = config.getFields().get(1);
        assertThat(ageField.getName()).isEqualTo("user_age");
        assertThat(ageField.getType()).isEqualTo("age");
        assertThat(ageField.getParameters().get("minAge")).isEqualTo(18);
        assertThat(ageField.getParameters().get("maxAge")).isEqualTo(65);

        // 验证输出配置
        OutputConfig outputConfig = config.getOutputConfig();
        assertThat(outputConfig.getFormat()).isEqualTo("csv");
        assertThat(outputConfig.isIncludeHeader()).isTrue();

        // 执行数据生成验证配置有效性
        dataForgeService.generateData(config);

        // 验证输出文件
        Path outputFile = tempDir.resolve("yaml_output.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(101); // 100条数据 + 1行头部
        assertThat(lines.get(0)).isEqualTo("user_name,user_age,user_email");
    }

    @Test
    @DisplayName("JSON配置文件加载集成测试")
    void testJsonConfigurationLoadingIntegration() throws IOException {
        // 创建JSON配置文件
        String jsonContent = """
                {
                  "count": 50,
                  "seed": 54321,
                  "fields": [
                    {
                      "name": "product_name",
                      "type": "name",
                      "parameters": {
                        "nameType": "ENGLISH"
                      }
                    },
                    {
                      "name": "product_price",
                      "type": "age",
                      "parameters": {
                        "minAge": 10,
                        "maxAge": 1000
                      }
                    }
                  ],
                  "output": {
                    "format": "json",
                    "filePath": "%s",
                    "prettyPrint": true
                  }
                }
                """.formatted(tempDir.resolve("json_output.json").toString().replace("\\", "\\\\"));

        Path jsonFile = tempDir.resolve("test_config.json");
        Files.writeString(jsonFile, jsonContent);

        // 加载配置
        GenerationConfig config = configurationManager.loadConfiguration(jsonFile.toString());

        // 验证配置加载正确
        assertThat(config.getCount()).isEqualTo(50);
        assertThat(config.getSeed()).isEqualTo(54321L);
        assertThat(config.getFields()).hasSize(2);

        // 验证字段配置
        FieldConfig nameField = config.getFields().get(0);
        assertThat(nameField.getName()).isEqualTo("product_name");
        assertThat(nameField.getType()).isEqualTo("name");

        // 验证输出配置
        OutputConfig outputConfig = config.getOutputConfig();
        assertThat(outputConfig.getFormat()).isEqualTo("json");
        assertThat(outputConfig.isPrettyPrint()).isTrue();

        // 执行数据生成验证配置有效性
        dataForgeService.generateData(config);

        // 验证输出文件
        Path outputFile = tempDir.resolve("json_output.json");
        assertThat(outputFile).exists();

        String content = Files.readString(outputFile);
        assertThat(content).contains("\"product_name\":");
        assertThat(content).contains("\"product_price\":");
    }

    @Test
    @DisplayName("复杂配置文件加载集成测试")
    void testComplexConfigurationLoadingIntegration() throws IOException {
        // 创建复杂的YAML配置文件
        String complexYamlContent = """
                count: 200
                seed: 98765

                fields:
                  - name: "id"
                    type: "uuid"
                    parameters:
                      format: "SNOWFLAKE"
                  - name: "name"
                    type: "name"
                    parameters:
                      nameType: "CHINESE"
                      gender: "random"
                  - name: "age"
                    type: "age"
                    parameters:
                      minAge: 20
                      maxAge: 60
                      distribution: "NORMAL"
                      mean: 35
                      stdDev: 10
                  - name: "email"
                    type: "email"
                    parameters:
                      domain: "company.com"
                      baseName: true
                  - name: "phone"
                    type: "phone"
                    parameters:
                      carrier: "CHINA_MOBILE"
                      region: "BEIJING"
                  - name: "idcard"
                    type: "idcard"
                    parameters:
                      regionCode: "110000"
                      minBirthYear: 1960
                      maxBirthYear: 2000

                output:
                  format: "csv"
                  filePath: "%s"
                  includeHeader: true
                  delimiter: ","
                  encoding: "UTF-8"

                validation:
                  enabled: true
                  strictMode: true

                performance:
                  parallelEnabled: true
                  threadCount: 4
                  batchSize: 50
                """.formatted(tempDir.resolve("complex_output.csv").toString());

        Path complexYamlFile = tempDir.resolve("complex_config.yml");
        Files.writeString(complexYamlFile, complexYamlContent);

        // 加载配置
        GenerationConfig config = configurationManager.loadConfiguration(complexYamlFile.toString());

        // 验证基本配置
        assertThat(config.getCount()).isEqualTo(200);
        assertThat(config.getSeed()).isEqualTo(98765L);
        assertThat(config.getFields()).hasSize(6);

        // 验证字段配置
        FieldConfig idField = config.getFields().get(0);
        assertThat(idField.getName()).isEqualTo("id");
        assertThat(idField.getType()).isEqualTo("uuid");
        assertThat(idField.getParameters().get("format")).isEqualTo("SNOWFLAKE");

        FieldConfig ageField = config.getFields().get(2);
        assertThat(ageField.getParameters().get("distribution")).isEqualTo("NORMAL");
        assertThat(ageField.getParameters().get("mean")).isEqualTo(35);

        // 验证输出配置
        OutputConfig outputConfig = config.getOutputConfig();
        assertThat(outputConfig.getFormat()).isEqualTo("csv");
        assertThat(outputConfig.getEncoding()).isEqualTo("UTF-8");

        // 验证验证配置
        assertThat(config.getValidationConfig().isEnabled()).isTrue();
        assertThat(config.getValidationConfig().isStrictMode()).isTrue();

        // 验证性能配置
        assertThat(config.getPerformanceConfig().isParallelEnabled()).isTrue();
        assertThat(config.getPerformanceConfig().getThreadCount()).isEqualTo(4);

        // 执行数据生成验证配置有效性
        dataForgeService.generateData(config);

        // 验证输出文件
        Path outputFile = tempDir.resolve("complex_output.csv");
        assertThat(outputFile).exists();

        List<String> lines = Files.readAllLines(outputFile);
        assertThat(lines).hasSize(201); // 200条数据 + 1行头部

        // 验证头部包含所有字段
        String header = lines.get(0);
        assertThat(header).isEqualTo("id,name,age,email,phone,idcard");

        // 验证数据格式
        String[] firstDataFields = lines.get(1).split(",");
        assertThat(firstDataFields).hasSize(6);
        assertThat(firstDataFields[0]).matches("\\d+"); // snowflake id
        assertThat(firstDataFields[2]).matches("\\d+"); // age
        assertThat(firstDataFields[3]).contains("@"); // email
        assertThat(firstDataFields[4]).matches("1[3-9]\\d{9}"); // phone
        assertThat(firstDataFields[5]).matches("\\d{18}"); // idcard
    }

    @Test
    @DisplayName("配置文件继承集成测试")
    void testConfigurationInheritanceIntegration() throws IOException {
        // 创建基础配置文件
        String baseConfigContent = """
                count: 100
                seed: 12345

                fields:
                  - name: "base_name"
                    type: "name"
                  - name: "base_age"
                    type: "age"

                output:
                  format: "csv"
                  includeHeader: true
                """;

        Path baseConfigFile = tempDir.resolve("base_config.yml");
        Files.writeString(baseConfigFile, baseConfigContent);

        // 创建继承配置文件
        String inheritedConfigContent = """
                extends: "%s"
                count: 50

                fields:
                  - name: "base_name"
                    type: "name"
                    parameters:
                      nameType: "ENGLISH"
                  - name: "base_age"
                    type: "age"
                    parameters:
                      minAge: 25
                      maxAge: 45
                  - name: "additional_email"
                    type: "email"

                output:
                  format: "json"
                  filePath: "%s"
                """.formatted(baseConfigFile.toString(), tempDir.resolve("inherited_output.json").toString());

        Path inheritedConfigFile = tempDir.resolve("inherited_config.yml");
        Files.writeString(inheritedConfigFile, inheritedConfigContent);

        // 加载继承配置
        GenerationConfig config = configurationManager.loadConfiguration(inheritedConfigFile.toString());

        // 验证继承效果
        assertThat(config.getCount()).isEqualTo(50); // 覆盖了基础配置
        assertThat(config.getSeed()).isEqualTo(12345L); // 继承了基础配置
        assertThat(config.getFields()).hasSize(3); // 2个基础字段 + 1个新增字段

        // 验证字段覆盖
        FieldConfig nameField = config.getFields().get(0);
        assertThat(nameField.getName()).isEqualTo("base_name");
        assertThat(nameField.getParameters().get("nameType")).isEqualTo("ENGLISH");

        // 验证输出配置覆盖
        OutputConfig outputConfig = config.getOutputConfig();
        assertThat(outputConfig.getFormat()).isEqualTo("json"); // 覆盖了基础配置
        assertThat(outputConfig.isIncludeHeader()).isTrue(); // 继承了基础配置

        // 执行数据生成验证配置有效性
        dataForgeService.generateData(config);

        // 验证输出文件
        Path outputFile = tempDir.resolve("inherited_output.json");
        assertThat(outputFile).exists();

        String content = Files.readString(outputFile);
        assertThat(content).contains("\"base_name\":");
        assertThat(content).contains("\"base_age\":");
        assertThat(content).contains("\"additional_email\":");
    }

    @Test
    @DisplayName("环境变量替换集成测试")
    void testEnvironmentVariableReplacementIntegration() throws IOException {
        // 设置环境变量（模拟）
        System.setProperty("DATA_COUNT", "75");
        System.setProperty("OUTPUT_PATH", tempDir.resolve("env_output.csv").toString());

        try {
            // 创建包含环境变量的配置文件
            String envConfigContent = """
                    count: ${DATA_COUNT}
                    seed: 11111

                    fields:
                      - name: "test_name"
                        type: "name"
                      - name: "test_age"
                        type: "age"

                    output:
                      format: "csv"
                      filePath: "${OUTPUT_PATH}"
                      includeHeader: true
                    """;

            Path envConfigFile = tempDir.resolve("env_config.yml");
            Files.writeString(envConfigFile, envConfigContent);

            // 加载配置
            GenerationConfig config = configurationManager.loadConfiguration(envConfigFile.toString());

            // 验证环境变量替换
            assertThat(config.getCount()).isEqualTo(75);
            assertThat(config.getOutputConfig().getFilePath()).isEqualTo(tempDir.resolve("env_output.csv").toString());

            // 执行数据生成验证配置有效性
            dataForgeService.generateData(config);

            // 验证输出文件
            Path outputFile = tempDir.resolve("env_output.csv");
            assertThat(outputFile).exists();

            List<String> lines = Files.readAllLines(outputFile);
            assertThat(lines).hasSize(76); // 75条数据 + 1行头部

        } finally {
            // 清理环境变量
            System.clearProperty("DATA_COUNT");
            System.clearProperty("OUTPUT_PATH");
        }
    }

    @Test
    @DisplayName("配置验证集成测试")
    void testConfigurationValidationIntegration() throws IOException {
        // 创建无效配置文件
        String invalidConfigContent = """
                count: -10
                seed: "invalid_seed"

                fields:
                  - name: ""
                    type: "invalid_type"

                output:
                  format: "unsupported_format"
                  filePath: ""
                """;

        Path invalidConfigFile = tempDir.resolve("invalid_config.yml");
        Files.writeString(invalidConfigFile, invalidConfigContent);

        // 验证配置加载时的验证
        assertThatThrownBy(() -> configurationManager.loadConfiguration(invalidConfigFile.toString()))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessageContaining("配置验证失败");
    }

    @Test
    @DisplayName("配置文件格式自动检测集成测试")
    void testConfigurationFormatAutoDetectionIntegration() throws IOException {
        // 创建不同格式的配置文件
        String yamlContent = """
                count: 30
                fields:
                  - name: "test_field"
                    type: "name"
                output:
                  format: "csv"
                  filePath: "%s"
                """.formatted(tempDir.resolve("auto_yaml_output.csv").toString());

        String jsonContent = """
                {
                  "count": 30,
                  "fields": [
                    {
                      "name": "test_field",
                      "type": "name"
                    }
                  ],
                  "output": {
                    "format": "csv",
                    "filePath": "%s"
                  }
                }
                """.formatted(tempDir.resolve("auto_json_output.csv").toString().replace("\\", "\\\\"));

        // 保存为不同扩展名的文件
        Path yamlFile = tempDir.resolve("auto_test.yaml");
        Path jsonFile = tempDir.resolve("auto_test.json");

        Files.writeString(yamlFile, yamlContent);
        Files.writeString(jsonFile, jsonContent);

        // 加载YAML配置
        GenerationConfig yamlConfig = configurationManager.loadConfiguration(yamlFile.toString());
        assertThat(yamlConfig.getCount()).isEqualTo(30);

        // 加载JSON配置
        GenerationConfig jsonConfig = configurationManager.loadConfiguration(jsonFile.toString());
        assertThat(jsonConfig.getCount()).isEqualTo(30);

        // 执行数据生成验证两种格式都能正常工作
        dataForgeService.generateData(yamlConfig);
        dataForgeService.generateData(jsonConfig);

        // 验证输出文件
        assertThat(tempDir.resolve("auto_yaml_output.csv")).exists();
        assertThat(tempDir.resolve("auto_json_output.csv")).exists();
    }
}