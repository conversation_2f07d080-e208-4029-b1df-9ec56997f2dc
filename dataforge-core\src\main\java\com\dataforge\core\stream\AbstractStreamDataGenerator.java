package com.dataforge.core.stream;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.generator.GenerationException;
import com.dataforge.core.model.GenerationContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

/**
 * 抽象流式数据生成器
 * 
 * 为流式数据生成器提供通用实现，包装现有的DataGenerator
 * 以提供流式生成能力。
 * 
 * @param <T> 生成的数据类型
 * <AUTHOR> Team
 * @version 1.0.0
 */
public abstract class AbstractStreamDataGenerator<T> implements StreamDataGenerator<T> {

    private static final Logger logger = LoggerFactory.getLogger(AbstractStreamDataGenerator.class);

    protected final DataGenerator<T> baseGenerator;

    /**
     * 构造函数
     * 
     * @param baseGenerator 基础数据生成器
     */
    protected AbstractStreamDataGenerator(DataGenerator<T> baseGenerator) {
        this.baseGenerator = baseGenerator;
    }

    @Override
    public Stream<T> generateStream(GenerationContext context, long count) {
        if (count <= 0) {
            return Stream.empty();
        }

        logger.debug("Generating stream of {} items using generator: {}", count, getType());

        return Stream.generate(() -> {
            try {
                return baseGenerator.generate(context);
            } catch (GenerationException e) {
                logger.error("Error generating data in stream: {}", e.getMessage(), e);
                throw new RuntimeException("Stream generation failed", e);
            }
        }).limit(count);
    }

    @Override
    public Stream<T> generateInfiniteStream(GenerationContext context) {
        logger.debug("Generating infinite stream using generator: {}", getType());

        return Stream.generate(() -> {
            try {
                return baseGenerator.generate(context);
            } catch (GenerationException e) {
                logger.error("Error generating data in infinite stream: {}", e.getMessage(), e);
                throw new RuntimeException("Infinite stream generation failed", e);
            }
        });
    }

    @Override
    public Stream<Batch<T>> generateBatchStream(GenerationContext context, int batchSize, long batchCount) {
        if (batchCount <= 0 || batchSize <= 0) {
            return Stream.empty();
        }

        logger.debug("Generating {} batches of size {} using generator: {}",
                batchCount, batchSize, getType());

        return Stream.iterate(0L, i -> i < batchCount, i -> i + 1)
                .map(batchNumber -> {
                    List<T> batchData = generateBatch(context, batchSize);
                    return new Batch<>(batchData, batchNumber, batchCount);
                });
    }

    /**
     * 生成单个批次的数据
     * 
     * @param context   生成上下文
     * @param batchSize 批次大小
     * @return 批次数据
     */
    protected List<T> generateBatch(GenerationContext context, int batchSize) {
        List<T> batch = new ArrayList<>(batchSize);
        for (int i = 0; i < batchSize; i++) {
            try {
                batch.add(baseGenerator.generate(context));
            } catch (GenerationException e) {
                logger.error("Error generating data in batch at index {}: {}", i, e.getMessage(), e);
                throw new RuntimeException("Batch generation failed at index " + i, e);
            }
        }
        return batch;
    }

    @Override
    public String getType() {
        return baseGenerator.getType() + "_stream";
    }

    @Override
    public boolean supportsParallelStream() {
        // 默认支持并行流，子类可以重写
        return true;
    }

    @Override
    public int getRecommendedBatchSize() {
        // 根据数据类型和复杂度调整批次大小
        return 1000;
    }

    /**
     * 创建并行流
     * 
     * @param context 生成上下文
     * @param count   生成数量
     * @return 并行数据流
     */
    public Stream<T> generateParallelStream(GenerationContext context, long count) {
        if (!supportsParallelStream()) {
            logger.warn("Generator {} does not support parallel streams, using sequential stream", getType());
            return generateStream(context, count);
        }

        logger.debug("Generating parallel stream of {} items using generator: {}", count, getType());

        return Stream.generate(() -> {
            // 为每个线程创建独立的上下文以确保线程安全
            GenerationContext threadContext = createThreadSafeContext(context);
            try {
                return baseGenerator.generate(threadContext);
            } catch (GenerationException e) {
                logger.error("Error generating data in parallel stream: {}", e.getMessage(), e);
                throw new RuntimeException("Parallel stream generation failed", e);
            }
        })
                .parallel()
                .limit(count);
    }

    /**
     * 创建线程安全的生成上下文
     * 
     * @param original 原始上下文
     * @return 线程安全的上下文
     */
    protected GenerationContext createThreadSafeContext(GenerationContext original) {
        return new GenerationContext.Builder()
                .copyFrom(original)
                .withNewRandomSeed()
                .build();
    }

    /**
     * 创建带背压控制的流
     * 
     * @param context    生成上下文
     * @param count      生成数量
     * @param bufferSize 缓冲区大小
     * @return 带背压控制的数据流
     */
    public Stream<T> generateBufferedStream(GenerationContext context, long count, int bufferSize) {
        logger.debug("Generating buffered stream of {} items with buffer size {} using generator: {}",
                count, bufferSize, getType());

        return generateBatchStream(context, bufferSize, (count + bufferSize - 1) / bufferSize)
                .flatMap(batch -> batch.data().stream());
    }

    /**
     * 创建带进度回调的流
     * 
     * @param context          生成上下文
     * @param count            生成数量
     * @param progressCallback 进度回调
     * @return 数据流
     */
    public Stream<T> generateStreamWithProgress(GenerationContext context, long count,
            ProgressCallback progressCallback) {
        if (progressCallback == null) {
            return generateStream(context, count);
        }

        logger.debug("Generating stream with progress tracking of {} items using generator: {}",
                count, getType());

        final long[] generated = { 0 };

        return generateStream(context, count)
                .peek(item -> {
                    long current = ++generated[0];
                    if (current % 1000 == 0 || current == count) {
                        double progress = (double) current / count * 100;
                        progressCallback.onProgress(current, count, progress);
                    }
                });
    }

    /**
     * 进度回调接口
     */
    @FunctionalInterface
    public interface ProgressCallback {
        /**
         * 进度更新回调
         * 
         * @param current    当前已生成数量
         * @param total      总数量
         * @param percentage 完成百分比
         */
        void onProgress(long current, long total, double percentage);
    }
}