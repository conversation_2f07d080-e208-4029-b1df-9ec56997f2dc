package com.dataforge.generators.text;

import com.dataforge.core.model.GenerationContext;
import com.dataforge.core.generator.GeneratorParameter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.RepeatedTest;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Pattern;

import static org.assertj.core.api.Assertions.*;

/**
 * 邮箱地址生成器单元测试
 */
@DisplayName("邮箱地址生成器测试")
class EmailGeneratorTest {

    private EmailGenerator generator;
    private GenerationContext context;

    @BeforeEach
    void setUp() {
        generator = new EmailGenerator();
        Map<String, Object> parameters = new HashMap<>();
        context = new GenerationContext(parameters, 12345L);
    }

    @Test
    @DisplayName("基本邮箱地址生成测试")
    void testBasicGeneration() {
        String email = generator.generate(context);

        assertThat(email).isNotNull();
        assertThat(email).contains("@");
        assertThat(email).matches("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}");
    }

    @RepeatedTest(10)
    @DisplayName("重复生成测试 - 验证格式一致性")
    void testRepeatedGeneration() {
        String email = generator.generate(context);

        assertThat(email).isNotNull();
        assertThat(email).contains("@");
        assertThat(generator.validate(email)).isTrue();
    }

    @Test
    @DisplayName("指定域名生成测试")
    void testGenerationWithDomain() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("domain", "example.com");
        GenerationContext contextWithDomain = new GenerationContext(parameters, 12345L);

        String email = generator.generate(contextWithDomain);

        assertThat(email).isNotNull();
        assertThat(email).endsWith("@example.com");
        assertThat(generator.validate(email)).isTrue();
    }

    @Test
    @DisplayName("基于姓名生成邮箱测试")
    void testGenerationWithName() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("baseName", "张三");
        GenerationContext contextWithName = new GenerationContext(parameters, 12345L);

        String email = generator.generate(contextWithName);

        assertThat(email).isNotNull();
        assertThat(email).contains("@");
        // 应该包含姓名的拼音或相关信息
        assertThat(generator.validate(email)).isTrue();
    }

    @Test
    @DisplayName("指定用户名长度生成测试")
    void testGenerationWithUsernameLength() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("usernameLength", 8);
        GenerationContext contextWithLength = new GenerationContext(parameters, 12345L);

        String email = generator.generate(contextWithLength);

        assertThat(email).isNotNull();
        String username = email.split("@")[0];
        assertThat(username.length()).isEqualTo(8);
        assertThat(generator.validate(email)).isTrue();
    }

    @Test
    @DisplayName("邮箱地址校验测试 - 有效地址")
    void testValidationWithValidEmail() {
        String[] validEmails = {
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>",
                "<EMAIL>"
        };

        for (String email : validEmails) {
            assertThat(generator.validate(email)).isTrue();
        }
    }

    @Test
    @DisplayName("邮箱地址校验测试 - 无效地址")
    void testValidationWithInvalidEmail() {
        String[] invalidEmails = {
                "invalid-email", // 缺少@符号
                "@example.com", // 缺少用户名
                "user@", // 缺少域名
                "user@domain", // 缺少顶级域名
                "<EMAIL>", // 连续的点
                "<EMAIL>", // 域名中连续的点
                "", // 空字符串
                "user <EMAIL>" // 用户名包含空格
        };

        for (String email : invalidEmails) {
            assertThat(generator.validate(email)).isFalse();
        }

        // 测试null值
        assertThat(generator.validate(null)).isFalse();
    }

    @Test
    @DisplayName("生成器类型测试")
    void testGeneratorType() {
        assertThat(generator.getType()).isEqualTo("email");
    }

    @Test
    @DisplayName("支持参数列表测试")
    void testSupportedParameters() {
        List<GeneratorParameter> parameters = generator.getSupportedParameters();

        assertThat(parameters).isNotEmpty();
        assertThat(parameters).extracting(GeneratorParameter::getName)
                .contains("domain", "baseName", "usernameLength");
    }

    @Test
    @DisplayName("生成数据格式一致性测试")
    void testGeneratedDataConsistency() {
        Pattern emailPattern = Pattern.compile("[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}");

        for (int i = 0; i < 100; i++) {
            String email = generator.generate(context);
            assertThat(email).matches(emailPattern);
            assertThat(generator.validate(email)).isTrue();
        }
    }

    @Test
    @DisplayName("不同域名提供商测试")
    void testDifferentDomainProviders() {
        String[] domains = { "gmail.com", "yahoo.com", "hotmail.com", "163.com", "qq.com" };

        for (String domain : domains) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("domain", domain);
            GenerationContext domainContext = new GenerationContext(parameters, 12345L);

            String email = generator.generate(domainContext);
            assertThat(email).isNotNull();
            assertThat(email).endsWith("@" + domain);
            assertThat(generator.validate(email)).isTrue();
        }
    }

    @Test
    @DisplayName("用户名生成策略测试")
    void testUsernameGenerationStrategies() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("usernameStrategy", "RANDOM");
        GenerationContext randomContext = new GenerationContext(parameters, 12345L);

        String randomEmail = generator.generate(randomContext);
        assertThat(randomEmail).isNotNull();
        assertThat(generator.validate(randomEmail)).isTrue();

        // 测试基于词典的用户名生成
        parameters.put("usernameStrategy", "DICTIONARY");
        GenerationContext dictContext = new GenerationContext(parameters, 12345L);

        String dictEmail = generator.generate(dictContext);
        assertThat(dictEmail).isNotNull();
        assertThat(generator.validate(dictEmail)).isTrue();
    }

    @Test
    @DisplayName("特殊字符处理测试")
    void testSpecialCharacterHandling() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("allowSpecialChars", true);
        GenerationContext specialContext = new GenerationContext(parameters, 12345L);

        String email = generator.generate(specialContext);
        assertThat(email).isNotNull();
        assertThat(generator.validate(email)).isTrue();

        // 可能包含点、加号、下划线等特殊字符
        String username = email.split("@")[0];
        assertThat(username).matches("[a-zA-Z0-9._%+-]+");
    }

    @Test
    @DisplayName("国际化域名测试")
    void testInternationalDomains() {
        String[] intlDomains = { "example.co.uk", "test.com.cn", "sample.org.au" };

        for (String domain : intlDomains) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("domain", domain);
            GenerationContext intlContext = new GenerationContext(parameters, 12345L);

            String email = generator.generate(intlContext);
            assertThat(email).isNotNull();
            assertThat(email).endsWith("@" + domain);
            assertThat(generator.validate(email)).isTrue();
        }
    }

    @Test
    @DisplayName("边界值测试")
    void testBoundaryValues() {
        // 测试最短用户名长度
        Map<String, Object> shortParams = new HashMap<>();
        shortParams.put("usernameLength", 1);
        GenerationContext shortContext = new GenerationContext(shortParams, 12345L);

        String shortEmail = generator.generate(shortContext);
        String shortUsername = shortEmail.split("@")[0];
        assertThat(shortUsername.length()).isEqualTo(1);
        assertThat(generator.validate(shortEmail)).isTrue();

        // 测试较长用户名长度
        Map<String, Object> longParams = new HashMap<>();
        longParams.put("usernameLength", 20);
        GenerationContext longContext = new GenerationContext(longParams, 12345L);

        String longEmail = generator.generate(longContext);
        String longUsername = longEmail.split("@")[0];
        assertThat(longUsername.length()).isEqualTo(20);
        assertThat(generator.validate(longEmail)).isTrue();
    }

    @Test
    @DisplayName("中文姓名转拼音测试")
    void testChineseNameToPinyin() {
        String[] chineseNames = { "张三", "李四", "王五" };

        for (String name : chineseNames) {
            Map<String, Object> parameters = new HashMap<>();
            parameters.put("baseName", name);
            GenerationContext nameContext = new GenerationContext(parameters, 12345L);

            String email = generator.generate(nameContext);
            assertThat(email).isNotNull();
            assertThat(generator.validate(email)).isTrue();

            // 用户名应该是拼音或相关的英文字符
            String username = email.split("@")[0];
            assertThat(username).matches("[a-zA-Z0-9._%+-]+");
        }
    }

    @Test
    @DisplayName("无效参数处理测试")
    void testInvalidParameters() {
        Map<String, Object> parameters = new HashMap<>();
        parameters.put("domain", "invalid-domain");
        parameters.put("usernameLength", -1);
        GenerationContext invalidContext = new GenerationContext(parameters, 12345L);

        // 应该能够处理无效参数并生成有效的邮箱地址
        String email = generator.generate(invalidContext);
        assertThat(email).isNotNull();
        assertThat(generator.validate(email)).isTrue();
    }

    @Test
    @DisplayName("性能测试")
    void testPerformance() {
        long startTime = System.currentTimeMillis();

        for (int i = 0; i < 10000; i++) {
            String email = generator.generate(context);
            assertThat(email).isNotNull();
        }

        long endTime = System.currentTimeMillis();
        long duration = endTime - startTime;

        assertThat(duration).isLessThan(3000); // 应该在3秒内完成
    }

    @Test
    @DisplayName("唯一性测试")
    void testUniqueness() {
        java.util.Set<String> emails = new java.util.HashSet<>();

        for (int i = 0; i < 1000; i++) {
            String email = generator.generate(context);
            emails.add(email);
        }

        // 期望有较高的唯一性
        assertThat(emails.size()).isGreaterThan(950);
    }

    @Test
    @DisplayName("邮箱长度限制测试")
    void testEmailLengthLimits() {
        String email = generator.generate(context);

        // 邮箱总长度不应超过254个字符（RFC 5321标准）
        assertThat(email.length()).isLessThanOrEqualTo(254);

        // 用户名部分不应超过64个字符
        String username = email.split("@")[0];
        assertThat(username.length()).isLessThanOrEqualTo(64);
    }
}