package com.dataforge.core.service;

import com.dataforge.core.model.GenerationConfig;
import com.dataforge.core.model.ValidationResult;
import com.dataforge.core.model.FieldConfig;
import com.dataforge.core.model.OutputConfig;
import com.dataforge.core.model.ValidationConfig;
import com.dataforge.core.model.PerformanceConfig;
import org.springframework.stereotype.Service;

import java.io.File;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.regex.Pattern;

/**
 * 配置验证器
 * 
 * 负责验证配置的完整性、参数类型检查、范围验证和配置冲突检测。
 * 提供详细的验证错误信息和修复建议。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
@Service
public class ConfigurationValidator {

    // 支持的数据类型
    private static final Set<String> SUPPORTED_DATA_TYPES = new HashSet<>(Arrays.asList(
            "name", "phone", "email", "idcard", "bankcard", "uscc", "uuid",
            "age", "gender", "password", "account", "ip", "mac", "url", "domain", "port"));

    // 支持的输出格式
    private static final Set<String> SUPPORTED_OUTPUT_FORMATS = new HashSet<>(Arrays.asList(
            "csv", "json", "xml", "sql", "console"));

    // 支持的编码格式
    private static final Set<String> SUPPORTED_ENCODINGS = new HashSet<>(Arrays.asList(
            "UTF-8", "GBK", "GB2312", "ISO-8859-1", "US-ASCII"));

    // 参数验证规则
    private static final Map<String, ParameterRule> PARAMETER_RULES = Map.of(
            "name.type", new ParameterRule(Set.of("CN", "EN", "BOTH"), "姓名类型"),
            "name.gender", new ParameterRule(Set.of("MALE", "FEMALE", "ANY"), "性别"),
            "phone.region", new ParameterRule(Set.of("CN"), "手机号地区"),
            "idcard.gender", new ParameterRule(Set.of("MALE", "FEMALE", "ANY"), "身份证性别"),
            "bankcard.type", new ParameterRule(Set.of("DEBIT", "CREDIT", "BOTH"), "银行卡类型"),
            "bankcard.issuer", new ParameterRule(Set.of("VISA", "MC", "UNIONPAY", "JCB", "ANY"), "卡组织"));

    // 数字范围验证
    private static final Pattern REGION_CODE_PATTERN = Pattern.compile("\\d{6}");
    private static final Pattern DATE_RANGE_PATTERN = Pattern.compile("\\d{4}-\\d{2}-\\d{2},\\d{4}-\\d{2}-\\d{2}");
    private static final Pattern LENGTH_RANGE_PATTERN = Pattern.compile("\\d+,\\d+");

    /**
     * 验证生成配置的完整性
     * 
     * @param config 生成配置
     * @return 验证结果
     */
    public ValidationResult validateConfiguration(GenerationConfig config) {
        ValidationResult result = new ValidationResult();

        if (config == null) {
            result.addError("配置对象不能为空");
            return result;
        }

        // 基本配置验证
        validateBasicConfiguration(config, result);

        // 参数验证
        validateParameters(config, result);

        // 输出配置验证
        validateOutputConfiguration(config.getOutputConfig(), result);

        // 校验配置验证
        validateValidationConfiguration(config.getValidationConfig(), result);

        // 性能配置验证
        validatePerformanceConfiguration(config.getPerformanceConfig(), result);

        // 字段配置验证
        if (config.isMultiFieldMode()) {
            validateFieldConfigurations(config.getFields(), result);
        }

        // 配置冲突检测
        detectConfigurationConflicts(config, result);

        return result;
    }

    /**
     * 验证基本配置
     */
    private void validateBasicConfiguration(GenerationConfig config, ValidationResult result) {
        // 验证数据类型
        if (!config.isMultiFieldMode()) {
            if (config.getDataType() == null || config.getDataType().trim().isEmpty()) {
                result.addError("数据类型不能为空");
            } else if (!SUPPORTED_DATA_TYPES.contains(config.getDataType().toLowerCase())) {
                result.addError("不支持的数据类型: " + config.getDataType() +
                        "。支持的类型: " + SUPPORTED_DATA_TYPES);
            }
        }

        // 验证生成数量
        if (config.getCount() <= 0) {
            result.addError("生成数量必须大于0，当前值: " + config.getCount());
        } else if (config.getCount() > 1000000) {
            result.addWarning("生成数量过大(" + config.getCount() + ")，可能影响性能");
        }

        // 验证随机种子
        if (config.getSeed() != null && config.getSeed() < 0) {
            result.addWarning("随机种子为负数，可能导致不可预期的结果");
        }
    }

    /**
     * 验证参数配置
     */
    private void validateParameters(GenerationConfig config, ValidationResult result) {
        Map<String, Object> parameters = config.getParameters();
        if (parameters == null || parameters.isEmpty()) {
            return;
        }

        for (Map.Entry<String, Object> entry : parameters.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (value == null) {
                result.addWarning("参数 " + key + " 的值为空");
                continue;
            }

            validateParameter(key, value, result);
        }
    }

    /**
     * 验证单个参数
     */
    private void validateParameter(String key, Object value, ValidationResult result) {
        String valueStr = value.toString();

        // 检查预定义的参数规则
        ParameterRule rule = PARAMETER_RULES.get(key);
        if (rule != null) {
            if (!rule.allowedValues.contains(valueStr.toUpperCase())) {
                result.addError("参数 " + key + " 的值无效: " + valueStr +
                        "。允许的值: " + rule.allowedValues);
            }
            return;
        }

        // 特殊参数验证
        if (key.endsWith(".region") && key.startsWith("idcard.")) {
            if (!REGION_CODE_PATTERN.matcher(valueStr).matches()) {
                result.addError("身份证地区代码格式无效: " + valueStr + "，应为6位数字");
            }
        } else if (key.endsWith(".birth-date-range")) {
            if (!DATE_RANGE_PATTERN.matcher(valueStr).matches()) {
                result.addError("出生日期范围格式无效: " + valueStr +
                        "，应为 YYYY-MM-DD,YYYY-MM-DD 格式");
            }
        } else if (key.endsWith(".username-length") || key.endsWith("-length")) {
            if (!LENGTH_RANGE_PATTERN.matcher(valueStr).matches()) {
                result.addError("长度范围格式无效: " + valueStr + "，应为 min,max 格式");
            } else {
                String[] parts = valueStr.split(",");
                try {
                    int min = Integer.parseInt(parts[0]);
                    int max = Integer.parseInt(parts[1]);
                    if (min < 0 || max < 0) {
                        result.addError("长度范围不能为负数: " + valueStr);
                    } else if (min > max) {
                        result.addError("最小长度不能大于最大长度: " + valueStr);
                    }
                } catch (NumberFormatException e) {
                    result.addError("长度范围必须为数字: " + valueStr);
                }
            }
        } else if (key.endsWith(".min") || key.endsWith(".max")) {
            try {
                int numValue = Integer.parseInt(valueStr);
                if (key.contains("age") && (numValue < 0 || numValue > 150)) {
                    result.addWarning("年龄值可能不合理: " + numValue);
                }
            } catch (NumberFormatException e) {
                result.addError("数值参数格式无效: " + key + " = " + valueStr);
            }
        } else if (key.equals("valid")) {
            if (!"true".equalsIgnoreCase(valueStr) && !"false".equalsIgnoreCase(valueStr)) {
                result.addError("布尔参数值无效: " + key + " = " + valueStr);
            }
        }
    }

    /**
     * 验证输出配置
     */
    private void validateOutputConfiguration(OutputConfig outputConfig, ValidationResult result) {
        if (outputConfig == null) {
            result.addError("输出配置不能为空");
            return;
        }

        // 验证输出格式
        String format = outputConfig.getFormat();
        if (format == null || format.trim().isEmpty()) {
            result.addError("输出格式不能为空");
        } else if (!SUPPORTED_OUTPUT_FORMATS.contains(format.toLowerCase())) {
            result.addError("不支持的输出格式: " + format +
                    "。支持的格式: " + SUPPORTED_OUTPUT_FORMATS);
        }

        // 验证输出文件
        String file = outputConfig.getFile();
        if (file != null && !file.trim().isEmpty()) {
            File outputFile = new File(file);
            File parentDir = outputFile.getParentFile();

            if (parentDir != null && !parentDir.exists()) {
                result.addWarning("输出目录不存在: " + parentDir.getAbsolutePath());
            }

            if (outputFile.exists() && !outputFile.canWrite()) {
                result.addError("输出文件不可写: " + file);
            }
        }

        // 验证编码
        String encoding = outputConfig.getEncoding();
        if (encoding != null && !SUPPORTED_ENCODINGS.contains(encoding.toUpperCase())) {
            result.addWarning("不常用的编码格式: " + encoding +
                    "。推荐使用: " + SUPPORTED_ENCODINGS);
        }
    }

    /**
     * 验证校验配置
     */
    private void validateValidationConfiguration(ValidationConfig validationConfig, ValidationResult result) {
        if (validationConfig == null) {
            result.addWarning("校验配置为空，将使用默认配置");
            return;
        }

        // 配置冲突检查
        if (validationConfig.isStrictMode() && validationConfig.shouldSkipInvalidData()) {
            result.addWarning("严格模式和跳过无效数据同时启用，可能导致意外行为");
        }
    }

    /**
     * 验证性能配置
     */
    private void validatePerformanceConfiguration(PerformanceConfig performanceConfig, ValidationResult result) {
        if (performanceConfig == null) {
            result.addWarning("性能配置为空，将使用默认配置");
            return;
        }

        // 使用性能配置自身的验证方法
        ValidationResult perfResult = performanceConfig.validate();
        result.merge(perfResult);

        // 验证线程池大小
        int threadPoolSize = performanceConfig.getThreadPoolSize();
        if (threadPoolSize > Runtime.getRuntime().availableProcessors() * 2) {
            result.addWarning("线程池大小(" + threadPoolSize + ")可能过大，建议不超过CPU核心数的2倍");
        }

        // 验证批处理大小
        int batchSize = performanceConfig.getBatchSize();
        if (batchSize > 10000) {
            result.addWarning("批处理大小(" + batchSize + ")可能过大，建议不超过10000");
        }
    }

    /**
     * 验证字段配置列表
     */
    private void validateFieldConfigurations(List<FieldConfig> fields, ValidationResult result) {
        if (fields == null || fields.isEmpty()) {
            result.addError("多字段模式下字段列表不能为空");
            return;
        }

        Set<String> fieldNames = new HashSet<>();
        for (int i = 0; i < fields.size(); i++) {
            FieldConfig field = fields.get(i);
            if (field == null) {
                result.addError("字段[" + i + "]配置为空");
                continue;
            }

            // 验证字段名唯一性
            String fieldName = field.getName();
            if (fieldName == null || fieldName.trim().isEmpty()) {
                result.addError("字段[" + i + "]名称不能为空");
            } else if (fieldNames.contains(fieldName)) {
                result.addError("字段名称重复: " + fieldName);
            } else {
                fieldNames.add(fieldName);
            }

            // 验证字段配置
            ValidationResult fieldResult = field.validate();
            if (!fieldResult.isValid()) {
                result.addError("字段[" + i + "](" + fieldName + ")配置无效: " +
                        String.join(", ", fieldResult.getErrors()));
            }
        }
    }

    /**
     * 检测配置冲突
     */
    private void detectConfigurationConflicts(GenerationConfig config, ValidationResult result) {
        // 检查输出格式与文件扩展名的匹配
        OutputConfig outputConfig = config.getOutputConfig();
        if (outputConfig.getFile() != null && !outputConfig.getFile().trim().isEmpty()) {
            String file = outputConfig.getFile().toLowerCase();
            String format = outputConfig.getFormat().toLowerCase();

            if ((format.equals("csv") && !file.endsWith(".csv")) ||
                    (format.equals("json") && !file.endsWith(".json")) ||
                    (format.equals("xml") && !file.endsWith(".xml")) ||
                    (format.equals("sql") && !file.endsWith(".sql"))) {
                result.addWarning("输出格式(" + format + ")与文件扩展名不匹配: " + outputConfig.getFile());
            }
        }

        // 检查性能配置与数据量的匹配
        PerformanceConfig perfConfig = config.getPerformanceConfig();
        if (perfConfig != null && config.getCount() > 0) {
            if (config.getCount() < 100 && perfConfig.isEnableParallel()) {
                result.addWarning("数据量较小(" + config.getCount() + ")时启用并行处理可能不会提升性能");
            }

            if (config.getCount() > 100000 && !perfConfig.isEnableParallel()) {
                result.addWarning("大数据量(" + config.getCount() + ")建议启用并行处理以提升性能");
            }
        }

        // 检查校验配置与性能的平衡
        ValidationConfig validConfig = config.getValidationConfig();
        if (validConfig != null && validConfig.isEnabled() && validConfig.isStrictMode() &&
                config.getCount() > 50000) {
            result.addWarning("大数据量下启用严格校验模式可能显著影响性能");
        }
    }

    /**
     * 参数规则内部类
     */
    private static class ParameterRule {
        final Set<String> allowedValues;
        final String description;

        ParameterRule(Set<String> allowedValues, String description) {
            this.allowedValues = allowedValues;
            this.description = description;
        }
    }
}