package com.dataforge.core.performance;

import com.dataforge.core.generator.DataGenerator;
import com.dataforge.core.generator.GenerationException;
import com.dataforge.core.model.GenerationContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;

/**
 * 并行数据生成服务
 * 
 * 提供多线程数据生成能力，支持任务分片、并发执行和结果合并。
 * 
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class ParallelGenerationService {

    private static final Logger logger = LoggerFactory.getLogger(ParallelGenerationService.class);

    private final ThreadPoolExecutor executor;
    private final int defaultBatchSize;

    /**
     * 构造函数
     * 
     * @param config 线程池配置
     */
    public ParallelGenerationService(ThreadPoolConfig config) {
        this.executor = config.createThreadPoolExecutor();
        this.defaultBatchSize = calculateOptimalBatchSize();
    }

    /**
     * 并行生成数据
     * 
     * @param generator  数据生成器
     * @param context    生成上下文
     * @param totalCount 总生成数量
     * @param <T>        数据类型
     * @return 生成的数据列表
     * @throws InterruptedException 如果线程被中断
     * @throws ExecutionException   如果执行过程中出现异常
     * @throws GenerationException  如果数据生成过程中出现异常
     */
    public <T> List<T> generateParallel(DataGenerator<T> generator, GenerationContext context, int totalCount)
            throws InterruptedException, ExecutionException, GenerationException {

        if (totalCount <= 0) {
            return new ArrayList<>();
        }

        // 如果数量较小，直接单线程生成
        if (totalCount < defaultBatchSize) {
            return generateSequential(generator, context, totalCount);
        }

        logger.info("Starting parallel generation of {} items using {} threads",
                totalCount, executor.getCorePoolSize());

        long startTime = System.currentTimeMillis();

        // 计算任务分片
        List<GenerationTask<T>> tasks = createTasks(generator, context, totalCount);

        // 提交所有任务
        List<Future<List<T>>> futures = new ArrayList<>();
        for (GenerationTask<T> task : tasks) {
            futures.add(executor.submit(task));
        }

        // 收集结果
        List<T> results = new ArrayList<>(totalCount);
        for (Future<List<T>> future : futures) {
            try {
                List<T> taskResult = future.get();
                results.addAll(taskResult);
            } catch (ExecutionException e) {
                logger.error("Task execution failed", e);
                // 取消其他任务
                futures.forEach(f -> f.cancel(true));
                throw e;
            }
        }

        long endTime = System.currentTimeMillis();
        logger.info("Parallel generation completed in {} ms, generated {} items",
                endTime - startTime, results.size());

        return results;
    }

    /**
     * 顺序生成数据（用于小数量场景）
     */
    private <T> List<T> generateSequential(DataGenerator<T> generator, GenerationContext context, int count)
            throws GenerationException {
        List<T> results = new ArrayList<>(count);
        for (int i = 0; i < count; i++) {
            try {
                results.add(generator.generate(context));
            } catch (GenerationException e) {
                logger.error("Failed to generate data at index {}", i, e);
                throw e;
            }
        }
        return results;
    }

    /**
     * 创建生成任务列表
     */
    private <T> List<GenerationTask<T>> createTasks(DataGenerator<T> generator, GenerationContext context,
            int totalCount) {
        List<GenerationTask<T>> tasks = new ArrayList<>();

        int threadCount = Math.min(executor.getCorePoolSize(), totalCount);
        int baseTaskSize = totalCount / threadCount;
        int remainder = totalCount % threadCount;

        for (int i = 0; i < threadCount; i++) {
            int taskSize = baseTaskSize + (i < remainder ? 1 : 0);
            if (taskSize > 0) {
                // 为每个任务创建独立的上下文副本以确保线程安全
                GenerationContext taskContext = createThreadSafeContext(context);
                tasks.add(new GenerationTask<>(generator, taskContext, taskSize, i));
            }
        }

        logger.debug("Created {} tasks for {} items", tasks.size(), totalCount);
        return tasks;
    }

    /**
     * 创建线程安全的生成上下文
     */
    private GenerationContext createThreadSafeContext(GenerationContext original) {
        // 创建上下文的副本，确保每个线程有独立的随机数生成器
        return new GenerationContext.Builder()
                .copyFrom(original)
                .withNewRandomSeed()
                .build();
    }

    /**
     * 计算最优批次大小
     */
    private int calculateOptimalBatchSize() {
        int processors = Runtime.getRuntime().availableProcessors();
        return Math.max(100, processors * 50); // 每个处理器至少50个项目才启用并行
    }

    /**
     * 获取线程池状态信息
     */
    public ThreadPoolStatus getThreadPoolStatus() {
        return new ThreadPoolStatus(
                executor.getCorePoolSize(),
                executor.getMaximumPoolSize(),
                executor.getActiveCount(),
                executor.getTaskCount(),
                executor.getCompletedTaskCount(),
                executor.getQueue().size());
    }

    /**
     * 关闭服务
     */
    public void shutdown() {
        logger.info("Shutting down parallel generation service");
        executor.shutdown();
        try {
            if (!executor.awaitTermination(30, TimeUnit.SECONDS)) {
                logger.warn("Executor did not terminate gracefully, forcing shutdown");
                executor.shutdownNow();
            }
        } catch (InterruptedException e) {
            logger.warn("Interrupted while waiting for executor termination");
            executor.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }

    /**
     * 线程池状态信息
     */
    public static class ThreadPoolStatus {
        private final int corePoolSize;
        private final int maximumPoolSize;
        private final int activeCount;
        private final long taskCount;
        private final long completedTaskCount;
        private final int queueSize;

        public ThreadPoolStatus(int corePoolSize, int maximumPoolSize, int activeCount,
                long taskCount, long completedTaskCount, int queueSize) {
            this.corePoolSize = corePoolSize;
            this.maximumPoolSize = maximumPoolSize;
            this.activeCount = activeCount;
            this.taskCount = taskCount;
            this.completedTaskCount = completedTaskCount;
            this.queueSize = queueSize;
        }

        // Getters
        public int getCorePoolSize() {
            return corePoolSize;
        }

        public int getMaximumPoolSize() {
            return maximumPoolSize;
        }

        public int getActiveCount() {
            return activeCount;
        }

        public long getTaskCount() {
            return taskCount;
        }

        public long getCompletedTaskCount() {
            return completedTaskCount;
        }

        public int getQueueSize() {
            return queueSize;
        }

        @Override
        public String toString() {
            return String.format("ThreadPoolStatus{core=%d, max=%d, active=%d, tasks=%d, completed=%d, queue=%d}",
                    corePoolSize, maximumPoolSize, activeCount, taskCount, completedTaskCount, queueSize);
        }
    }
}